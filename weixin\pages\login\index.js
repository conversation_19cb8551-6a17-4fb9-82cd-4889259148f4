Page({
  data: {
    phone: '',
    password: '',
    isRemember: false
  },

  onLoad() {
    // 检查是否有保存的登录信息
    const savedPhone = wx.getStorageSync('savedPhone');
    const savedPassword = wx.getStorageSync('savedPassword');
    
    if (savedPhone && savedPassword) {
      this.setData({
        phone: savedPhone,
        password: savedPassword,
        isRemember: true
      });
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换记住密码
  toggleRemember() {
    this.setData({
      isRemember: !this.data.isRemember
    });
  },

  // 手机号登录
  handleLogin() {
    const { phone, password, isRemember } = this.data;

    if (!phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '登录中...'
    });

    wx.request({
      url: 'http://127.0.0.1:8889/api/weixin/login',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        phone,
        password
      },
      success: (res) => {
        console.log('登录完整响应:', res);
        if (res.statusCode === 200 && res.data.code === 200) {
          // 保存token和用户信息
          wx.setStorageSync('token', res.data.token);
          wx.setStorageSync('userInfo', res.data.userInfo);
          if (isRemember) {
             wx.setStorageSync('rememberedPhone', phone);
          }
          wx.showToast({
                title: '登录成功',
                icon: 'success',
                duration: 1500,
                success: () => {
                    setTimeout(() => {
                        // 检查用户是否已绑定户号
                        const userInfo = res.data.userInfo;
                        if (userInfo && userInfo.houseId && userInfo.houseId > 0) 
                        {
                            wx.reLaunch({
                            url: '/pages/index/index'
                            });
                        } else {
                            // 未绑定户号，跳转到绑定页面
                            wx.reLaunch({
                                url: '/pages/bind/index'
                            });
                        }
               }, 1500);
            }
        });
        } else {
          wx.showToast({
            title: res.data?.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('登录请求失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/index'
    });
  },

  // 微信授权登录 - 保留功能但暂不使用
  handleWxLogin() {
    wx.showToast({
      title: '微信登录功能暂未开放',
      icon: 'none'
    });
  },

  onForgetPwd() {
    wx.showToast({
      title: '请联系管理员重置密码',
      icon: 'none'
    });
  },

  viewUserAgreement() {
    // TODO: 跳转到用户协议页面
  },

  viewPrivacyPolicy() {
    // TODO: 跳转到隐私政策页面
  }
}); 
