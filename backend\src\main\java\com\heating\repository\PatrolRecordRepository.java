package com.heating.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.patrol.TPatrolRecord;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface PatrolRecordRepository extends JpaRepository<TPatrolRecord, Long> {

    /**
     * 统计指定状态的巡检记录数量
     * @param status 巡检状态
     * @return 记录数量
     */
    long countByStatus(String status);
    
    /**
     * 查询指定状态的巡检记录列表
     * @param status 巡检状态
     * @return 巡检记录列表
     */
    List<TPatrolRecord> findByStatus(String status);
    
    /**
     * 查询指定状态列表中的巡检记录列表
     * @param statuses 巡检状态列表
     * @return 巡检记录列表
     */
    List<TPatrolRecord> findByStatusIn(List<String> statuses);
    
    /**
     * 查询指定状态列表和执行人ID的巡检记录列表
     * @param statuses 巡检状态列表
     * @param executorId 执行人ID
     * @return 巡检记录列表
     */
    List<TPatrolRecord> findByStatusInAndExecutorId(List<String> statuses, Long executorId);
    
    /**
     * 根据条件查询巡检记录
     * @param status 状态
     * @param executorId 执行人ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 巡检记录分页列表
     */
    @Query("SELECT r FROM TPatrolRecord r WHERE " +
           "(:status IS NULL OR r.status = :status) AND " +
           "(:executorId IS NULL OR r.executorId = :executorId) AND " +
           "(:startDate IS NULL OR r.executionDate >= :startDate) AND " +
           "(:endDate IS NULL OR r.executionDate <= :endDate)")
    Page<TPatrolRecord> findByConditions(
        @Param("status") String status,
        @Param("executorId") Long executorId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate,
        Pageable pageable
    );

    @Query(value = "SELECT count(*) FROM TPatrolRecord r WHERE " +
            "(:status IS NULL OR r.status = :status) AND " +
            "(:executorId IS NULL OR r.executorId = :executorId) AND " +
            "(:startDate IS NULL OR r.executionDate >= :startDate) AND " +
            "(:endDate IS NULL OR r.executionDate <= :endDate)")
    long countPatrolRecords(
            @Param("status") String status,
            @Param("executorId") Long executorId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * 查找指定巡检计划的最新执行记录
     * @param patrolPlanId 巡检计划ID
     * @return 最新的巡检记录
     */
    Optional<TPatrolRecord> findTopByPatrolPlanIdOrderByExecutionDateDesc(Long patrolPlanId);
    
    /**
     * 检查指定巡检计划在指定日期是否已有执行记录
     * @param patrolPlanId 巡检计划ID
     * @param executionDate 执行日期
     * @return 是否存在记录
     */
    boolean existsByPatrolPlanIdAndExecutionDate(Long patrolPlanId, LocalDate executionDate);
} 