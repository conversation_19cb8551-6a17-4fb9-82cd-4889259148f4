# 微信小程序查看账单界面优化

## 修改概述

按照要求对微信小程序查看账单界面进行了全面优化，主要包括：
1. 调整后端接口，增加历史欠费计算
2. 优化前端显示，增加详细的费用信息展示
3. 添加详细的中文注释

## 后端修改详情

### 1. 数据结构优化 (`BillDetailViewResponse.BillInfo`)

#### 新增字段
```java
/**
 * 供热费用（元）- 本年度供暖费用
 */
private BigDecimal heatingFee;

/**
 * 历史欠费金额（元）- 历史年度未缴清的费用
 */
private BigDecimal historicalDebt;

/**
 * 应缴费金额（元）- 供热费用 + 历史欠费金额
 */
private BigDecimal totalPayableAmount;

/**
 * 实际缴费金额（元）- 用户实际已缴纳的金额
 */
private BigDecimal actualPaidAmount;
```

#### 保留原有字段
- `totalAmount` - 保持兼容性
- `paidAmount` - 保持兼容性
- `remainingAmount` - 重新计算逻辑

### 2. 核心业务逻辑优化

#### 历史欠费计算 (`calculateHistoricalDebt`)
```java
/**
 * 计算历史欠费金额
 * 查询该房屋在历史年度（当前年度之前）未缴清的费用总和
 * @param houseId 房屋ID
 * @param currentHeatingYear 当前供暖年度
 * @return 历史欠费金额
 */
private BigDecimal calculateHistoricalDebt(Long houseId, Integer currentHeatingYear)
```

**计算逻辑：**
1. 查询该房屋在当前年度之前的所有账单
2. 计算每个历史账单的欠费金额 = 应缴金额 - 已缴金额
3. 只统计欠费金额大于0的账单
4. 返回历史欠费总和

#### 账单信息构建优化 (`buildBillInfo`)
```java
/**
 * 构建账单信息
 * 根据t_bill表数据，计算供热费用、历史欠费金额和应缴费金额
 * @param bill 账单实体
 * @return 账单信息
 */
private BillDetailViewResponse.BillInfo buildBillInfo(TBill bill)
```

**构建逻辑：**
1. **供热费用** = `bill.getTotalAmount()` (本年度供暖费用)
2. **历史欠费金额** = `calculateHistoricalDebt()` (历史年度未缴清费用)
3. **应缴费金额** = 供热费用 + 历史欠费金额
4. **实际缴费金额** = `bill.getPaidAmount()` (用户实际已缴纳金额)
5. **剩余未缴金额** = 应缴费金额 - 实际缴费金额

### 3. 数据库查询优化

#### 新增查询方法 (`TBillRepository`)
```java
/**
 * 根据房屋ID查询历史年度账单（当前年度之前的账单）
 * 用于计算历史欠费金额
 */
@Query("SELECT b FROM TBill b WHERE b.houseId = :houseId AND b.heatYear < :currentHeatingYear ORDER BY b.heatYear DESC")
List<TBill> findByHouseIdAndHeatYearLessThanOrderByHeatYearDesc(@Param("houseId") Long houseId, @Param("currentHeatingYear") Integer currentHeatingYear);
```

### 4. 四种场景的优化

#### 场景1：正常供暖
- 使用统一的 `buildBillInfo()` 方法
- 包含滞纳金计算和详细费用说明
- 显示完整的费用明细

#### 场景2：供暖前停供
- 基于统一的费用计算
- 添加30%折扣说明
- 包含历史欠费信息

#### 场景3：供暖后停供（未缴费）
- 计算结算金额
- 显示详细的费用分解
- 包含历史欠费处理

#### 场景4：供暖后停供（已缴费）
- 计算退费金额
- 基于实际缴费金额计算
- 显示完整的费用和退费明细

## 前端修改详情

### 1. 界面布局优化 (`bill-view.wxml`)

#### 新的显示结构
```xml
<!-- 供热费用 -->
<view class="amount-row">
  <text class="amount-label">供热费用：</text>
  <text class="amount-value">¥{{billInfo.heatingFee || billInfo.totalAmount}}</text>
</view>

<!-- 历史欠费金额 -->
<view class="amount-row" wx:if="{{billInfo.historicalDebt && billInfo.historicalDebt > 0}}">
  <text class="amount-label">历史欠费：</text>
  <text class="amount-value debt">¥{{billInfo.historicalDebt}}</text>
</view>

<!-- 应缴费金额 -->
<view class="amount-row main-amount">
  <text class="amount-label">应缴费金额：</text>
  <text class="amount-value">¥{{billInfo.totalPayableAmount || billInfo.totalAmount}}</text>
</view>

<!-- 实际缴费金额 -->
<view class="amount-row">
  <text class="amount-label">实际缴费金额：</text>
  <text class="amount-value paid">¥{{billInfo.actualPaidAmount || billInfo.paidAmount}}</text>
</view>

<!-- 剩余金额 -->
<view class="amount-row" wx:if="{{billInfo.remainingAmount > 0}}">
  <text class="amount-label">剩余金额：</text>
  <text class="amount-value remaining">¥{{billInfo.remainingAmount}}</text>
</view>
```

### 2. 样式优化 (`bill-view.wxss`)

#### 新增样式
```css
/* 历史欠费样式 */
.amount-value.debt {
  color: #ff4d4f;
  font-weight: 600;
}

/* 供热费用样式 */
.amount-row:first-child .amount-label {
  color: #1890ff;
  font-weight: 500;
}

.amount-row:first-child .amount-value {
  color: #1890ff;
  font-weight: 600;
}
```

## 显示效果

### 正常情况显示
```
供热费用：¥2,500.00
历史欠费：¥500.00 (如果有)
应缴费金额：¥3,000.00
实际缴费金额：¥1,000.00
剩余金额：¥2,000.00
```

### 兼容性处理
- 如果新字段不存在，自动回退到原有字段显示
- 历史欠费为0时不显示该行
- 保持原有的状态显示和操作按钮

## 测试建议

### 1. 数据准备
- 创建有历史欠费的测试账单
- 创建无历史欠费的测试账单
- 创建不同状态的停供申请

### 2. 测试用例
1. **正常账单测试**：验证费用计算和显示
2. **历史欠费测试**：验证历史欠费计算准确性
3. **停供场景测试**：验证四种停供场景的费用显示
4. **兼容性测试**：验证新老数据的兼容性

### 3. 验证要点
- 金额计算准确性
- 界面显示完整性
- 数据兼容性
- 性能影响评估

## 部署注意事项

1. 数据库查询性能监控
2. 历史数据兼容性验证
3. 前端缓存清理
4. 用户界面适配测试
