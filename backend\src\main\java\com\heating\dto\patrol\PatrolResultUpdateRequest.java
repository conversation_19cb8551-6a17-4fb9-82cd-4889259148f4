package com.heating.dto.patrol;
 
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data; 
import java.util.List;

@Data
public class PatrolResultUpdateRequest {

    @JsonProperty("check_result")
    private String checkResult;

    @JsonProperty("param_value")
    private String paramValue;

    private String description;
    private List<String> images;
    private Double latitude;
    private Double longitude;
}