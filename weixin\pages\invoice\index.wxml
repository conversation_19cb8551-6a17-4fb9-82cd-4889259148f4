<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">缴费票据</text>
  </view>

  <!-- 票据列表 -->
  <view class="invoice-list" wx:if="{{invoiceList.length > 0}}">
    <view class="invoice-card" wx:for="{{invoiceList}}" wx:key="id">
      <view class="card-header">
        <view class="invoice-icon">📄</view>
        <text class="invoice-title">缴费票据</text>
      </view>
      
      <view class="invoice-info">
        <view class="info-item">
          <text class="info-icon">💬</text>
          <text class="info-text">供暖周期：{{item.period}}</text>
        </view>
        <view class="info-item">
          <text class="info-icon">💬</text>
          <text class="info-text">金额：¥{{item.amount}}</text>
        </view>
        <view class="info-item">
          <text class="info-icon">📅</text>
          <text class="info-text">生成时间：{{item.createTime}}</text>
        </view>
        <view class="info-item">
          <text class="info-icon">🧾</text>
          <text class="info-text">票据编号：{{item.invoiceNo}}</text>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="action-buttons">
        <button class="action-btn preview-btn" bindtap="previewInvoice" data-id="{{item.id}}">
          预览
        </button>
        <button class="action-btn download-btn" bindtap="downloadPDF" data-id="{{item.id}}">
          下载PDF
        </button>
        <button class="action-btn share-btn" bindtap="shareInvoice" data-id="{{item.id}}">
          分享
        </button>
      </view>
    </view>
  </view>

  <!-- 无票据提示 -->
  <view class="empty-state" wx:if="{{invoiceList.length === 0}}">
    <view class="empty-icon">📄</view>
    <text class="empty-title">您尚未生成任何缴费票据</text>
    <text class="empty-desc">完成缴费后可自动生成票据</text>
    <button class="generate-btn" bindtap="goToBill">去生成</button>
  </view>
</view>