package com.heating.entity.hes;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "t_hes")
@Data
public class THes {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "heat_unit_id")
    private Integer heatUnitId;

    @Column(name = "heat_unit_name")
    private String heatUnitName;

    @Column(name = "hes_code")
    private Integer hesCode;

    @Column(name = "used_year")
    private Integer usedYear;

    @Column(name = "equipment_num")
    private Integer equipmentNum;

    @Column(name = "run_mode")
    private Integer runMode;

    @Column(name = "is_used")
    private Byte isUsed;

    @Column(name = "heating_type")
    private Integer heatingType;

    @Column(name = "heating_area")
    private Float heatingArea;

    @Column(name = "is_run")
    private Byte isRun;

    @Column(name = "disconnect_dt")
    private String disconnectDt;

    @Column(name = "heat_rate")
    private Double heatRate;

    @Column(name = "calc_mode")
    private Integer calcMode;

    @Column(name = "target_t")
    private Integer targetT;

    @Column(name = "control_ip")
    private String controlIp;

    @Column(name = "control_port")
    private String controlPort;

    @Column(name = "design_load")
    private Float designLoad;

    @Column(name = "design_flow")
    private Float designFlow;

    @Column(name = "g_rate", columnDefinition = "DECIMAL(10,2)")
    private java.math.BigDecimal gRate;

    @Column(name = "is_heating")
    private Integer isHeating;

    @Column(name = "flow_rate")
    private Float flowRate;

    @Column(name = "status")
    private Integer status;

    @Column(name = "heating_index")
    private String heatingIndex;

    @Column(name = "alarm_count")
    private Integer alarmCount;

    @Column(name = "remark")
    private String remark;
}