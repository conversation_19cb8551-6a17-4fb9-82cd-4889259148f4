package com.heating.service.impl;

import com.heating.dto.ManagerHeatUnitDto;
import com.heating.entity.TManagerHeatUnit;
import com.heating.repository.ManagerHeatUnitRepository;
import com.heating.service.ManagerHeatUnitService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理人员与热用户关联服务实现类
 */
@Service
public class ManagerHeatUnitServiceImpl implements ManagerHeatUnitService {

    @Autowired
    private ManagerHeatUnitRepository managerHeatUnitRepository;

    @Override
    @Transactional
    public Long save(ManagerHeatUnitDto dto) {
        TManagerHeatUnit entity = new TManagerHeatUnit();
        BeanUtils.copyProperties(dto, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        return managerHeatUnitRepository.save(entity).getId();
    }

    @Override
    @Transactional
    public ManagerHeatUnitDto update(Long id, ManagerHeatUnitDto dto) {
        TManagerHeatUnit entity = managerHeatUnitRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("未找到ID为" + id + "的管理人员热用户关联记录"));
        
        BeanUtils.copyProperties(dto, entity, "id", "createTime");
        entity.setUpdateTime(LocalDateTime.now());
        
        TManagerHeatUnit updated = managerHeatUnitRepository.save(entity);
        ManagerHeatUnitDto result = new ManagerHeatUnitDto();
        BeanUtils.copyProperties(updated, result);
        return result;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        managerHeatUnitRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("未找到ID为" + id + "的管理人员热用户关联记录"));
        
        managerHeatUnitRepository.deleteById(id);
    }

    @Override
    public ManagerHeatUnitDto getById(Long id) {
        TManagerHeatUnit entity = managerHeatUnitRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("未找到ID为" + id + "的管理人员热用户关联记录"));
        
        ManagerHeatUnitDto dto = new ManagerHeatUnitDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public List<ManagerHeatUnitDto> getByManagerId(Long managerId) {
        List<TManagerHeatUnit> entities = managerHeatUnitRepository.findByManagerId(managerId);
        return entities.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ManagerHeatUnitDto> getByHeatUnitId(Long heatUnitId) {
        List<TManagerHeatUnit> entities = managerHeatUnitRepository.findByHeatUnitId(heatUnitId);
        return entities.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * 将实体转换为DTO
     */
    private ManagerHeatUnitDto convertToDto(TManagerHeatUnit entity) {
        ManagerHeatUnitDto dto = new ManagerHeatUnitDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
} 