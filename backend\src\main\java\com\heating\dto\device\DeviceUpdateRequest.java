package com.heating.dto.device;

import lombok.Data;
import java.util.List;

@Data
public class DeviceUpdateRequest {
    private String name;
    private String type;
    private String model;
    private String manufacturer;
    private LocationInfo location;
    private SpecsInfo specs;
    private MaintenanceInfo maintenance;
    private List<String> photos;
    
    @Data
    public static class LocationInfo {
        private String building;
        private String floor;
        private String room;
        private Coordinates coordinates;
    }
    
    @Data
    public static class Coordinates {
        private Double lat;
        private Double lng;
    }
    
    @Data
    public static class SpecsInfo {
        private Double power;
        private Temperature temperature;
        private String protocol;
    }
    
    @Data
    public static class Temperature {
        private Integer min;
        private Integer max;
    }
    
    @Data
    public static class MaintenanceInfo {
        private Integer period;
        private List<String> staff;
        private String last_maintenance;
        private String next_maintenance;    
        
    }
} 