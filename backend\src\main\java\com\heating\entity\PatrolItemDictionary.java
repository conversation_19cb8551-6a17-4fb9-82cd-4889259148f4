package com.heating.entity;

import lombok.Data;
import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 巡检项目字典实体类
 */
@Data
@Entity
@Table(name = "t_patrol_item_dictionary")
public class PatrolItemDictionary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "item_code", nullable = false, length = 32)
    private String itemCode;
    
    @Column(name = "item_name", nullable = false, length = 100)
    private String itemName;
    
    @Column(name = "category_id")
    private Long categoryId;
    
    @Column(name = "category_name", length = 100)
    private String categoryName;
    
    @Column(name = "param_type", length = 20)
    private String paramType;
    
    @Column(name = "unit", length = 20)
    private String unit; 
    
    /**
     * 正常范围
     * 这个字段实际上存储在t_device_patrol_item表中
     * 在这里添加该字段用于关联查询
     */
    @Transient
    private String normalRange;
    
    @Column(name = "check_method", length = 255)
    private String checkMethod;
    
    @Column(name = "importance", length = 10)
    private String importance;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "is_active")
    private Boolean isActive;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 