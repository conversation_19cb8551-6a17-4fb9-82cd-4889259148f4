const { stopSupplyApi } = require('../../api/index.js');

Page({
  data: {
    houseInfo: {
      id: null,
      address: '',
      houseNumber: ''
    },
    formData: {
      stopStartDate: '',
      stopEndDate: '',
      reason: ''
    },
    minDate: '',
    canSubmit: false
  },

  onLoad(options) {
    this.initData();
    this.loadUserInfo();
  },

  initData() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    this.setData({
      minDate: `${year}-${month}-${day}`
    });
  },

  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.houseId) {
      this.setData({
        'houseInfo.id': userInfo.houseId,
        'houseInfo.address': userInfo.address || '暂无地址信息',
        'houseInfo.houseNumber': userInfo.houseNumber || userInfo.id || '暂无户号'
      });
    } else {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onStartDateChange(e) {
    const startDate = e.detail.value;
    this.setData({
      'formData.stopStartDate': startDate
    });
    
    // 如果结束日期早于开始日期，清空结束日期
    if (this.data.formData.stopEndDate && this.data.formData.stopEndDate < startDate) {
      this.setData({
        'formData.stopEndDate': ''
      });
    }
    
    this.validateForm();
  },

  onEndDateChange(e) {
    this.setData({
      'formData.stopEndDate': e.detail.value
    });
    this.validateForm();
  },

  onReasonInput(e) {
    this.setData({
      'formData.reason': e.detail.value
    });
    this.validateForm();
  },

  validateForm() {
    const { stopStartDate, reason } = this.data.formData;
    const canSubmit = stopStartDate && reason.trim().length > 0;
    this.setData({ canSubmit });
  },

  submitApply() {
    if (!this.data.canSubmit) return;

    const { formData, houseInfo } = this.data;
    
    if (!houseInfo.id) {
      wx.showToast({
        title: '房屋信息异常',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    const requestData = {
      houseId: houseInfo.id,
      stopStartDate: formData.stopStartDate,
      stopEndDate: formData.stopEndDate || null,
      reason: formData.reason.trim()
    };

    console.log('提交停供申请:', requestData);

    stopSupplyApi.submitApply(requestData).then(res => {
      wx.hideLoading();
      console.log('停供申请响应:', res);

      if (res.code === 200) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success',
          duration: 2000
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('停供申请失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  goToRecords() {
    wx.navigateTo({
      url: '/pages/stop-supply/records'
    });
  }
});

