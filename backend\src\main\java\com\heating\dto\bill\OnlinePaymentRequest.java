package com.heating.dto.bill;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 在线缴费请求DTO
 */
@Data
@ToString
public class OnlinePaymentRequest {
    
    /**
     * 账单ID
     */
    private Long billId;
    
    /**
     * 房屋ID
     */
    private Long houseId;
    
    /**
     * 缴费金额
     */
    private BigDecimal amount;
    
    /**
     * 支付方式 (wechat, alipay)
     */
    private String paymentMethod;
    
    /**
     * 第三方支付交易号
     */
    private String transactionNo;
    /**
     * 用热状态（必填）
     * 1: 用热
     * 0: 不用热
     */
    private Integer isHeating;

    /**
     * 缴费类型（必填）
     * heating: 用热缴费(全额)
     * maintenance: 不用热(管网维护费)
     */
    private String feeType;

    /**
     * 备注
     */
    private String remark;
    
    /**
     * 用户ID（从token中获取）
     */
    private Long userId;
}
