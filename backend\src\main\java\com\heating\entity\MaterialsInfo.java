package com.heating.entity;

import lombok.Data;
import jakarta.persistence.*;

/**
 * 材料信息实体类
 */
@Data
@Entity
@Table(name = "t_materials_info")
public class MaterialsInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 材料名称
     */
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 规格型号
     */
    @Column(name = "specification")
    private String specification;

    /**
     * 材质
     */
    @Column(name = "material_type")
    private String materialType;

    /**
     * 材料类别
     */
    @Column(name = "category")
    private String category;

    /**
     * 用途
     */
    @Column(name = "usaged")
    private String usaged;

    /**
     * 备注
     */
    @Column(name = "mark")
    private String mark;
} 