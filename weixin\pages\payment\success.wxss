.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 60rpx 30rpx;
}

.success-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: #52c41a;
  border-radius: 50%;
  color: #fff;
  font-size: 60rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.success-amount {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b35;
  margin-bottom: 20rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.order-info {
  border-top: 1rpx solid #eee;
  padding-top: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-secondary, .btn-primary {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
}