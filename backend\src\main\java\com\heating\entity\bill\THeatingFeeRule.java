package com.heating.entity.bill;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_heating_fee_rule")
public class THeatingFeeRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "unit_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal unitPrice;

    @Column(name = "heating_start_date", nullable = false)
    private LocalDate heatingStartDate;

    @Column(name = "heating_end_date", nullable = false)
    private LocalDate heatingEndDate;

    @Column(name = "min_payment_rate", precision = 5, scale = 2)
    private BigDecimal minPaymentRate = new BigDecimal("0.30");

    @Column(name = "penalty_rate", precision = 5, scale = 4)
    private BigDecimal penaltyRate = new BigDecimal("0.0005");

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}