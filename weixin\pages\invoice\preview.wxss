.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 140rpx;
}

/* 票据内容 */
.invoice-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 票据头部 */
.invoice-header {
  position: relative;
  text-align: center;
  padding: 30rpx 0 40rpx;
  border-bottom: 2rpx solid #333;
  margin-bottom: 30rpx;
}

.company-name {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
  letter-spacing: 2rpx;
}

.company-name-en {
  display: block;
  font-size: 24rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 电子章样式 */
.seal-image {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  z-index: 10;
}

/* 顶部电子章 */
.seal-top {
  top: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0.8;
}

/* 基本信息 */
.invoice-basic {
  margin-bottom: 30rpx;
}

.basic-item {
  display: flex;
  margin-bottom: 15rpx;
}

.basic-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.basic-value {
  font-size: 28rpx;
  color: #333;
}

/* 分割线和标题 */
.section-divider {
  text-align: center;
  margin: 40rpx 0 30rpx;
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: #ddd;
}

.section-title {
  background: #fff;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  letter-spacing: 4rpx;
}

/* 缴费详情 */
.payment-details {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 15rpx;
  position: relative;
}

.detail-item.payment-item {
  position: relative;
}

/* 右下角电子章 */
.seal-bottom {
  bottom: -10rpx;
  right: 0rpx;
  opacity: 0.9;
}

.detail-label {
  font-size: 28rpx;
  color: #333;
  min-width: 200rpx;
  line-height: 1.5;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.detail-value.amount {
  font-weight: 700;
  color: #ff6b35;
  font-size: 32rpx;
}

/* 确保最后一项的文字不被电子章遮挡 */
.payment-item .detail-value {
  padding-right: 140rpx;
}

/* 附注说明 */
.notes-section {
  margin-bottom: 40rpx;
}

.notes-content {
  padding-left: 20rpx;
}

.note-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 二维码区域 */
.qr-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.qr-placeholder {
  display: inline-block;
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed #ccc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}

.qr-text {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.qr-desc {
  font-size: 20rpx;
  color: #999;
}

/* 票据底部 */
.invoice-footer {
  text-align: center;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.footer-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.footer-subtitle {
  display: block;
  font-size: 22rpx;
  color: #999;
}

/* 操作按钮 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.04);
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.download-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3);
}

.share-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
}
