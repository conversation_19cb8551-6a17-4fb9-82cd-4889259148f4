package com.heating.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
  private int code;    // 200 or 500
  private String message;   // success or error message
  private T data;           // return object from service class, if successful
  
  public static <T> ApiResponse<T> success(String message, T data) {
    return new ApiResponse<>(200, message, data);
  }
  
  public static <T> ApiResponse<T> error(String message) {
    return new ApiResponse<>(500, message, null);
  }
} 