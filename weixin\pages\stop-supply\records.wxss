.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx 40rpx;
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  display: block;
}

.stats-card {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.record-list {
  height: calc(100vh - 300rpx);
  padding-bottom: 40rpx;
}

.record-item {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-dot.pending {
  background-color: #ff9500;
}

.status-dot.approved {
  background-color: #34c759;
}

.status-dot.rejected {
  background-color: #ff3b30;
}

.status-dot.canceled {
  background-color: #8e8e93;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
}

.status-text.pending {
  color: #ff9500;
}

.status-text.approved {
  color: #34c759;
}

.status-text.rejected {
  color: #ff3b30;
}

.status-text.canceled {
  color: #8e8e93;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  margin-bottom: 20rpx;
}

.content-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.content-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.value.reason {
  line-height: 1.4;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.heating-year {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  color: #ccc;
  font-size: 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 60rpx;
  line-height: 1.4;
}

.empty-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 60rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}