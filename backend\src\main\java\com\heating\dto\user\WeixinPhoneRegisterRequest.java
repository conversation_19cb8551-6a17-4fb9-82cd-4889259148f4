package com.heating.dto.user;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class WeixinPhoneRegisterRequest {

    @NotBlank(message = "微信授权码不能为空")
    private String code;

    @NotBlank(message = "姓名不能为空")
    private String name;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    private String nickName;

    private String avatar;

    private Integer gender;
}