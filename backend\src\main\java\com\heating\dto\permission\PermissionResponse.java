package com.heating.dto.permission;

/**
 * 系统权限响应数据传输对象
 */
public record PermissionResponse(
    Long id,
    String permissionCode,
    String permissionName,
    String menuName,
    String path
) {
    /**
     * 紧凑型规范构造函数
     * 用于验证输入参数
     */
    public PermissionResponse {
        // 验证参数，如果不符合要求则抛出异常
        if (id == null) {
            throw new IllegalArgumentException("权限ID不能为空");
        }
    }
} 