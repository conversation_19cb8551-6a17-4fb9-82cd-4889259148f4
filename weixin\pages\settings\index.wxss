.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.settings-list {
  margin-bottom: 40rpx;
}

.settings-group {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.settings-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #333;
}

.value {
  font-size: 28rpx;
  color: #999;
}

switch {
  transform: scale(0.8);
} 