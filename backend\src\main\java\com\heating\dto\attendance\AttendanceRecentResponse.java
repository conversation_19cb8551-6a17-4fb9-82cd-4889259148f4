package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceRecentResponse {
    private List<DailyRecord> records;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyRecord {
        private String date; // 日期，例如：2023-12-05
        private String week; // 星期，例如：周一
        private String clockInTime; // 上班打卡时间
        private String clockInStatus; // 上班打卡状态
        private String clockOutTime; // 下班打卡时间
        private String clockOutStatus; // 下班打卡状态
    }
} 