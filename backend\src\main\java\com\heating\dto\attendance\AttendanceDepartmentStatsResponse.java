package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceDepartmentStatsResponse {
    private List<DepartmentStats> departments;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepartmentStats {
        private Long departmentId;
        private String departmentName;
        private Integer staffCount;
        private Double attendanceRate;
        private Double lateRate;
        private Double earlyLeaveRate;
        private Double absentRate;
    }
} 