# API 接口使用说明

## 引入方式
```javascript
const { billApi, userApi, paymentApi } = require('../../api/index.js');
```

## 账单相关接口

### 获取账单列表
```javascript
billApi.getBillList({
  userId: 123,
  status: 'unpaid', // unpaid, paid, all
  page: 1,
  pageSize: 10
}).then(res => {
  console.log(res.bills);
}).catch(err => {
  console.error(err);
});
```

### 获取账单详情
```javascript
billApi.getBillDetail(billId).then(res => {
  console.log(res);
}).catch(err => {
  console.error(err);
});
```

## 用户相关接口

### 用户登录
```javascript
userApi.login({
  username: 'user',
  password: 'password'
}).then(res => {
  wx.setStorageSync('token', res.token);
  wx.setStorageSync('userInfo', res.userInfo);
}).catch(err => {
  console.error(err);
});
```

## 文件上传

### 上传图片
```javascript
uploadApi.uploadFile(filePath, 'image', 'repair').then(res => {
  console.log('上传成功:', res.url);
}).catch(err => {
  console.error('上传失败:', err);
});
```

## 错误处理
所有接口都会返回Promise，建议统一使用try-catch或.catch()进行错误处理。