package com.heating.repository;

import com.heating.entity.House;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.List;

@Repository
public interface HouseRepository extends JpaRepository<House, Long> {

    /**
     * 根据户号查询房屋信息
     */
    Optional<House> findByHouseNumber(String houseNumber);

    /**
     * 根据热用户ID查询房屋列表
     */
    List<House> findByHeatUnitId(Long heatUnitId);

    /**
     * 根据热用户编号查询房屋列表
     */
    List<House> findByHeatUnitNo(String heatUnitNo);

    /**
     * 根据房屋主人电话查询房屋信息
     */
    List<House> findByHouseMasterTel(String houseMasterTel);

    /**
     * 根据房屋主人姓名查询房屋信息
     */
    List<House> findByHouseMaster(String houseMaster);

    /**
     * 根据是否供暖状态查询房屋列表
     */
    List<House> findByIsHeating(Integer isHeating);

    /**
     * 根据是否缴费状态查询房屋列表
     */
    List<House> findByIsPay(Integer isPay);

    /**
     * 根据阀门状态查询房屋列表
     */
    List<House> findByValveStatus(Integer valveStatus);
}