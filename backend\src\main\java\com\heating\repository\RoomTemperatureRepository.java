package com.heating.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.temperature.TRoomTemperature;

import java.sql.Date;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface RoomTemperatureRepository extends JpaRepository<TRoomTemperature, Long> {

    @Query("SELECT AVG(r.indoorTemp) FROM TRoomTemperature r WHERE DATE(r.reportTime) = CURRENT_DATE")
    Double findAverageTemperatureToday();

    @Query("SELECT new map(r.heatUnitName as name, AVG(r.indoorTemp) as temperature, MAX(r.reportTime) as latest_time) " +
           "FROM TRoomTemperature r " +
           "WHERE DATE(r.reportTime) = CURRENT_DATE " +
           "GROUP BY r.heatUnitName " +
           "ORDER BY r.heatUnitName")
    List<Map<String, Object>> findCommunityTemperatures();

    @Query("SELECT rt FROM TRoomTemperature rt WHERE rt.reportTime >= :startTime ORDER BY rt.reportTime DESC")
    List<TRoomTemperature> findRecentTemperatures(@Param("startTime") LocalDateTime startTime);

    @Query("SELECT rt FROM TRoomTemperature rt WHERE rt.reportTime >= :startTime ORDER BY rt.reportTime DESC")
    List<TRoomTemperature> findTopNRecentTemperatures(@Param("startTime") LocalDateTime startTime, Pageable pageable);
 
    @Query("SELECT rt FROM TRoomTemperature rt WHERE rt.id = :id")
    Optional<TRoomTemperature> findById(@Param("id") long id);
 
    @Query("SELECT rt FROM TRoomTemperature rt WHERE (:heatUnitName IS NULL OR rt.heatUnitName = :heatUnitName) AND (:date IS NULL OR DATE(rt.reportTime) = :date)")
    List<TRoomTemperature> findByHeatUnitNameAndDate(@Param("heatUnitName") String heatUnitName, @Param("date") Date date);

    @Query("SELECT rt FROM TRoomTemperature rt")
    List<TRoomTemperature> findAll();

}