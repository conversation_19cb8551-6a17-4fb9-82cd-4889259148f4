package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PaymentListResponse {
    private PaymentSummary summary;
    private List<PaymentDetail> payments;
    private Integer total;
    private Integer page;
    private Integer pageSize;

    @Data
    public static class PaymentSummary {
        private BigDecimal totalAmount = BigDecimal.ZERO;
        private Integer totalCount = 0;
    }

    @Data
    public static class PaymentDetail {
        private Long id;
        private Long billId;
        private String roomNo;
        private String heatYear;
        private String paymentMethod;
        private String paymentMethodText;
        private String amount;
        private String transactionNo;
        private String paymentDate;
        private String remark;
        private String period; // 供暖期间
    }
}