package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.device.TDeviceRealTimeData;

import java.util.Optional;

@Repository
public interface DeviceRealTimeDataRepository extends JpaRepository<TDeviceRealTimeData, Long> {
    @Query("SELECT d FROM TDeviceRealTimeData d WHERE d.deviceId = :deviceId ORDER BY d.collectTime DESC LIMIT 1")
    Optional<TDeviceRealTimeData> findLatestByDeviceId(@Param("deviceId") Long deviceId);
} 