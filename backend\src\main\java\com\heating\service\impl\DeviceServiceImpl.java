package com.heating.service.impl;

import com.heating.dto.device.DeviceCreateRequest;
import com.heating.dto.device.DeviceCreateResponse;
import com.heating.dto.device.DeviceDetailResponse;
import com.heating.dto.device.DeviceHeatUnitResponse;
import com.heating.dto.device.DeviceListRequest;
import com.heating.dto.device.DeviceListResponse;
import com.heating.dto.device.DeviceMaintenanceRecord;
import com.heating.dto.device.DevicePatrolItemResponse;
import com.heating.dto.device.DeviceStatsResponse;
import com.heating.dto.device.DeviceUpdateRequest;
import com.heating.entity.PatrolItemDictionary;
import com.heating.entity.TDevicePatrolItem;
import com.heating.repository.DeviceRepository;
import com.heating.repository.DeviceRealTimeDataRepository;
import com.heating.repository.PatrolItemDictionaryRepository;
import com.heating.repository.TDevicePatrolItemRepository;
import com.heating.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.format.DateTimeFormatter;
import java.math.BigDecimal;
import java.util.*;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.stream.Collectors;

import com.heating.repository.DeviceMaintenanceRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.heating.repository.UserRepository;
import com.heating.entity.device.TDevice;
import com.heating.entity.device.TDeviceMaintenance;
import com.heating.entity.device.TDeviceRealTimeData;
import com.heating.entity.user.TUser;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DeviceServiceImpl implements DeviceService {
    private static final Logger log = LoggerFactory.getLogger(DeviceServiceImpl.class);

    @Autowired
    private DeviceRepository deviceRepository;

    @Autowired
    private DeviceRealTimeDataRepository realTimeDataRepository;

    @Autowired
    private DeviceMaintenanceRepository maintenanceRepository;

    @Autowired
    private UserRepository userRepository;  
    
    @Autowired
    private TDevicePatrolItemRepository devicePatrolItemRepository;
    
    @Autowired
    private PatrolItemDictionaryRepository patrolItemDictionaryRepository;

    @Override
    public Page<DeviceListResponse> getDeviceList(DeviceListRequest request) {   
        try { 
                if (request.getPage() < 1) {
                    request.setPage(1);
                }
                String sortBy = request.getSortBy() != null && !request.getSortBy().isEmpty() ? request.getSortBy() : "createTime";
                String sortOrder = request.getSortOrder() != null && !request.getSortOrder().isEmpty() ? request.getSortOrder() : "DESC";
                // Create pageable with sorting
                Sort sort = Sort.by(Sort.Direction.fromString(sortOrder), sortBy);
                PageRequest pageRequest = PageRequest.of(request.getPage() - 1, request.getPageSize(), sort);

                // Convert status string to enum if provided 
                String statusString = request.getStatus() != null ? request.getStatus() : null;
                TDevice.DeviceStatus status = null;
                if (statusString != null && !statusString.isEmpty()) {
                    try {
                        // 转为小写，确保大小写不敏感
                        status = TDevice.DeviceStatus.valueOf(statusString.toLowerCase());
                    } catch (IllegalArgumentException e) {
                        log.error("获取设备列表失败: No enum constant com.heating.entity.device.TDevice.DeviceStatus.{}", statusString);
                        // 无效的状态，使用null作为过滤条件（不根据状态过滤）
                    }
                }
                String areaString = request.getArea() != null && !request.getArea().isEmpty() ? request.getArea() : null;
                String typeString = request.getType() != null && !request.getType().isEmpty() ? request.getType() : null;
                String keywordString = request.getKeyword() != null && !request.getKeyword().isEmpty() ? request.getKeyword() : null;  
                // Query devices with filters
                Page<TDevice> devices = deviceRepository.findDevices(
                    status,
                    areaString,
                    typeString,
                    keywordString,
                    pageRequest
                );

                return devices.map(this::convertToResponse);
        } catch (Exception e) {
            System.out.println("Error processing request: " + e);
            throw e;
        }
    }

    private DeviceListResponse convertToResponse(TDevice device) {
        DeviceListResponse response = new DeviceListResponse();
        response.setDeviceId(device.getId());
        response.setName(device.getName()); 
        response.setModel(device.getModel());
        response.setType(device.getType());
        response.setStatus(device.getStatus().name());
        
        // 安全处理可能为null的维护时间字段
        if (device.getLastMaintenance() != null) {
            response.setLastMaintenance(device.getLastMaintenance().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            response.setLastMaintenance("未维护");
        }
        
        if (device.getNextMaintenance() != null) {
            response.setNextMaintenance(device.getNextMaintenance().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            response.setNextMaintenance("未设置");
        }
        
        response.setAlarmCount(device.getAlarmCount()); 

        // Set location info
        DeviceListResponse.LocationInfo location = new DeviceListResponse.LocationInfo();
        location.setBuilding(device.getBuilding());
        location.setFloor(device.getFloor());
        location.setRoom(device.getRoom());

        DeviceListResponse.LocationInfo.Coordinates coordinates = new DeviceListResponse.LocationInfo.Coordinates();
        coordinates.setLat(device.getLatitude());
        coordinates.setLng(device.getLongitude());
        location.setCoordinates(coordinates);
        response.setLocation(location);

        // Get and set real-time data
        realTimeDataRepository.findLatestByDeviceId(device.getId())
            .ifPresent(data -> {
                DeviceListResponse.RealTimeData realTimeData = new DeviceListResponse.RealTimeData();
                realTimeData.setTemperature(data.getTemperature());
                realTimeData.setPressure(data.getPressure());
                realTimeData.setFlow(data.getFlow());
                response.setRealTimeData(realTimeData);
            });

        return response;
    }

    @Override
    public DeviceDetailResponse getDeviceDetail(Long deviceId) {
        TDevice device = deviceRepository.findById(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));

        DeviceDetailResponse response = new DeviceDetailResponse();
        
        // Set basic info
        DeviceDetailResponse.BasicInfo basicInfo = new DeviceDetailResponse.BasicInfo();
        basicInfo.setDeviceId(device.getId().toString());
        basicInfo.setName(device.getName());
        basicInfo.setModel(device.getModel());
        basicInfo.setType(device.getType());
        basicInfo.setStatus(device.getStatus().name());
//        basicInfo.setInstallTime(device.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        basicInfo.setManufacturer(device.getManufacturer());
        response.setBasicInfo(basicInfo);

        // Set location info
        DeviceDetailResponse.LocationInfo location = new DeviceDetailResponse.LocationInfo();
        location.setBuilding(device.getBuilding());
        location.setFloor(device.getFloor());
        location.setRoom(device.getRoom());
        
        DeviceDetailResponse.LocationInfo.Coordinates coordinates = new DeviceDetailResponse.LocationInfo.Coordinates();
        coordinates.setLat(device.getLatitude());
        coordinates.setLng(device.getLongitude());
        location.setCoordinates(coordinates);
        response.setLocation(location);

        // Set maintenance info
        DeviceDetailResponse.MaintenanceInfo maintenance = new DeviceDetailResponse.MaintenanceInfo();
        
        // 安全处理可能为null的维护时间字段
        if (device.getLastMaintenance() != null) {
            maintenance.setLastTime(device.getLastMaintenance().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            maintenance.setLastTime("未维护");
        }
        
        if (device.getNextMaintenance() != null) {
            maintenance.setNextTime(device.getNextMaintenance().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            // Calculate remaining days
            long remainingDays = ChronoUnit.DAYS.between(LocalDateTime.now(), device.getNextMaintenance());
            maintenance.setRemainingDays((int) remainingDays);
        } else {
            maintenance.setNextTime("未设置");
            maintenance.setRemainingDays(0);
        }
        
        maintenance.setPeriod(device.getPeriod());
        // Note: maintenance period would need to be added to Device entity
        response.setMaintenance(maintenance);

        // Set real-time data 
        realTimeDataRepository.findLatestByDeviceId(device.getId()).ifPresent(data -> {
            DeviceDetailResponse.RealTimeData realTimeData = new DeviceDetailResponse.RealTimeData();
            
            // Temperature 
            DeviceDetailResponse.RealTimeData.ParameterInfo temperature = new DeviceDetailResponse.RealTimeData.ParameterInfo();
            temperature.setValue(data.getTemperature());
            temperature.setUnit("℃");
            DeviceDetailResponse.RealTimeData.ParameterInfo.Range tempRange = new DeviceDetailResponse.RealTimeData.ParameterInfo.Range();
            tempRange.setMin(new BigDecimal("50"));
            tempRange.setMax(new BigDecimal("80"));
            temperature.setRange(tempRange);
            temperature.setWarning(data.getTemperature().compareTo(tempRange.getMax()) > 0);
            realTimeData.setTemperature(temperature);

            // Pressure
            DeviceDetailResponse.RealTimeData.ParameterInfo pressure = new DeviceDetailResponse.RealTimeData.ParameterInfo();
            pressure.setValue(data.getPressure());
            pressure.setUnit("MPa");
            pressure.setWarning(data.getPressure().compareTo(new BigDecimal("0.8")) > 0);
            realTimeData.setPressure(pressure);

            // Flow
            DeviceDetailResponse.RealTimeData.ParameterInfo flow = new DeviceDetailResponse.RealTimeData.ParameterInfo();
            flow.setValue(data.getFlow());
            flow.setUnit("m³/h");
            realTimeData.setFlow(flow);

            response.setRealTimeData(realTimeData);
        });

        return response;
    }


    // 判断设备是否存在
    @Override
    public boolean isDeviceExists(Long deviceId) {
        return deviceRepository.findById(deviceId).isPresent();   
    }

    @Override
    @Transactional
    public DeviceCreateResponse createDevice(DeviceCreateRequest request) {
        // Validate required fields
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("设备名称不能为空");
        }  

        if (request.getType() == null || request.getType().trim().isEmpty()) {
            throw new IllegalArgumentException("设备类型不能为空");
        }
        
        if (request.getLocation() == null || request.getLocation().getBuilding() == null || 
            request.getLocation().getBuilding().trim().isEmpty()) {
            throw new IllegalArgumentException("设备位置信息不完整");
        }

        // Create new device entity
        TDevice device = new TDevice();
        
        // Generate device ID if not provided through scan code
        String device_no = request.getScanCode() != null ?  
                         request.getScanCode() : 
                         "dev_" + UUID.randomUUID().toString().substring(0, 8);
        device.setNo(device_no);
        
        // Set basic info
        device.setName(request.getName());
        device.setType(request.getType());
        device.setModel(request.getModel());
        device.setManufacturer(request.getManufacturer());
        
        // Set location info
        device.setBuilding(request.getLocation().getBuilding());
        device.setFloor(request.getLocation().getFloor());
        device.setRoom(request.getLocation().getRoom());
        if (request.getLocation().getCoordinates() != null) {
            device.setLatitude(request.getLocation().getCoordinates().getLat());
            device.setLongitude(request.getLocation().getCoordinates().getLng());
        }
        
        // Set maintenance info
        if (request.getMaintenance() != null) {
            device.setPeriod(request.getMaintenance().getPeriod());
            LocalDateTime now = LocalDateTime.now();
            device.setLastMaintenance(now);
            if (request.getMaintenance().getPeriod() != null) {
                device.setNextMaintenance(now.plusDays(request.getMaintenance().getPeriod()));
            }
            // Store maintenance staff as JSON
            if (request.getMaintenance().getStaff() != null && !request.getMaintenance().getStaff().isEmpty()) {
              device.setStaff(request.getMaintenance().getStaff());
            }
        }
        
        // Set initial status
        device.setStatus(TDevice.DeviceStatus.online);
        device.setAlarmCount(0);
        
        // Save device
        device = deviceRepository.save(device);
        
        // Prepare response
        DeviceCreateResponse response = new DeviceCreateResponse();
        response.setDeviceId(device.getId().toString());
        response.setMessage("设备添加成功");
        
        return response;
    }

    @Override
    @Transactional
    public Map<String, Object> updateDevice(Long deviceId, DeviceUpdateRequest request) {
        // 参数校验
        if (deviceId == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        
        // 获取设备
        TDevice device = deviceRepository.findById(deviceId)
            .orElseThrow(() -> new IllegalArgumentException("设备不存在"));
        
        // 更新基本信息
        if (request.getName() != null) {
            device.setName(request.getName());
        }
        if (request.getType() != null) {
            device.setType(request.getType());
        }
        if (request.getModel() != null) {
            device.setModel(request.getModel());
        }
        if (request.getManufacturer() != null) {
            device.setManufacturer(request.getManufacturer());
        }   
        
        // 更新位置信息
        if (request.getLocation() != null) {
            DeviceUpdateRequest.LocationInfo location = request.getLocation();
            if (location.getBuilding() != null) {
                device.setBuilding(location.getBuilding());
            }
            if (location.getFloor() != null) {
                device.setFloor(location.getFloor());
            }
            if (location.getRoom() != null) {
                device.setRoom(location.getRoom());
            }
            if (location.getCoordinates() != null) {
                device.setLatitude(location.getCoordinates().getLat());
                device.setLongitude(location.getCoordinates().getLng());
            }
        }
        
        // 更新维护信息
        if (request.getMaintenance() != null) {
            DeviceUpdateRequest.MaintenanceInfo maintenance = request.getMaintenance();
            if (maintenance.getLast_maintenance() != null) {
                device.setLastMaintenance(LocalDateTime.parse(maintenance.getLast_maintenance(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (maintenance.getNext_maintenance() != null) {
                device.setNextMaintenance(LocalDateTime.parse(maintenance.getNext_maintenance(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }   
            if (maintenance.getPeriod() != null) {
                device.setPeriod(maintenance.getPeriod());
                // 更新下次维护时间
                if (device.getLastMaintenance() != null) {
                    device.setNextMaintenance(
                        device.getLastMaintenance().plusDays(maintenance.getPeriod())
                    );
                }
            }
            if (maintenance.getStaff() != null && !maintenance.getStaff().isEmpty()) {
                device.setStaff(maintenance.getStaff());
            }
        }
        
        // 保存更新
        deviceRepository.save(device);
        
        // 返回结果
        Map<String, Object> response = new HashMap<>();
        response.put("message", "设备更新成功");
        return response;
    }

   
    @Override
    public Map<String, Object> getDeviceMaintenance(Long deviceId) {
        // 参数校验
        if (deviceId == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        
        // 验证设备是否存在
        if (!isDeviceExists(deviceId)) {
            throw new IllegalArgumentException("设备不存在");
        }
        
        // 查询维护记录  device_maintenance 表中的 executor_id  对应的是user表中的user_id 字段
        // 最终 operator 是 user表中的 name 字段
        List<TDeviceMaintenance> maintenanceList = maintenanceRepository.findByDeviceIdOrderByStartTimeDesc(deviceId);
        for (TDeviceMaintenance maintenance : maintenanceList) {
            // Fetch the executor name from the user repository
            Optional<TUser> user = userRepository.findById(Long.parseLong(maintenance.getExecutorId()));
            if (user.isPresent()) { 
                maintenance.setExecutorName(user.get().getName());
            }
        }   

        // 转换为响应对象
        List<DeviceMaintenanceRecord> records = maintenanceList.stream()
            .map(this::convertToRecord)
            .collect(Collectors.toList());
            
        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("total", records.size());
        response.put("list", records);
        
        return response;
    }
    
    private DeviceMaintenanceRecord convertToRecord(TDeviceMaintenance maintenance) {
        DeviceMaintenanceRecord record = new DeviceMaintenanceRecord();
        record.setTime(maintenance.getStartTime().format(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        record.setAction(maintenance.getMaintenanceType());
        record.setOperator(maintenance.getExecutorName());
        
        // 处理媒体文件
        if (maintenance.getImages() != null && !maintenance.getImages().isEmpty()) {
            List<DeviceMaintenanceRecord.MediaInfo> mediaList = new ArrayList<>();
            try {
                ObjectMapper mapper = new ObjectMapper();
                List<String> images = mapper.readValue(
                    maintenance.getImages(), 
                    new TypeReference<List<String>>() {}
                );
                
                for (String imageUrl : images) {
                    DeviceMaintenanceRecord.MediaInfo media = 
                        new DeviceMaintenanceRecord.MediaInfo();
                    media.setType("image");
                    media.setUrl(imageUrl);
                    mediaList.add(media);
                }
                record.setMedia(mediaList);
            } catch (JsonProcessingException e) {
                log.error("解析维护记录图片失败", e);
            }
        }
        
        return record;
    }

    @Override
    public DeviceStatsResponse getDeviceStats() {
        // 从数据库查询各状态设备数量
        long total = deviceRepository.count();
        long online = deviceRepository.countByStatus(TDevice.DeviceStatus.online);
        long offline = deviceRepository.countByStatus(TDevice.DeviceStatus.offline);
        long fault = deviceRepository.countByStatus(TDevice.DeviceStatus.fault);
        
        return DeviceStatsResponse.builder()
                .total((int)total)
                .online((int)online)
                .offline((int)offline)
                .fault((int)fault)
                .build();
    }
   
    /**
     * 根据热用户ID获取设备列表
     *
     * @param heatUnitId 热用户ID
     * @return 设备列表
     */
    @Override
    public List<DeviceHeatUnitResponse> getDevicesByHeatUnitId(Long heatUnitId) {
        log.info("获取热用户ID:{}的设备列表", heatUnitId);
        
        if (heatUnitId == null) {
            throw new IllegalArgumentException("热用户ID不能为空");
        }

        try {
            // 查询热用户下的所有设备
            List<TDevice> devices = deviceRepository.findByHeatUnitId(heatUnitId);
            
            if (devices.isEmpty()) {
                return Collections.emptyList();
            }
            
            // 转换为DTO
            return devices.stream()
                    .map(device -> {
                        DeviceHeatUnitResponse response = new DeviceHeatUnitResponse();
                        response.setId(device.getId());
                        response.setName(device.getName());
                        response.setDeviceParent(device.getDeviceParent());
                        response.setType(device.getType());
                        return response;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取热用户设备列表失败", e);
            throw new RuntimeException("获取热用户设备列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取设备巡检项目列表
     *
     * @param deviceId 设备ID
     * @return 巡检项目列表
     */
    @Override
    public List<DevicePatrolItemResponse> getDevicePatrolItems(Long deviceId) {
        log.info("获取设备ID:{}的巡检项列表", deviceId);
        
        if (deviceId == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }

        // 验证设备是否存在
        if (!deviceRepository.existsById(deviceId)) {
            throw new IllegalArgumentException("设备不存在");
        }
        
        try {
            // 查询设备关联的巡检项
            List<TDevicePatrolItem> devicePatrolItems = devicePatrolItemRepository.findByDeviceId(deviceId);
            
            // 如果没有关联巡检项，返回空列表
            if (devicePatrolItems.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 转换为响应DTO
            List<DevicePatrolItemResponse> responses = new ArrayList<>();
            
            for (TDevicePatrolItem item : devicePatrolItems) {
                DevicePatrolItemResponse response = new DevicePatrolItemResponse();
                response.setId(item.getId());
                response.setDeviceId(item.getDeviceId());
                response.setPatrolItemDictId(item.getPatrolItemDictId());
                response.setNormalRange(item.getNormalRange());
                
                // 获取巡检项字典信息
                Optional<PatrolItemDictionary> dictOptional = patrolItemDictionaryRepository.findById(item.getPatrolItemDictId());
                
                if (dictOptional.isPresent()) {
                    PatrolItemDictionary dict = dictOptional.get();
                    response.setItemName(dict.getItemName());
                    response.setCategoryName(dict.getCategoryName());
                    response.setParamType(dict.getParamType());
                    response.setUnit(dict.getUnit());
                    response.setCheckMethod(dict.getCheckMethod());
                    response.setImportance(dict.getImportance());
                    response.setDescription(dict.getDescription());
                } else {
                    // 巡检项字典不存在，设置默认值
                    response.setItemName("未知巡检项");
                    response.setCategoryName("未知类别");
                    log.warn("巡检项字典不存在, ID: {}", item.getPatrolItemDictId());
                }
                
                responses.add(response);
            }
            
            return responses;
            
        } catch (Exception e) {
            log.error("获取设备巡检项列表失败", e);
            throw new RuntimeException("获取设备巡检项列表失败: " + e.getMessage(), e);
        }
    }
} 