package com.heating.service.impl;

import com.heating.dto.attendance.AttendanceClockRequest;
import com.heating.dto.attendance.AttendanceClockResponse;
import com.heating.dto.attendance.AttendanceRecordResponse;
import com.heating.dto.attendance.AttendanceStatsResponse;
import com.heating.dto.attendance.AttendanceStaffListResponse;
import com.heating.dto.attendance.AttendanceSupplementApproveRequest;
import com.heating.dto.attendance.AttendanceSupplementApproveResponse;
import com.heating.entity.attendance.TAttendanceRecord;
import com.heating.entity.user.TUser;
import com.heating.repository.AttendanceRecordRepository;
import com.heating.repository.UserRepository;
import com.heating.service.AttendanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.temporal.ChronoUnit;
import java.time.DayOfWeek;
import java.util.HashSet;
import com.heating.dto.attendance.AttendanceDepartmentStatsResponse;
import com.heating.dto.attendance.AttendanceDepartmentStatsQuery; 

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.heating.dto.attendance.AttendanceStaffResponse;
import com.heating.dto.attendance.AttendanceStaffQuery;
import com.heating.dto.attendance.AttendanceTodayResponse;
import com.heating.dto.attendance.AttendanceRecentResponse;
import com.heating.dto.attendance.AttendanceCheckAreaRequest;
import com.heating.dto.attendance.AttendanceCheckAreaResponse;
import com.heating.dto.attendance.AttendanceRulesResponse;
import com.heating.dto.attendance.AttendanceSupplementRequest;
import com.heating.dto.attendance.AttendanceSupplementResponse;
import com.heating.dto.user.UserBasicInfo;
import com.heating.entity.attendance.TOtherParam;
import com.heating.repository.OtherSystemRepository;


@Service
public class AttendanceServiceImpl implements AttendanceService {

    private static final Logger log = LoggerFactory.getLogger(AttendanceServiceImpl.class);

    @Autowired
    private AttendanceRecordRepository attendanceRecordRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OtherSystemRepository otherSystemRepository;

    private static final LocalTime MORNING_START_TIME = LocalTime.of(9, 0); // 上班时间9:00
    private static final LocalTime EVENING_END_TIME = LocalTime.of(18, 0);  // 下班时间18:00

    // 工作日计算常量
    private static final int WORKDAYS_PER_WEEK = 5; // 每周工作日数量（周一至周五）

    @Override
    @Transactional
    public AttendanceClockResponse clockIn(AttendanceClockRequest request) {
        // 参数校验
        validateRequest(request);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        
        // 创建打卡记录
        TAttendanceRecord record = new TAttendanceRecord();
        record.setUserId(request.getUserId());
        record.setClockType(request.getClockType());
        record.setClockTime(now);
        record.setLatitude(request.getLatitude());
        record.setLongitude(request.getLongitude());
        record.setStatus(request.getStatus());
//        record.setFacePhoto(request.getFacePhoto());
//        record.setLivenessData(request.getLivenessData());
        record.setLeaveType(request.getLeaveType());
        record.setLeaveReason(request.getLeaveReason());
//        record.setLeaveProof(request.getLeaveProof());
        record.setCreateTime(now);
        
        // 判断打卡状态
        String status = determineClockStatus(request.getUserId(), request.getClockType(), now);
        record.setStatus(status);
        
        // 保存打卡记录
        attendanceRecordRepository.save(record);
        
        // 构建响应
        AttendanceClockResponse response = new AttendanceClockResponse();
        response.setClockTime(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setStatus(status);
        
        return response;
    }
    
    /**
     * 校验请求参数
     * @param request 打卡请求
     */
    private void validateRequest(AttendanceClockRequest request) {
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        if (request.getClockType() == null || request.getClockType().trim().isEmpty()) {
            throw new IllegalArgumentException("打卡类型不能为空");
        }
        
        if (!request.getClockType().equals("checkin") && !request.getClockType().equals("checkout")) {
            throw new IllegalArgumentException("打卡类型必须为checkin或checkout");
        }
        
        if (request.getLatitude() == null) {
            throw new IllegalArgumentException("纬度不能为空");
        }
        
        if (request.getLongitude() == null) {
            throw new IllegalArgumentException("经度不能为空");
        }
        
        // 人脸照片可选，不再强制要求
        // if (request.getFacePhoto() == null || request.getFacePhoto().isEmpty()) {
        //     throw new IllegalArgumentException("人脸照片不能为空");
        // }
    }
    
    /**
     * 判断打卡状态
     * @param userId 用户ID
     * @param clockType 打卡类型
     * @param clockTime 打卡时间
     * @return 打卡状态
     */
    private String determineClockStatus(Long userId, String clockType, LocalDateTime clockTime) {
        LocalTime time = clockTime.toLocalTime();
        
        // 判断是否请假
        if (isOnLeave(userId, clockTime.toLocalDate())) {
            return "leave";
        }
        
        // 上班打卡
        if ("checkin".equals(clockType)) {
            return time.isAfter(MORNING_START_TIME) ? "late" : "normal";
        } 
        // 下班打卡
        else if ("checkout".equals(clockType)) {
            return time.isBefore(EVENING_END_TIME) ? "early" : "normal";
        }
        
        return "normal";
    }
    
    /**
     * 查询考勤记录
     * @param userId 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）    
     * @param day 天数（可选）
     * @param status 状态（可选）
     * @return 考勤记录响应
     */
    @Override
    public AttendanceRecordResponse getAttendanceRecords(Long userId, Integer year, Integer month, Integer day, String status) {
        // 处理年份和月份参数，如果未提供则使用当前年月日
        LocalDate now = LocalDate.now();
        year = (year != null) ? year : now.getYear();
        month = (month != null) ? month : now.getMonthValue();
        
        // 计算日期范围
        LocalDate startDate;
        LocalDate endDate;
        
        if (day != null) {
            // 如果提供了具体日期，则查询当天记录
            startDate = LocalDate.of(year, month, day);
            endDate = startDate;
        } else {
            // 否则查询整个月的记录
            startDate = LocalDate.of(year, month, 1);
            endDate = startDate.plusMonths(1).minusDays(1);
        }
        
        LocalDateTime startOfPeriod = startDate.atStartOfDay();
        LocalDateTime endOfPeriod = endDate.atTime(23, 59, 59);
        
        log.info("查询考勤记录：userId={}, 日期范围={} 至 {}", userId, startOfPeriod, endOfPeriod);
        
        // 查询考勤记录
        List<TAttendanceRecord> records;
        if (userId != null) {
            // 验证用户是否存在
            Optional<TUser> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            if (status != null && !status.isEmpty()) {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenAndStatusOrderByClockTimeDesc(
                        userId, startOfPeriod, endOfPeriod, status);
            } else {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        userId, startOfPeriod, endOfPeriod);
            }
        } else {
            if (status != null && !status.isEmpty()) {
                records = attendanceRecordRepository.findByClockTimeBetweenAndStatusOrderByClockTimeDesc(
                        startOfPeriod, endOfPeriod, status);
            } else {
                records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                        startOfPeriod, endOfPeriod);
            }
        }
        
        log.info("查询到 {} 条考勤记录", records.size());
        
        // 构建响应
        AttendanceRecordResponse response = new AttendanceRecordResponse();
        
        // 计算考勤统计数据
        Map<String, Object> summary = calculateAttendanceSummary(records, startDate, endDate);
        response.setSummary(summary);
        
        // 按用户ID和日期分组打卡记录
        Map<String, Map<LocalDate, List<TAttendanceRecord>>> recordsByUserAndDate = records.stream()
                .collect(Collectors.groupingBy(
                        record -> String.valueOf(record.getUserId()),
                        Collectors.groupingBy(record -> record.getClockTime().toLocalDate())
                ));
        
        // 转换考勤记录，合并同一天的上下班打卡
        List<AttendanceRecordResponse.AttendanceRecordDetail> recordDetails = new ArrayList<>();
        
        recordsByUserAndDate.forEach((userIdStr, userRecords) -> {
            userRecords.forEach((date, dailyRecords) -> {
                // 查找当天的上班打卡记录
                Optional<TAttendanceRecord> clockInRecord = dailyRecords.stream()
                        .filter(r -> "checkin".equals(r.getClockType()))
                        .findFirst();
                
                // 查找当天的下班打卡记录
                Optional<TAttendanceRecord> clockOutRecord = dailyRecords.stream()
                        .filter(r -> "checkout".equals(r.getClockType()))
                        .findFirst();
                
                // 创建合并后的记录
                AttendanceRecordResponse.AttendanceRecordDetail detail = new AttendanceRecordResponse.AttendanceRecordDetail();
                
                // 设置用户信息（从任一记录中获取）
                TAttendanceRecord anyRecord = dailyRecords.get(0);
                Optional<TUser> userOpt = userRepository.findById(anyRecord.getUserId());
                if (userOpt.isPresent()) {
                    TUser user = userOpt.get();
                    UserBasicInfo userInfo = new UserBasicInfo();
                    userInfo.setId(user.getId());
                    userInfo.setName(user.getName());
                    userInfo.setAvatar(user.getAvatar());
                    userInfo.setDepartment(user.getDepartment());
                    detail.setUser(userInfo);
                }
                
                // 设置日期作为记录标识
                detail.setCreateTime(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                
                // 设置日期和星期几
                detail.setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                detail.setWeek(getWeekDayString(date.getDayOfWeek()));
                
                // 设置上班打卡信息
                if (clockInRecord.isPresent()) {
                    TAttendanceRecord record = clockInRecord.get();
                    detail.setClockType("daily"); // 标记为日常打卡
                    detail.setClockTime(record.getClockTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    detail.setLatitude(record.getLatitude());
                    detail.setLongitude(record.getLongitude());
                    detail.setFacePhoto(record.getFacePhoto());
                    detail.setLivenessData(record.getLivenessData());
                    detail.setStatus(record.getStatus());
                    
                    // 设置上班打卡时间
                    detail.setClockInTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                    detail.setClockInStatus(record.getStatus());
                }
                
                // 添加下班打卡信息
                if (clockOutRecord.isPresent()) {
                    TAttendanceRecord record = clockOutRecord.get();
                    // 如果没有上班打卡记录，使用下班打卡的基本信息
                    if (!clockInRecord.isPresent()) {
                        detail.setClockType("daily");
                        detail.setClockTime(record.getClockTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        detail.setLatitude(record.getLatitude());
                        detail.setLongitude(record.getLongitude());
                        detail.setFacePhoto(record.getFacePhoto());
                        detail.setLivenessData(record.getLivenessData());
                        detail.setStatus(record.getStatus());
                    }
                    
                    // 设置下班打卡时间
                    detail.setClockOutTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                    detail.setClockOutStatus(record.getStatus());
                    
                    // 设置请假信息（如果有）
                    if (record.getLeaveType() != null && !record.getLeaveType().isEmpty()) {
                        detail.setLeaveType(record.getLeaveType());
                        detail.setLeaveReason(record.getLeaveReason());
                        detail.setLeaveProof(record.getLeaveProof());
                    }
                }
                
                recordDetails.add(detail);
            });
        });
        
        // 按日期降序排序
        recordDetails.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        response.setRecords(recordDetails);
        return response;
    }

    /**
     * 计算考勤统计数据
     * @param records 考勤记录列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    private Map<String, Object> calculateAttendanceSummary(List<TAttendanceRecord> records, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> summary = new HashMap<>();
        
        // 计算工作日数量（简化版，仅考虑周一至周五）
        long totalDays = ChronoUnit.DAYS.between(startDate, endDate.plusDays(1));
        long weekends = 0;
        for (int i = 0; i < totalDays; i++) {
            LocalDate date = startDate.plusDays(i);
            if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                weekends++;
            }
        }
        int workdays = (int) (totalDays - weekends);
        
        // 统计出勤、迟到、缺勤数量
        Set<LocalDate> attendanceDates = new HashSet<>();
        int lateCount = 0;
        int absentCount = 0;
        
        for (TAttendanceRecord record : records) {
            LocalDate recordDate = record.getClockTime().toLocalDate();
            attendanceDates.add(recordDate);
            
            if ("late".equals(record.getStatus())) {
                lateCount++;
            }
        }
        
        // 计算出勤天数（有打卡记录的天数）
        int attendanceCount = attendanceDates.size();
        
        // 计算缺勤天数（工作日 - 出勤天数）
        absentCount = workdays - attendanceCount;
        if (absentCount < 0) absentCount = 0;
        
        summary.put("workdays", workdays);
        summary.put("attendance", attendanceCount);
        summary.put("late", lateCount);
        summary.put("absent", absentCount);
        
        return summary;
    }

    /**
     * 将考勤记录实体转换为响应DTO
     * @param record 考勤记录实体
     * @return 考勤记录详情DTO
     */
    private AttendanceRecordResponse.AttendanceRecordDetail convertToRecordDetail(TAttendanceRecord record) {
        AttendanceRecordResponse.AttendanceRecordDetail detail = new AttendanceRecordResponse.AttendanceRecordDetail();
        detail.setClockType(record.getClockType());
        detail.setClockTime(record.getClockTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        detail.setLatitude(record.getLatitude());
        detail.setLongitude(record.getLongitude());
        detail.setFacePhoto(record.getFacePhoto());
        detail.setLivenessData(record.getLivenessData());
        detail.setStatus(record.getStatus());
        detail.setLeaveType(record.getLeaveType());
        detail.setLeaveReason(record.getLeaveReason());
        detail.setLeaveProof(record.getLeaveProof());
        detail.setCreateTime(record.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 获取用户信息并添加到响应中
        Optional<TUser> userOpt = userRepository.findById(record.getUserId());
        if (userOpt.isPresent()) {
            TUser user = userOpt.get();
            UserBasicInfo userInfo = new UserBasicInfo();
            userInfo.setId(user.getId());
            userInfo.setName(user.getName());
            userInfo.setAvatar(user.getAvatar());
            userInfo.setDepartment(user.getDepartment());
            detail.setUser(userInfo);
        }
        
        return detail;
    }

    /**
     * 判断用户是否请假
     * @param userId 用户ID
     * @param date 日期
     * @return 是否请假
     */
    private boolean isOnLeave(Long userId, LocalDate date) {
        // 查询当天是否有请假记录
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        
        List<TAttendanceRecord> records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                userId, startOfDay, endOfDay);
        
        return records.stream()
                .anyMatch(record -> record.getLeaveType() != null && !record.getLeaveType().isEmpty());
    }

    /**
     * 查询考勤统计
     * @param userId 用户ID（可选）
     * @param year 年份（可选）
     * @param month 月份（可选）
     * @param day 天数（可选）
     * @return 考勤统计响应
     */
    @Override
    public AttendanceStatsResponse getAttendanceStats(Long userId, Integer year, Integer month, Integer day) {  
        // 如果只提供了年份，则统计整年的数据
        if (month == null) 
        {
            return getYearlyAttendanceStats(userId, year);
        } 
        // 如果只提供了年份和月份，则统计月度数据
        if(day == null)
        {
            return getMonthlyAttendanceStats(userId, year, month);
        } 
        // 如果提供了年、月、日，则统计单日数据
        return getDailyAttendanceStats(userId, year, month, day);
    }

    /**
     * 获取日考勤统计
     * @param userId 用户ID（可选）
     * @param year 年份
     * @param month 月份
     * @param day 日期
     * @return 考勤统计响应
     */
    private AttendanceStatsResponse getDailyAttendanceStats(Long userId, Integer year, Integer month, Integer day) {
        // 构建响应
        AttendanceStatsResponse response = new AttendanceStatsResponse();
        
        // 计算日期的起止时间
        LocalDate date = LocalDate.of(year, month, day);
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        
        // 查询考勤记录
        List<TAttendanceRecord> records;
        if (userId != null) {
            // 验证用户是否存在
            Optional<TUser> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                    userId, startOfDay, endOfDay);
        } else {
            records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                    startOfDay, endOfDay);
        }
        
        // 计算考勤统计数据
        Map<String, Object> summary = new HashMap<>();
        
        // 判断是否是工作日
        boolean isWorkday = true;
        if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
            isWorkday = false;
        }
        
        // 统计数据
        int workdays = isWorkday ? 1 : 0;
        boolean hasAttendance = !records.isEmpty();
        int attendanceCount = hasAttendance ? 1 : 0;
        int lateCount = records.stream().anyMatch(r -> "late".equals(r.getStatus())) ? 1 : 0;
        int absentCount = (isWorkday && !hasAttendance) ? 1 : 0;
        
        summary.put("workdays", workdays);
        summary.put("attendance", attendanceCount);
        summary.put("late", lateCount);
        summary.put("absent", absentCount);
        response.setSummary(summary);
        
        // 生成图表数据
        AttendanceStatsResponse.ChartData chartData = new AttendanceStatsResponse.ChartData();
        
        // 对于单日统计，X轴只有一个数据点
        List<String> xAxis = new ArrayList<>();
        xAxis.add(String.format("%02d", day));
        chartData.setXAxis(xAxis);
        
        // 设置数据系列
        List<AttendanceStatsResponse.Series> seriesList = new ArrayList<>();
        
        // 出勤率系列
        AttendanceStatsResponse.Series attendanceSeries = new AttendanceStatsResponse.Series();
        attendanceSeries.setName("出勤率");
        List<Double> attendanceRateData = new ArrayList<>();
        attendanceRateData.add(hasAttendance ? 1.0 : 0.0);
        attendanceSeries.setData(attendanceRateData);
        seriesList.add(attendanceSeries);
        
        // 迟到率系列
        AttendanceStatsResponse.Series lateSeries = new AttendanceStatsResponse.Series();
        lateSeries.setName("迟到率");
        List<Double> lateRateData = new ArrayList<>();
        lateRateData.add(lateCount > 0 ? 1.0 : 0.0);
        lateSeries.setData(lateRateData);
        seriesList.add(lateSeries);
        
        // 缺勤率系列
        AttendanceStatsResponse.Series absentSeries = new AttendanceStatsResponse.Series();
        absentSeries.setName("缺勤率");
        List<Double> absentRateData = new ArrayList<>();
        absentRateData.add(absentCount > 0 ? 1.0 : 0.0);
        absentSeries.setData(absentRateData);
        seriesList.add(absentSeries);
        
        chartData.setSeries(seriesList);
        response.setChart(chartData);
        
        return response;
    }

    /**
     * 获取年度考勤统计
     * @param userId 用户ID（可选）
     * @param year 年份
     * @return 考勤统计响应
     */
    private AttendanceStatsResponse getYearlyAttendanceStats(Long userId, Integer year) {
        // 构建响应
        AttendanceStatsResponse response = new AttendanceStatsResponse();
        
        // 计算年度考勤统计数据
        Map<String, Object> summary = new HashMap<>();
        int totalWorkdays = 0;
        int totalAttendance = 0;
        int totalLate = 0;
        int totalAbsent = 0;
        
        // 生成年度图表数据
        List<String> xAxis = new ArrayList<>();
        List<Double> attendanceRateData = new ArrayList<>();
        List<Double> lateRateData = new ArrayList<>();
        List<Double> absentRateData = new ArrayList<>();
        
        // 按月统计
        for (int month = 1; month <= 12; month++) {
            // 计算月份的起止时间
            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            LocalDateTime startOfMonth = startDate.atStartOfDay();
            LocalDateTime endOfMonth = endDate.atTime(23, 59, 59);
            
            // 查询考勤记录
            List<TAttendanceRecord> records;
            if (userId != null) {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        userId, startOfMonth, endOfMonth);
            } else {
                records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                        startOfMonth, endOfMonth);
            }
            
            // 计算月度统计数据
            Map<String, Object> monthlySummary = calculateAttendanceSummary(records, startDate, endDate);
            
            // 累加年度统计数据
            totalWorkdays += (int) monthlySummary.get("workdays");
            totalAttendance += (int) monthlySummary.get("attendance");
            totalLate += (int) monthlySummary.get("late");
            totalAbsent += (int) monthlySummary.get("absent");
            
            // 计算月度比率
            int workdays = (int) monthlySummary.get("workdays");
            if (workdays > 0) {
                double attendanceRate = (double) (int) monthlySummary.get("attendance") / workdays;
                double lateRate = (double) (int) monthlySummary.get("late") / workdays;
                double absentRate = (double) (int) monthlySummary.get("absent") / workdays;
                
                // 添加到图表数据
                xAxis.add(String.format("%02d", month));
                attendanceRateData.add(Math.round(attendanceRate * 100) / 100.0);
                lateRateData.add(Math.round(lateRate * 100) / 100.0);
                absentRateData.add(Math.round(absentRate * 100) / 100.0);
            } else {
                xAxis.add(String.format("%02d", month));
                attendanceRateData.add(0.0);
                lateRateData.add(0.0);
                absentRateData.add(0.0);
            }
        }
        
        // 设置年度统计数据
        summary.put("workdays", totalWorkdays);
        summary.put("attendance", totalAttendance);
        summary.put("late", totalLate);
        summary.put("absent", totalAbsent);
        response.setSummary(summary);
        
        // 设置图表数据
        AttendanceStatsResponse.ChartData chartData = new AttendanceStatsResponse.ChartData();
        chartData.setXAxis(xAxis);
        
        List<AttendanceStatsResponse.Series> seriesList = new ArrayList<>();
        
        AttendanceStatsResponse.Series attendanceSeries = new AttendanceStatsResponse.Series();
        attendanceSeries.setName("出勤率");
        attendanceSeries.setData(attendanceRateData);
        seriesList.add(attendanceSeries);
        
        AttendanceStatsResponse.Series lateSeries = new AttendanceStatsResponse.Series();
        lateSeries.setName("迟到率");
        lateSeries.setData(lateRateData);
        seriesList.add(lateSeries);
        
        AttendanceStatsResponse.Series absentSeries = new AttendanceStatsResponse.Series();
        absentSeries.setName("缺勤率");
        absentSeries.setData(absentRateData);
        seriesList.add(absentSeries);
        
        chartData.setSeries(seriesList);
        response.setChart(chartData);
        
        return response;
    }

    /**
     * 获取月度考勤统计
     * @param userId 用户ID（可选）
     * @param year 年份
     * @param month 月份
     * @return 考勤统计响应
     */
    private AttendanceStatsResponse getMonthlyAttendanceStats(Long userId, Integer year, Integer month) {      
        // 构建响应
        AttendanceStatsResponse response = new AttendanceStatsResponse();

        System.out.println("===============================================");
        
        // 计算月份的起止时间
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        int daysInMonth = endDate.getDayOfMonth();
        
        LocalDateTime startOfMonth = startDate.atStartOfDay();
        LocalDateTime endOfMonth = endDate.atTime(23, 59, 59);
        
        // 查询考勤记录
        List<TAttendanceRecord> records;
        if (userId != null) {
            // 验证用户是否存在
            Optional<TUser> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                    userId, startOfMonth, endOfMonth);
        } else {
            records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                    startOfMonth, endOfMonth);
        }

        // 控制台输出 计算 records 数量
        System.out.println("计算 records 数量: " + records.size());
        
        // 计算月度统计数据
        Map<String, Object> summary = calculateAttendanceSummary(records, startDate, endDate);
        response.setSummary(summary);
        
        // 生成日期X轴数据（每一天）
        List<String> xAxis = new ArrayList<>();
        List<Double> attendanceRateData = new ArrayList<>();
        List<Double> lateRateData = new ArrayList<>();
        List<Double> absentRateData = new ArrayList<>();
        
        // 按天统计
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = LocalDate.of(year, month, day);
            xAxis.add(String.format("%02d", day));
            
            // 跳过周末或按实际情况统计
            if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                attendanceRateData.add(0.0);
                lateRateData.add(0.0);
                absentRateData.add(0.0);
                continue;
            }
            
            // 获取当天考勤记录
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(23, 59, 59);
            
            List<TAttendanceRecord> dayRecords;
            if (userId != null) {
                dayRecords = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        userId, startOfDay, endOfDay);
            } else {
                dayRecords = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                        startOfDay, endOfDay);
            }
            
            // 计算当天考勤情况
            boolean hasAttendance = !dayRecords.isEmpty();
            boolean hasLate = dayRecords.stream().anyMatch(r -> "late".equals(r.getStatus()));
            boolean hasLeave = dayRecords.stream().anyMatch(r -> r.getLeaveType() != null && !r.getLeaveType().isEmpty());
            
            // 添加到统计数据
            attendanceRateData.add(hasAttendance ? 1.0 : 0.0);
            lateRateData.add(hasLate ? 1.0 : 0.0);
            absentRateData.add(!hasAttendance && !hasLeave ? 1.0 : 0.0);
        }
        
        // 设置图表数据
        AttendanceStatsResponse.ChartData chartData = new AttendanceStatsResponse.ChartData();
        chartData.setXAxis(xAxis);
        
        List<AttendanceStatsResponse.Series> seriesList = new ArrayList<>();
        
        AttendanceStatsResponse.Series attendanceSeries = new AttendanceStatsResponse.Series();
        attendanceSeries.setName("出勤率");
        attendanceSeries.setData(attendanceRateData);
        seriesList.add(attendanceSeries);
        
        AttendanceStatsResponse.Series lateSeries = new AttendanceStatsResponse.Series();
        lateSeries.setName("迟到率");
        lateSeries.setData(lateRateData);
        seriesList.add(lateSeries);
        
        AttendanceStatsResponse.Series absentSeries = new AttendanceStatsResponse.Series();
        absentSeries.setName("缺勤率");
        absentSeries.setData(absentRateData);
        seriesList.add(absentSeries);
        
        chartData.setSeries(seriesList);
        response.setChart(chartData);
        
        return response;
    }

    /**
     * 生成图表数据
     * @param userId 用户ID（可选）
     * @param year 年份
     * @param month 月份
     * @return 图表数据
     */
    private AttendanceStatsResponse.ChartData generateChartData(Long userId, Integer year, Integer month) {
        AttendanceStatsResponse.ChartData chartData = new AttendanceStatsResponse.ChartData();
        
        // 获取月份的天数
        LocalDate startDate = LocalDate.of(year, month, 1);
        int daysInMonth = startDate.lengthOfMonth();
        
        // 生成X轴数据（日期）
        List<String> xAxis = new ArrayList<>();
        for (int day = 1; day <= daysInMonth; day++) {
            xAxis.add(String.format("%02d", day));
        }
        chartData.setXAxis(xAxis);
        
        // 初始化数据系列
        List<Double> attendanceRateData = new ArrayList<>();
        List<Double> lateRateData = new ArrayList<>();
        List<Double> absentRateData = new ArrayList<>();
        
        // 按天统计
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = LocalDate.of(year, month, day);
            
            // 跳过周末
            if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                attendanceRateData.add(0.0);
                lateRateData.add(0.0);
                absentRateData.add(0.0);
                continue;
            }
            
            // 查询当天考勤记录
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.atTime(23, 59, 59);
            
            List<TAttendanceRecord> records;
            if (userId != null) {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        userId, startOfDay, endOfDay);
            } else {
                records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                        startOfDay, endOfDay);
            }
            
            // 计算当天考勤率
            boolean hasAttendance = !records.isEmpty();
            boolean hasLate = records.stream().anyMatch(r -> "late".equals(r.getStatus()));
            boolean hasLeave = records.stream().anyMatch(r -> r.getLeaveType() != null && !r.getLeaveType().isEmpty());
            
            // 添加到数据系列
            attendanceRateData.add(hasAttendance ? 1.0 : 0.0);
            lateRateData.add(hasLate ? 1.0 : 0.0);
            absentRateData.add(!hasAttendance && !hasLeave ? 1.0 : 0.0);
        }
        
        // 设置数据系列
        List<AttendanceStatsResponse.Series> seriesList = new ArrayList<>();
        
        AttendanceStatsResponse.Series attendanceSeries = new AttendanceStatsResponse.Series();
        attendanceSeries.setName("出勤率");
        attendanceSeries.setData(attendanceRateData);
        seriesList.add(attendanceSeries);
        
        AttendanceStatsResponse.Series lateSeries = new AttendanceStatsResponse.Series();
        lateSeries.setName("迟到率");
        lateSeries.setData(lateRateData);
        seriesList.add(lateSeries);
        
        AttendanceStatsResponse.Series absentSeries = new AttendanceStatsResponse.Series();
        absentSeries.setName("缺勤率");
        absentSeries.setData(absentRateData);
        seriesList.add(absentSeries);
        
        chartData.setSeries(seriesList);
        
        return chartData;
    }

    @Override
    public AttendanceStaffListResponse getAllStaff() {
        log.info("Getting all staff for attendance management");
        
        List<TUser> allUsers = userRepository.findAll();
        
        List<AttendanceStaffListResponse.StaffInfo> staffInfoList = allUsers.stream()
            .map(user -> new AttendanceStaffListResponse.StaffInfo(
                user.getId(),
                user.getName(),
                user.getDepartment(),
                handleMultipleRoles(user.getRole()) // 处理多角色情况
            ))
            .collect(Collectors.toList());
        
        return new AttendanceStaffListResponse(staffInfoList);
    }
    
    /**
     * 处理多角色情况，将逗号分隔的角色字符串处理为适合显示的格式
     * @param role 角色字符串，可能包含多个角色（逗号分隔）
     * @return 处理后的角色字符串
     */
    private String handleMultipleRoles(String role) {
        if (role == null || role.isEmpty()) {
            return "";
        }
        
        // 如果角色包含逗号，说明是多角色情况
        if (role.contains(",")) {
            String[] roles = role.split(",");
            // 返回第一个角色作为显示（或者可以自定义其他处理逻辑）
            return roles[0].trim();
        }
        
        return role;
    }
    
    @Override
    @Transactional
    public AttendanceSupplementApproveResponse approveSupplement(AttendanceSupplementApproveRequest request) {
        log.info("Processing supplement approval request: {}", request);
        
        if (request.getId() == null) {
            throw new IllegalArgumentException("补卡申请ID不能为空");
        }
        
        if (request.getStatus() == null || request.getStatus().isEmpty()) {
            throw new IllegalArgumentException("审批状态不能为空");
        }
        
        // 查找考勤记录
        TAttendanceRecord record = attendanceRecordRepository.findById(request.getId())
            .orElseThrow(() -> new IllegalArgumentException("补卡申请不存在"));
        
        // 更新审批状态
        record.setStatus(request.getStatus());
        record.setLeaveReason(request.getRemark()); // 使用leaveReason字段存储审批备注
        
        // 保存更新后的记录
        attendanceRecordRepository.save(record);
        
        // 构建响应
        AttendanceSupplementApproveResponse response = new AttendanceSupplementApproveResponse();
        response.setId(record.getId());
        response.setStatus(record.getStatus());
        response.setApprovedTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 获取审批人信息
        String approvedBy = "管理员"; // 默认值
        if (record.getUserId() != null) {
            Optional<TUser> approver = userRepository.findById(record.getUserId());
            if (approver.isPresent()) {
                approvedBy = approver.get().getName();
            }
        }
        response.setApprovedBy(approvedBy);
        
        return response;
    }

    /**
     * 获取部门考勤统计
     * @param query 查询条件
     * @return 部门考勤统计
     */
    @Override
    @Transactional
    public AttendanceDepartmentStatsResponse getDepartmentStats(AttendanceDepartmentStatsQuery query) {
        log.info("Getting department stats for query: {}", query);
        
        // 处理年份和月份参数，如果未提供则使用当前年月
        LocalDate now = LocalDate.now();
        Integer year = (query.getYear() != null) ? query.getYear() : now.getYear();
        Integer month = (query.getMonth() != null) ? query.getMonth() : now.getMonthValue();
        
        // 计算月份的起止时间
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        LocalDateTime startOfMonth = startDate.atStartOfDay();
        LocalDateTime endOfMonth = endDate.atTime(23, 59, 59);
        
        // 获取所有用户，按部门分组
        List<TUser> allUsers = userRepository.findAll();
        Map<String, List<TUser>> departmentUsers = allUsers.stream()
            .filter(user -> user.getDepartment() != null && !user.getDepartment().isEmpty())
            .collect(Collectors.groupingBy(TUser::getDepartment));
        
        // 创建响应对象
        List<AttendanceDepartmentStatsResponse.DepartmentStats> departmentStatsList = new ArrayList<>();
        
        // 计算每个部门的考勤统计
        for (Map.Entry<String, List<TUser>> entry : departmentUsers.entrySet()) {
            String departmentName = entry.getKey();
            List<TUser> users = entry.getValue();
            
            // 计算工作日数量
            long totalDays = ChronoUnit.DAYS.between(startDate, endDate.plusDays(1));
            long weekends = 0;
            for (int i = 0; i < totalDays; i++) {
                LocalDate date = startDate.plusDays(i);
                if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                    weekends++;
                }
            }
            int workdays = (int) (totalDays - weekends);
            int totalWorkdays = workdays * users.size(); // 总的工作日数量（人数 * 工作日）
            
            // 统计数据
            int attendanceCount = 0;
            int lateCount = 0;
            int earlyLeaveCount = 0;
            int absentCount = 0;
            
            // 遍历每个用户计算考勤
            for (TUser user : users) {
                // 查询用户在此期间的考勤记录
                List<TAttendanceRecord> records = attendanceRecordRepository
                    .findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        user.getId(), startOfMonth, endOfMonth);
                
                // 统计出勤天数（有打卡记录的天数）
                Set<LocalDate> attendanceDates = new HashSet<>();
                for (TAttendanceRecord record : records) {
                    attendanceDates.add(record.getClockTime().toLocalDate());
                    
                    if ("late".equals(record.getStatus())) {
                        lateCount++;
                    } else if ("early".equals(record.getStatus())) {
                        earlyLeaveCount++;
                    }
                }
                
                // 计算出勤天数和缺勤天数
                attendanceCount += attendanceDates.size();
                int userAbsentCount = workdays - attendanceDates.size();
                if (userAbsentCount > 0) {
                    absentCount += userAbsentCount;
                }
            }
            
            // 计算比率
            double attendanceRate = totalWorkdays > 0 ? (double) attendanceCount / totalWorkdays : 0;
            double lateRate = totalWorkdays > 0 ? (double) lateCount / totalWorkdays : 0;
            double earlyLeaveRate = totalWorkdays > 0 ? (double) earlyLeaveCount / totalWorkdays : 0;
            double absentRate = totalWorkdays > 0 ? (double) absentCount / totalWorkdays : 0;
            
            // 构建部门统计
            AttendanceDepartmentStatsResponse.DepartmentStats stats = new AttendanceDepartmentStatsResponse.DepartmentStats();
            stats.setDepartmentName(departmentName);
            stats.setStaffCount(users.size());
            stats.setAttendanceRate(Math.round(attendanceRate * 100) / 100.0);
            stats.setLateRate(Math.round(lateRate * 100) / 100.0);
            stats.setEarlyLeaveRate(Math.round(earlyLeaveRate * 100) / 100.0);
            stats.setAbsentRate(Math.round(absentRate * 100) / 100.0);
            
            departmentStatsList.add(stats);
        }
        
        // 按出勤率降序排序
        departmentStatsList.sort((a, b) -> Double.compare(b.getAttendanceRate(), a.getAttendanceRate()));
        
        AttendanceDepartmentStatsResponse response = new AttendanceDepartmentStatsResponse();
        response.setDepartments(departmentStatsList);
        return response;
    }

    /**
     * 获取员工考勤列表
     * @param query 查询条件
     * @return 员工考勤列表
     */
    @Override
    public AttendanceStaffResponse getStaffAttendance(AttendanceStaffQuery query) {
        log.info("Getting staff attendance for query: {}", query);
        
        // 解析月份参数，如果未提供则使用当前年月
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        
        if (query.getMonth() != null && !query.getMonth().isEmpty()) {
            try {
                String[] parts = query.getMonth().split("-");
                if (parts.length == 2) {
                    year = Integer.parseInt(parts[0]);
                    month = Integer.parseInt(parts[1]);
                }
            } catch (Exception e) {
                log.warn("Invalid month format: {}, using current month", query.getMonth());
            }
        }
        
        // 计算月份的起止时间
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        LocalDateTime startOfMonth = startDate.atStartOfDay();
        LocalDateTime endOfMonth = endDate.atTime(23, 59, 59);
        
        // 获取用户列表，根据部门ID筛选
        List<TUser> users;
        if (query.getDepartmentId() != null) {
            // 假设我们根据部门ID可以直接从数据库获取该部门的所有用户
            // 这里需要根据实际情况调整，因为原始代码中用户是按字符串存储部门
            users = userRepository.findAll().stream()
                .filter(user -> user.getDepartment() != null && 
                       query.getDepartmentId().toString().equals(user.getDepartment()))
                .collect(Collectors.toList());
        } else {
            users = userRepository.findAll();
        }
        
        // 统计每个用户的考勤情况
        List<AttendanceStaffResponse.StaffAttendance> staffAttendanceList = new ArrayList<>();
        
        for (TUser user : users) {
            // 获取用户在指定月份的考勤记录
            List<TAttendanceRecord> records = attendanceRecordRepository
                .findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                    user.getId(), startOfMonth, endOfMonth);
            
            // 根据状态筛选记录
            if (query.getStatus() != null && !query.getStatus().isEmpty()) {
                records = records.stream()
                    .filter(record -> query.getStatus().equals(record.getStatus()))
                    .collect(Collectors.toList());
            }
            
            // 计算工作日数量
            long totalDays = ChronoUnit.DAYS.between(startDate, endDate.plusDays(1));
            long weekends = 0;
            for (int i = 0; i < totalDays; i++) {
                LocalDate date = startDate.plusDays(i);
                if (date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                    weekends++;
                }
            }
            int workdays = (int) (totalDays - weekends);
            
            // 统计出勤、迟到、早退和缺勤
            Set<LocalDate> attendanceDates = new HashSet<>();
            int lateCount = 0;
            int earlyLeaveCount = 0;
            
            for (TAttendanceRecord record : records) {
                LocalDate recordDate = record.getClockTime().toLocalDate();
                attendanceDates.add(recordDate);
                
                if ("late".equals(record.getStatus())) {
                    lateCount++;
                } else if ("early".equals(record.getStatus())) {
                    earlyLeaveCount++;
                }
            }
            
            // 计算出勤率和缺勤次数
            int attendanceCount = attendanceDates.size();
            int absentCount = workdays - attendanceCount;
            if (absentCount < 0) absentCount = 0;
            
            double attendanceRate = workdays > 0 ? (double) attendanceCount / workdays : 0;
            
            // 构建员工考勤对象
            AttendanceStaffResponse.StaffAttendance staffAttendance = AttendanceStaffResponse.StaffAttendance.builder()
                .userId(user.getId())
                .userName(user.getName())
                .department(user.getDepartment())
                .position(handleMultipleRoles(user.getRole())) // 使用角色作为职位，处理多角色情况
                .attendanceRate(Math.round(attendanceRate * 100) / 100.0)
                .lateCount(lateCount)
                .earlyLeaveCount(earlyLeaveCount)
                .absentCount(absentCount)
                .build();
            
            staffAttendanceList.add(staffAttendance);
        }
        
        // 返回响应
        return AttendanceStaffResponse.builder()
            .total(staffAttendanceList.size())
            .list(staffAttendanceList)
            .build();
    }

    /**
     * 获取今日打卡记录
     * @param userId 用户ID
     * @return 今日打卡记录
     */
    @Override
    public AttendanceTodayResponse getTodayAttendance(Long userId) {
        log.info("Getting today's attendance for user ID: {}", userId);
        
        // 验证用户是否存在
        Optional<TUser> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 获取今天的日期范围
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(23, 59, 59);
        
        // 查询今日打卡记录
        List<TAttendanceRecord> todayRecords = attendanceRecordRepository
            .findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(userId, startOfDay, endOfDay);
        
        // 构建响应
        AttendanceTodayResponse response = new AttendanceTodayResponse();
        
        // 查找上班打卡记录
        Optional<TAttendanceRecord> clockInRecord = todayRecords.stream()
            .filter(record -> "checkin".equals(record.getClockType()))
            .findFirst();
        
        // 查找下班打卡记录
        Optional<TAttendanceRecord> clockOutRecord = todayRecords.stream()
            .filter(record -> "checkout".equals(record.getClockType()))
            .findFirst();
        
        // 设置上班打卡信息
        if (clockInRecord.isPresent()) {
            TAttendanceRecord record = clockInRecord.get();
            response.setClockInTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            response.setClockInStatus(record.getStatus());
            
            // 设置位置信息
            String location = String.format("%.6f,%.6f", record.getLatitude(), record.getLongitude());
            response.setLocation(location);
        }
        
        // 设置下班打卡信息
        if (clockOutRecord.isPresent()) {
            TAttendanceRecord record = clockOutRecord.get();
            response.setClockOutTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            response.setClockOutStatus(record.getStatus());
            
            // 如果没有上班打卡记录，则使用下班打卡的位置信息
            if (!clockInRecord.isPresent()) {
                String location = String.format("%.6f,%.6f", record.getLatitude(), record.getLongitude());
                response.setLocation(location);
            }
        }
        
        return response;
    }

    /**
     * 获取最近打卡记录
     * @param userId 用户ID
     * @param days 天数
     * @return 最近打卡记录
     */
    @Override
    public AttendanceRecentResponse getRecentAttendance(Long userId, Integer days) {
        log.info("Getting recent attendance for user ID: {} for the last {} days", userId, days);
        
        // 参数校验
        if (days == null || days <= 0) {
            days = 7; // 默认查询最近7天
        }
        
        // 验证用户是否存在
        Optional<TUser> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 计算日期范围
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        LocalDateTime startOfPeriod = startDate.atStartOfDay();
        LocalDateTime endOfPeriod = endDate.atTime(23, 59, 59);
        
        // 查询打卡记录
        List<TAttendanceRecord> records = attendanceRecordRepository
            .findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(userId, startOfPeriod, endOfPeriod);
        
        // 按天分组打卡记录
        Map<LocalDate, List<TAttendanceRecord>> recordsByDay = records.stream()
            .collect(Collectors.groupingBy(record -> record.getClockTime().toLocalDate()));
        
        // 构建响应
        List<AttendanceRecentResponse.DailyRecord> dailyRecords = new ArrayList<>();
        
        // 遍历日期范围内的每一天
        for (int i = 0; i < days; i++) {
            LocalDate date = endDate.minusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String weekStr = getWeekDayString(date.getDayOfWeek());
            
            // 获取当天打卡记录
            List<TAttendanceRecord> dayRecords = recordsByDay.getOrDefault(date, new ArrayList<>());
            
            // 查找上班打卡记录
            Optional<TAttendanceRecord> clockInRecord = dayRecords.stream()
                .filter(record -> "checkin".equals(record.getClockType()))
                .findFirst();
                
            // 查找下班打卡记录
            Optional<TAttendanceRecord> clockOutRecord = dayRecords.stream()
                .filter(record -> "checkout".equals(record.getClockType()))
                .findFirst();
            
            // 构建日打卡记录
            AttendanceRecentResponse.DailyRecord dailyRecord = new AttendanceRecentResponse.DailyRecord();
            dailyRecord.setDate(dateStr);
            dailyRecord.setWeek(weekStr);
            
            // 设置上班打卡信息
            if (clockInRecord.isPresent()) {
                TAttendanceRecord record = clockInRecord.get();
                dailyRecord.setClockInTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                dailyRecord.setClockInStatus(record.getStatus());
            }
            
            // 设置下班打卡信息
            if (clockOutRecord.isPresent()) {
                TAttendanceRecord record = clockOutRecord.get();
                dailyRecord.setClockOutTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                dailyRecord.setClockOutStatus(record.getStatus());
            }
            
            dailyRecords.add(dailyRecord);
        }
        
        // 构建响应
        AttendanceRecentResponse response = new AttendanceRecentResponse();
        response.setRecords(dailyRecords);
        
        return response;
    }
    
    /**
     * 获取星期几的中文表示
     * @param dayOfWeek 星期几
     * @return 中文表示
     */
    private String getWeekDayString(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "周一";
            case TUESDAY: return "周二";
            case WEDNESDAY: return "周三";
            case THURSDAY: return "周四";
            case FRIDAY: return "周五";
            case SATURDAY: return "周六";
            case SUNDAY: return "周日";
            default: return "";
        }
    }

    /**
     * 检查考勤范围
     * @param request 考勤范围检查请求
     * @return 考勤范围检查响应
     */
    @Override
    public AttendanceCheckAreaResponse checkAttendanceArea(AttendanceCheckAreaRequest request) {
        log.info("Checking attendance area for coordinates: {}, {}", request.getLatitude(), request.getLongitude());
        
        // 参数校验
        if (request.getLatitude() == null || request.getLongitude() == null) {
            throw new IllegalArgumentException("位置坐标不能为空");
        }
        
        // 考勤打卡地点坐标（这里使用硬编码，实际应该从配置或数据库获取）
        double companyLatitude = 34.2079; // 公司纬度
        double companyLongitude = 108.9649; // 公司经度
        int allowedDistance = 500; // 允许的打卡距离（米）
        
        // 计算距离（使用Haversine公式计算两点之间的球面距离）
        int distance = calculateDistance(
            request.getLatitude(), request.getLongitude(),
            companyLatitude, companyLongitude
        );
        
        // 判断是否在考勤范围内
        boolean inArea = distance <= allowedDistance;
        
        // 构建响应
        return AttendanceCheckAreaResponse.builder()
            .inArea(inArea)
            .distance(distance)
            .build();
    }
    
    /**
     * 计算两个经纬度坐标之间的距离（使用Haversine公式）
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 距离（米）
     */
    private int calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径（米）
        final double EARTH_RADIUS = 6371000;
        
        // 转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);
        
        // Haversine公式
        double dLat = lat2Rad - lat1Rad;
        double dLon = lon2Rad - lon1Rad;
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        double distance = EARTH_RADIUS * c;
        
        return (int) Math.round(distance);
    }
    
    /**
     * 获取打卡规则
     * @return 打卡规则
     */
    @Override
    public AttendanceRulesResponse getAttendanceRules() {
        log.info("Getting attendance rules from database");
        
        // 从数据库获取上下班时间设置，默认获取ID=1的记录
        TOtherParam systemParams = otherSystemRepository.findById(1L);
        
        // 如果没有找到记录，使用默认值
        LocalTime clockInTime = systemParams != null ? systemParams.getClockInTime() : MORNING_START_TIME;
        LocalTime clockOutTime = systemParams != null ? systemParams.getClockOutTime() : EVENING_END_TIME;
        
        // 构建响应
        return AttendanceRulesResponse.builder()
            .clockInTime(clockInTime.format(DateTimeFormatter.ofPattern("HH:mm")))
            .clockOutTime(clockOutTime.format(DateTimeFormatter.ofPattern("HH:mm")))
            .allowedDistance(500) // 允许的打卡距离（米）
            .lateThreshold(15) // 迟到阈值（分钟）
            .earlyLeaveThreshold(15) // 早退阈值（分钟）
            .build();
    }

    /**
     * 提交补卡申请
     * @param request 补卡申请请求
     * @return 补卡申请响应
     */
    @Override
    @Transactional
    public AttendanceSupplementResponse submitSupplement(AttendanceSupplementRequest request) {
        log.info("Submitting attendance supplement request: {}", request);
        
        // 参数校验
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        if (request.getDate() == null || request.getDate().isEmpty()) {
            throw new IllegalArgumentException("补卡日期不能为空");
        }
        
        if (request.getType() == null) {
            throw new IllegalArgumentException("补卡类型不能为空");
        }
        
        if (request.getReason() == null || request.getReason().isEmpty()) {
            throw new IllegalArgumentException("补卡原因不能为空");
        }
        
        // 验证用户是否存在
        Optional<TUser> userOpt = userRepository.findById(request.getUserId());
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 解析日期
        LocalDate supplementDate;
        try {
            supplementDate = LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式不正确，应为yyyy-MM-dd");
        }
        
        // 验证是否为过去的日期
        if (supplementDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("不能为未来日期补卡");
        }
        
        // 判断补卡类型对应的时间
        LocalDateTime clockTime;
        String clockType;
        
        if (request.getType() == 1) { // 上班打卡
            clockTime = supplementDate.atTime(MORNING_START_TIME);
            clockType = "checkin";
        } else if (request.getType() == 2) { // 下班打卡
            clockTime = supplementDate.atTime(EVENING_END_TIME);
            clockType = "checkout";
        } else {
            throw new IllegalArgumentException("补卡类型不正确，应为1（上班）或2（下班）");
        }
        
        // 检查是否已经有打卡记录
        List<TAttendanceRecord> existingRecords = attendanceRecordRepository
            .findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                request.getUserId(),
                supplementDate.atStartOfDay(),
                supplementDate.atTime(23, 59, 59)
            );
        
        for (TAttendanceRecord record : existingRecords) {
            if (clockType.equals(record.getClockType())) {
                throw new IllegalArgumentException("该时间段已有打卡记录，不能重复补卡");
            }
        }
        
        // 创建补卡记录
        TAttendanceRecord record = new TAttendanceRecord();
        record.setUserId(request.getUserId());
        record.setClockType(clockType);
        record.setClockTime(clockTime);
        record.setLatitude(0.0); // 补卡没有具体位置
        record.setLongitude(0.0);
        record.setStatus("pending"); // 补卡状态为待审批
        record.setLeaveReason(request.getReason());
        record.setLeaveProof(request.getImages());
        record.setCreateTime(LocalDateTime.now());
        
        // 保存补卡记录
        TAttendanceRecord savedRecord = attendanceRecordRepository.save(record);
        
        // 构建响应
        return AttendanceSupplementResponse.builder()
            .id(savedRecord.getId())
            .status(savedRecord.getStatus())
            .build();
    }

    /**
     * 更新上下班时间设置
     * @param clockInTime 上班时间
     * @param clockOutTime 下班时间
     * @return 更新后的打卡规则
     */
    @Override
    @Transactional
    public AttendanceRulesResponse updateAttendanceRules(String clockInTime, String clockOutTime) {
        log.info("Updating attendance rules: clockInTime={}, clockOutTime={}", clockInTime, clockOutTime);
        
        // 参数校验
        if (clockInTime == null || clockInTime.isEmpty()) {
            throw new IllegalArgumentException("上班时间不能为空");
        }
        
        if (clockOutTime == null || clockOutTime.isEmpty()) {
            throw new IllegalArgumentException("下班时间不能为空");
        }
        
        // 解析时间
        LocalTime parsedClockInTime;
        LocalTime parsedClockOutTime;
        
        try {
            parsedClockInTime = LocalTime.parse(clockInTime, DateTimeFormatter.ofPattern("HH:mm"));
            parsedClockOutTime = LocalTime.parse(clockOutTime, DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            throw new IllegalArgumentException("时间格式不正确，应为HH:mm格式");
        }
        
        // 获取ID=1的记录，如果不存在则创建
        TOtherParam systemParams = otherSystemRepository.findById(1L);
        if (systemParams == null) {
            systemParams = new TOtherParam();
            systemParams.setId(1L);
        }
        
        // 更新时间设置
        systemParams.setClockInTime(parsedClockInTime);
        systemParams.setClockOutTime(parsedClockOutTime);
        
        // 保存更新
        otherSystemRepository.save(systemParams);
        
        // 返回更新后的规则
        return AttendanceRulesResponse.builder()
            .clockInTime(parsedClockInTime.format(DateTimeFormatter.ofPattern("HH:mm")))
            .clockOutTime(parsedClockOutTime.format(DateTimeFormatter.ofPattern("HH:mm")))
            .allowedDistance(500) // 允许的打卡距离（米）
            .lateThreshold(15) // 迟到阈值（分钟）
            .earlyLeaveThreshold(15) // 早退阈值（分钟）
            .build();
    }

    /**
     * 按日期范围查询考勤记录
     * @param userId 用户ID（可选）
     * @param startDateStr 开始日期（格式：yyyy-MM-dd）
     * @param endDateStr 结束日期（格式：yyyy-MM-dd）
     * @param status 状态（可选）
     * @return 考勤记录响应
     */
    @Override
    public AttendanceRecordResponse getAttendanceRecordsByDateRange(Long userId, String startDateStr, String endDateStr, String status) {
        log.info("按日期范围查询考勤记录：userId={}, 日期范围={} 至 {}", userId, startDateStr, endDateStr);
        
        // 解析日期字符串
        LocalDate startDate;
        LocalDate endDate;
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            startDate = LocalDate.parse(startDateStr, formatter);
            endDate = LocalDate.parse(endDateStr, formatter);
        } catch (Exception e) {
            log.error("日期格式解析错误", e);
            throw new IllegalArgumentException("日期格式不正确，应为yyyy-MM-dd");
        }
        
        // 转换为日期时间范围
        LocalDateTime startOfPeriod = startDate.atStartOfDay();
        LocalDateTime endOfPeriod = endDate.atTime(23, 59, 59);
        
        // 查询考勤记录
        List<TAttendanceRecord> records;
        if (userId != null) {
            // 验证用户是否存在
            Optional<TUser> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            if (status != null && !status.isEmpty()) {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenAndStatusOrderByClockTimeDesc(
                        userId, startOfPeriod, endOfPeriod, status);
            } else {
                records = attendanceRecordRepository.findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
                        userId, startOfPeriod, endOfPeriod);
            }
        } else {
            if (status != null && !status.isEmpty()) {
                records = attendanceRecordRepository.findByClockTimeBetweenAndStatusOrderByClockTimeDesc(
                        startOfPeriod, endOfPeriod, status);
            } else {
                records = attendanceRecordRepository.findByClockTimeBetweenOrderByClockTimeDesc(
                        startOfPeriod, endOfPeriod);
            }
        }
        
        log.info("查询到 {} 条考勤记录", records.size());
        
        // 构建响应
        AttendanceRecordResponse response = new AttendanceRecordResponse();
        
        // 计算考勤统计数据
        Map<String, Object> summary = calculateAttendanceSummary(records, startDate, endDate);
        response.setSummary(summary);
        
        // 按用户ID和日期分组打卡记录
        Map<String, Map<LocalDate, List<TAttendanceRecord>>> recordsByUserAndDate = records.stream()
                .collect(Collectors.groupingBy(
                        record -> String.valueOf(record.getUserId()),
                        Collectors.groupingBy(record -> record.getClockTime().toLocalDate())
                ));
        
        // 转换考勤记录，合并同一天的上下班打卡
        List<AttendanceRecordResponse.AttendanceRecordDetail> recordDetails = new ArrayList<>();
        
        recordsByUserAndDate.forEach((userIdStr, userRecords) -> {
            userRecords.forEach((date, dailyRecords) -> {
                // 查找当天的上班打卡记录
                Optional<TAttendanceRecord> clockInRecord = dailyRecords.stream()
                        .filter(r -> "checkin".equals(r.getClockType()))
                        .findFirst();
                
                // 查找当天的下班打卡记录
                Optional<TAttendanceRecord> clockOutRecord = dailyRecords.stream()
                        .filter(r -> "checkout".equals(r.getClockType()))
                        .findFirst();
                
                // 创建合并后的记录
                AttendanceRecordResponse.AttendanceRecordDetail detail = new AttendanceRecordResponse.AttendanceRecordDetail();
                
                // 设置用户信息（从任一记录中获取）
                TAttendanceRecord anyRecord = dailyRecords.get(0);
                Optional<TUser> userOpt = userRepository.findById(anyRecord.getUserId());
                if (userOpt.isPresent()) {
                    TUser user = userOpt.get();
                    UserBasicInfo userInfo = new UserBasicInfo();
                    userInfo.setId(user.getId());
                    userInfo.setName(user.getName());
                    userInfo.setAvatar(user.getAvatar());
                    userInfo.setDepartment(user.getDepartment());
                    detail.setUser(userInfo);
                }
                
                // 设置日期作为记录标识
                detail.setCreateTime(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                
                // 设置日期和星期几
                detail.setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                detail.setWeek(getWeekDayString(date.getDayOfWeek()));
                
                // 设置上班打卡信息
                if (clockInRecord.isPresent()) {
                    TAttendanceRecord record = clockInRecord.get();
                    detail.setClockType("daily"); // 标记为日常打卡
                    detail.setClockTime(record.getClockTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    detail.setLatitude(record.getLatitude());
                    detail.setLongitude(record.getLongitude());
                    detail.setFacePhoto(record.getFacePhoto());
                    detail.setLivenessData(record.getLivenessData());
                    detail.setStatus(record.getStatus());
                    
                    // 设置上班打卡时间
                    detail.setClockInTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                    detail.setClockInStatus(record.getStatus());
                }
                
                // 添加下班打卡信息
                if (clockOutRecord.isPresent()) {
                    TAttendanceRecord record = clockOutRecord.get();
                    // 如果没有上班打卡记录，使用下班打卡的基本信息
                    if (!clockInRecord.isPresent()) {
                        detail.setClockType("daily");
                        detail.setClockTime(record.getClockTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        detail.setLatitude(record.getLatitude());
                        detail.setLongitude(record.getLongitude());
                        detail.setFacePhoto(record.getFacePhoto());
                        detail.setLivenessData(record.getLivenessData());
                        detail.setStatus(record.getStatus());
                    }
                    
                    // 设置下班打卡时间
                    detail.setClockOutTime(record.getClockTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                    detail.setClockOutStatus(record.getStatus());
                    
                    // 设置请假信息（如果有）
                    if (record.getLeaveType() != null && !record.getLeaveType().isEmpty()) {
                        detail.setLeaveType(record.getLeaveType());
                        detail.setLeaveReason(record.getLeaveReason());
                        detail.setLeaveProof(record.getLeaveProof());
                    }
                }
                
                recordDetails.add(detail);
            });
        });
        
        // 按日期降序排序
        recordDetails.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        response.setRecords(recordDetails);
        return response;
    }
}   
