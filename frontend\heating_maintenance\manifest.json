{
    "name" : "热擎云",
    "appid" : "__UNI__6A10D03",
    "description" : "热擎云 是专为暖通供热行业打造的一站式智能管理平台，致力于提升供热系统的能效水平与运维效率。平台融合物联网、大数据、人工智能与视频分析技术，面向供热企业、工程公司及运维单位，提供从能耗分析到设备监控的全链路数字化解决方案",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "VideoPlayer" : {},
            "Speech" : {},
            "Geolocation" : {},
            "Barcode" : {},
            "Camera" : {},
            "SQLite" : {},
            "Push" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "speech" : {},
                "maps" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "push" : {}
            },
            "icons" : {
                "ios" : {
                    "appstore" : ""
                },
                "android" : {
                    "xxxhdpi" : "static/images/02-128.png",
                    "xxhdpi" : "static/images/02-128.png",
                    "xhdpi" : "static/images/02-96.png",
                    "hdpi" : "static/images/02-96.png"
                }
            }
        },
        /* 网络请求跨域配置 */
        "network" : {
            "request" : {
                "timeout" : 10000,
                "header" : {
                    "Content-Type" : "application/json"
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans",
    /* H5特有配置 */
    "h5" : {
        "router" : {
            "mode" : "hash"
        },
        "devServer" : {
            "port" : 8888,
            "disableHostCheck" : true,
            "proxy" : {
                "/api" : {
                    "target" : "http://*************:8889",
                    "changeOrigin" : true,
                    "secure" : false,
                    "ws" : true,
                    "rewrite" : {
                        "^/api" : "/api"
                    },
                    "logLevel" : "debug"
                }
            }
        },
        /* 跨域配置 */
        "title" : "HEC",
        "domain" : "*************"
    }
}
