package com.heating.service.impl;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heating.dto.valve.ValveListRequest;
import com.heating.dto.valve.ValveControlRequest;
import com.heating.service.ValveService;

@Service
public class ValveServiceImpl implements ValveService {

    private static final Logger logger = LoggerFactory.getLogger(ValveServiceImpl.class);
    
    // 第三方接口
    // 入户阀门列表
    @Value("${valve.api.list}")
    private String valveApiList;
    
    // 入户阀门控制
    @Value("${valve.api.control}")
    private String valveApiControl;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 通用API调用方法
     * @param apiUrl API地址
     * @param request 请求对象
     * @return API响应结果
     */
    private List<Map<String, Object>> getApi(String apiUrl, Object request) {
        try {
            HttpClient client = HttpClient.newHttpClient();
            // 构建请求体
            String requestBody = "";
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                requestBody = objectMapper.writeValueAsString(request);
            } catch (Exception e) {
                throw new RuntimeException("Error converting request to JSON", e);
            }
            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> response = client.send(httpRequest,
                    HttpResponse.BodyHandlers.ofString());

            // 解析响应数据
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                // Parse the entire response as a Map
                Map<String, Object> responseMap = objectMapper.readValue(response.body(),
                        new TypeReference<Map<String, Object>>() {});

                // Check if the response is successful
                List<Map<String, Object>> resultList = new ArrayList<>();
                resultList.add(responseMap);
                return resultList;
            } catch (Exception e) {
                throw new RuntimeException("Error parsing response data: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error fetching HES data: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<Map<String, Object>> getValveList(ValveListRequest request) {
        return getApi(valveApiList, request);
    }
    
    @Override
    public Map<String, Object> controlValve(ValveControlRequest request) {
        return getApi(valveApiControl, request).get(0);
    }
} 