package com.heating.entity.system;

import lombok.Data;

import jakarta.persistence.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统参数实体类
 */
@Entity
@Table(name = "t_app_system_param")
@Data
public class TAppSystemParam {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 系统名称
     */
    @Column(name = "system_name")
    private String systemName;

    /**
     * 系统logo
     */
    @Column(name = "system_logo")
    private String systemLogo;

    /**
     * 系统版本
     */
    @Column(name = "system_versions")
    private String systemVersions;

    /**
     * 版权
     */
    private String copyright;

    /**
     * 公司
     */
    private String company;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 网址
     */
    @Column(name = "internet_addr")
    private String internetAddr;

    /**
     * 公司地址
     */
    @Column(name = "company_addr")
    private String companyAddr;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 系统简介
     */
    private String intro;
    /**
     * 发布日期
     */
    @Column(name = "release_date")
    private LocalDate releaseDate;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @Column(name = "create_user")
    private Integer createUser;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @Column(name = "update_user")
    private Integer updateUser;

    /**
     * 标记
     */
    private Boolean mark;
} 