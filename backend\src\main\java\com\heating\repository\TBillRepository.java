package com.heating.repository;

import com.heating.entity.bill.TBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TBillRepository extends JpaRepository<TBill, Long> {
    
    // 根据房屋ID查询所有账单，按供暖年份降序排列（不分页）
    List<TBill> findByHouseIdOrderByHeatYearDesc(Long houseId);
    
    // 根据房屋ID查询账单，按供暖年份降序排列（分页）
    Page<TBill> findByHouseIdOrderByHeatYearDesc(Long houseId, Pageable pageable);
    
    // 根据房屋ID和状态查询账单，按供暖年份降序排列（分页）
    @Query("SELECT b FROM TBill b WHERE b.houseId = :houseId AND b.status = :status ORDER BY b.heatYear DESC")
    Page<TBill> findByHouseIdAndStatus(@Param("houseId") Long houseId, @Param("status") TBill.BillStatus status, Pageable pageable);
    
    // 根据房屋ID和供暖年份查询账单
    @Query("SELECT b FROM TBill b WHERE b.houseId = :houseId AND b.heatYear = :heatYear")
    Optional<TBill> findByHouseIdAndHeatYear(@Param("houseId") Long houseId, @Param("heatYear") Integer heatYear);
    
    // 添加调试用的查询方法
    @Query("SELECT COUNT(b) FROM TBill b WHERE b.houseId = :houseId")
    long countByHouseId(@Param("houseId") Long houseId);

    /**
     * 根据房屋ID查询历史年度账单（当前年度之前的账单）
     * 用于计算历史欠费金额
     * @param houseId 房屋ID
     * @param currentHeatingYear 当前供暖年度
     * @return 历史账单列表，按年度降序排列
     */
    @Query("SELECT b FROM TBill b WHERE b.houseId = :houseId AND b.heatYear < :currentHeatingYear ORDER BY b.heatYear DESC")
    List<TBill> findByHouseIdAndHeatYearLessThanOrderByHeatYearDesc(@Param("houseId") Long houseId, @Param("currentHeatingYear") Integer currentHeatingYear);
}
