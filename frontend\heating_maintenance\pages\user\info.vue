<template>
  <view class="user-info-container">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-header-bg"></view>
      <view class="avatar-container">
        <image class="avatar" :src="getFullImageUrl(userInfo.avatar)"></image>
        <view class="edit-avatar" @click="updateAvatar">
          <text class="iconfont icon-camera"></text>
        </view>
      </view>

      <view class="user-details">
        <view class="user-name">{{ userInfo.name }}</view>
     <!--   <view class="user-role">
          <text class="role-badge">{{ userInfo.role }}</text>
        </view> -->
      </view>
    </view>

    <!-- 工作统计 -->
<!--   <view class="work-stats">
      <view class="stat-item">
        <text class="stat-value">{{ workStats.monthlyTasks }}</text>
        <text class="stat-label">本月工单</text>
      </view>
     <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ workStats.averageRating }}</text>
        <text class="stat-label">已完成工单</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ workStats.attendanceRate }}</text>
        <text class="stat-label">超时工单</text>
      </view>
    </view> -->

    <!-- 快速入口 -->
  <!--  <view class="quick-actions">
      <view class="quick-grid">
        <view class="quick-item" @click="navigateTo('/pages/workorder/list')">
          <view class="quick-icon-bg">
            <text class="iconfont icon-task"></text>
          </view>
          <text class="item-text">我的工单</text>
        </view>
        <view class="quick-item" @click="navigateTo('/pages/patrol/records')">
          <view class="quick-icon-bg">
            <text class="iconfont icon-patrol"></text>
          </view>
          <text class="item-text">巡检记录</text>
        </view>
        <view class="quick-item" @click="navigateTo('/pages/device/list')">
          <view class="quick-icon-bg">
            <text class="iconfont icon-device"></text>
          </view>
          <text class="item-text">设备管理</text>
        </view>
        <view class="quick-item" @click="navigateTo('/pages/fault/list')">
          <view class="quick-icon-bg">
            <text class="iconfont icon-fault"></text>
          </view>
          <text class="item-text">故障列表</text>
        </view>
      </view>
    </view> -->

    <!-- 功能列表 -->
    <view class="feature-list">
      <!-- 个人设置 -->
      <view class="feature-section">
        <view class="section-title">
          <text class="section-icon iconfont icon-settings"></text>
          个人设置
        </view>

        <view class="menu-item" @click="navigateTo('/pages/user/profile')">
          <view class="menu-icon-container profile-icon">
            <text class="iconfont icon-user"></text>
          </view>
          <view class="menu-content">个人资料</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>

        <view class="menu-item" @click="navigateTo('/pages/user/message-settings')">
          <view class="menu-icon-container message-icon">
            <text class="iconfont icon-notification"></text>
          </view>
          <view class="menu-content">消息设置</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>

      <!-- 安全设置 -->
      <view class="feature-section">
        <view class="section-title">
          <text class="section-icon iconfont icon-shield"></text>
          安全设置
        </view>

        <view class="menu-item" @click="navigateTo('/pages/user/change-password')">
          <view class="menu-icon-container security-icon">
            <text class="iconfont icon-lock"></text>
          </view>
          <view class="menu-content">密码修改</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>

     <!--   <view class="menu-item" @click="navigateTo('/pages/user/account-binding')">
          <view class="menu-icon-container binding-icon">
            <text class="iconfont icon-phone"></text>
          </view>
          <view class="menu-content">账号绑定</view>
          <text class="iconfont icon-arrow-right"></text>
        </view> -->
      </view>

      <!-- 帮助与支持 -->
      <view class="feature-section">
        <view class="section-title">
          <text class="section-icon iconfont icon-support"></text>
          帮助与支持
        </view>

        <view class="menu-item" @click="navigateTo('/pages/user/faq')">
          <view class="menu-icon-container help-icon">
            <text class="iconfont icon-help"></text>
          </view>
          <view class="menu-content">常见问题</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>

        <view class="menu-item" @click="navigateTo('/pages/user/about')">
          <view class="menu-icon-container about-icon">
            <text class="iconfont icon-info"></text>
          </view>
          <view class="menu-content">关于系统</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>

        <!-- 新增：消息服务测试按钮 -->
   <!--     <view class="menu-item" @click="navigateTo('/pages/messageTest/messageTest')">
          <view class="menu-icon-container message-test-icon">
            <text class="iconfont icon-message"></text>
          </view>
          <view class="menu-content">消息服务测试</view>
          <text class="iconfont icon-arrow-right"></text>
        </view> -->
        <!-- 新增结束 -->
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-btn" @click="handleLogout">退出登录</view>

    <!-- 添加自定义TabBar -->
    <BaseTabBar></BaseTabBar>
  </view>
</template>

<script>
import { userApi } from "../../utils/api";
import BaseTabBar from "@/components/BaseTabBar.vue";
import uploadUtils from "@/utils/upload.js";

export default {
  components: {
    BaseTabBar,
  },
  data() {
    return {
      userInfo: {},
      workStats: {
        monthlyTasks: 23,
        averageRating: 4.8,
        attendanceRate: "100%",
      },
      isUploading: false,
    };
  },
  onShow() {
    // 每次显示页面时都加载用户信息，确保数据最新
    this.loadUserInfo();
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      // 从本地存储获取用户信息
      const userInfo = uni.getStorageSync("userInfo");
      const userId = uni.getStorageSync("userId");
      const userRole = uni.getStorageSync("userRole");

      if (userInfo) {
        console.log("本地存储的用户信息:", userInfo);
        // 更新数据
        this.userInfo = {
          name: userInfo.user.name || userInfo.user.username || "用户",
          avatar: userInfo.user.avatar ? uploadUtils.getFileUrl(userInfo.user.avatar) : "/static/user/avatar.png",
          role: this.getRoleName(userRole) || "普通用户",
          id: userId || "USER-" + Date.now(),
        };
      } else {
        // 如果本地没有用户信息，从服务器获取
        this.fetchUserInfoFromServer();
      }
    },

    // 从服务器获取用户信息
    fetchUserInfoFromServer() {
      uni.showLoading({
        title: "加载中...",
      });

      userApi
        .getUserInfo()
        .then((res) => {
          uni.hideLoading();
          if (res.code === 200) {
            // 存储用户信息
            uni.setStorageSync("userInfo", res.data);
             
            // 更新数据
            this.userInfo = {
              name: res.data.name || res.data.username || "用户",
              avatar: res.data.avatar ? uploadUtils.getFileUrl(res.data.avatar) : "/static/user/avatar.png",
              role: this.getRoleName(res.data.role) || "普通用户",
              id: res.data.userId || "USER-" + Date.now(),
            };
          } else {
            this.showError("获取用户信息失败");
          }
        })
        .catch((err) => {
          uni.hideLoading();
          console.error("获取用户信息失败:", err);
          this.showError("网络错误，请稍后重试");
        });
    },

    // 角色名称转换
    getRoleName(role) {
      const roleMap = {
        admin: "系统管理员",
        manager: "主管",
        engineer: "维修工程师",
        operator: "操作员",
        user: "普通用户",
      };

      return roleMap[role] || role || "普通用户";
    },

    // 显示错误提示
    showError(message) {
      uni.showToast({
        title: message,
        icon: "none",
      });
    },

    // 更新头像
    updateAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          // 显示上传中提示
          uni.showLoading({
            title: '上传中...',
            mask: true
          });
          
          this.isUploading = true;
          
          // 检查token是否存在，不存在则提示用户登录
          const token = uni.getStorageSync('token');
          if (!token) {
            uni.hideLoading();
            uni.showToast({
              title: '请先登录',
              icon: 'none'
            });
            return;
          }
          
          // 使用uploadUtils上传头像
          uploadUtils.uploadImage(tempFilePaths[0])
            .then(serverPath => {
              console.log('头像上传成功，服务器路径:', serverPath);
              
              // 更新本地显示 - 使用临时文件路径以立即显示效果
              this.userInfo.avatar = tempFilePaths[0];
              
              // 调用API更新用户头像信息
              return userApi.updateUserInfo({
                avatar: "/uploads"+serverPath
              });
            })
            .then(res => {
              if (res.code === 200) {
                // 更新本地存储的用户信息 - 使用服务器路径
                const userInfo = uni.getStorageSync('userInfo') || {};
                userInfo.avatar = res.data?.avatar || userInfo.avatar;
                uni.setStorageSync('userInfo', userInfo);
                
                uni.showToast({
                  title: '头像更新成功',
                  icon: 'success'
                });
				
				
              } else {
                uni.showToast({
                  title: res.message || '更新头像失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              console.error('上传头像失败:', err);
              uni.showToast({
                title: '上传失败，请重试',
                icon: 'none'
              });
            })
            .finally(() => {
              uni.hideLoading();
              this.isUploading = false;
            });
        },
      });
    },

    // 页面导航
    navigateTo(url) {
      uni.navigateTo({
        url: url,
      });
    },

    // 退出登录
    handleLogout() {
      uni.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储的登录信息
            uni.removeStorageSync("token");
            uni.removeStorageSync("userInfo");
            uni.removeStorageSync("userId");
            uni.removeStorageSync("userRole");
            uni.removeStorageSync("userPermissions");

            // 重定向到登录页
            uni.reLaunch({
              url: "/pages/user/login",
            });
          }
        },
      });
    },

    // 获取完整的图片URL
    getFullImageUrl(url) {
      if (!url) return '/static/user/avatar.png';
      
      // 如果已经是完整URL，直接返回
      if (url.startsWith('http') || url.startsWith('/static/')) {
        return url;
      }
      
      // 使用uploadUtils获取完整URL
      return uploadUtils.getFileUrl(url);
    },
  },
};
</script>

<style lang="scss">
.user-info-container {
  padding-bottom: 50rpx;
  background-color: #f5f7fa;
}

.user-header {
  background-image: linear-gradient(135deg, #1890ff, #0076e4);
  padding: 120rpx 30rpx 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;

  .user-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="15" fill="rgba(255,255,255,0.05)"/><circle cx="70" cy="70" r="25" fill="rgba(255,255,255,0.05)"/><circle cx="100" cy="30" r="20" fill="rgba(255,255,255,0.05)"/></svg>');
    background-size: 200rpx;
    opacity: 0.8;
  }

  .avatar-container {
    position: relative;
    margin-bottom: 20rpx;
    z-index: 1;

    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      border: 6rpx solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
    }

    .edit-avatar {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 56rpx;
      height: 56rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);

      .iconfont {
        font-size: 30rpx;
        color: $uni-color-primary;
      }
    }
  }

  .user-details {
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    z-index: 1;

    .user-name {
      font-size: 44rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    .user-role {
      margin-bottom: 12rpx;

      .role-badge {
        background-color: rgba(255, 255, 255, 0.25);
        padding: 8rpx 24rpx;
        border-radius: 30rpx;
        font-size: 26rpx;
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
      }
    }

    .user-id {
      font-size: 26rpx;
      opacity: 0.9;
      letter-spacing: 1rpx;
    }
  }
}

.work-stats {
  margin-top: -50rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.1);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
  z-index: 10;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0;

    .stat-value {
      font-size: 48rpx;
      font-weight: bold;
      color: $uni-color-primary;
      margin-bottom: 12rpx;
      line-height: 1;
    }

    .stat-label {
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
    }
  }

  .stat-divider {
    width: 2rpx;
    height: 70rpx;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.02),
      rgba(0, 0, 0, 0.1),
      rgba(0, 0, 0, 0.02)
    );
  }
}

.quick-actions {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);

  .quick-grid {
    display: flex;
    justify-content: space-around;

    .quick-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15rpx 0;
      transition: transform 0.2s;

      &:active {
        transform: scale(0.95);
      }

      .quick-icon-bg {
        width: 110rpx;
        height: 110rpx;
        border-radius: 30rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 15rpx;
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.1),
          rgba(24, 144, 255, 0.2)
        );
        box-shadow: 0 6rpx 15rpx rgba(24, 144, 255, 0.1);

        .iconfont {
          font-size: 52rpx;
          color: $uni-color-primary;
        }

        &:nth-child(1) {
          background: linear-gradient(
            135deg,
            rgba(24, 144, 255, 0.1),
            rgba(24, 144, 255, 0.2)
          );
        }
      }

      &:nth-child(1) .quick-icon-bg {
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.1),
          rgba(24, 144, 255, 0.2)
        );
      }

      &:nth-child(2) .quick-icon-bg {
        background: linear-gradient(
          135deg,
          rgba(82, 196, 26, 0.1),
          rgba(82, 196, 26, 0.2)
        );

        .iconfont {
          color: #52c41a;
        }
      }

      &:nth-child(3) .quick-icon-bg {
        background: linear-gradient(
          135deg,
          rgba(250, 140, 22, 0.1),
          rgba(250, 140, 22, 0.2)
        );

        .iconfont {
          color: #fa8c16;
        }
      }

      &:nth-child(4) .quick-icon-bg {
        background: linear-gradient(
          135deg,
          rgba(245, 34, 45, 0.1),
          rgba(245, 34, 45, 0.2)
        );

        .iconfont {
          color: #f5222d;
        }
      }

      .item-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.feature-list {
	margin-top: 20rpx;;
  .feature-section {
    margin: 0 30rpx 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      padding: 0 20rpx;
      display: flex;
      align-items: center;

      .section-icon {
        font-size: 32rpx;
        color: $uni-color-primary;
        margin-right: 10rpx;
      }
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      transition: background-color 0.2s;

      &:active {
        background-color: rgba(0, 0, 0, 0.02);
      }

      &:last-child {
        border-bottom: none;
      }

      .menu-icon-container {
        width: 70rpx;
        height: 70rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;
        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);

        .iconfont {
          font-size: 36rpx;
          color: #fff;
        }

        &.profile-icon {
          background: linear-gradient(135deg, #1890ff, #36b3ff);
        }

        &.message-icon {
          background: linear-gradient(135deg, #52c41a, #73d13d);
        }

        &.security-icon {
          background: linear-gradient(135deg, #fa8c16, #ffa940);
        }

        &.binding-icon {
          background: linear-gradient(135deg, #722ed1, #9254de);
        }

        &.help-icon {
          background: linear-gradient(135deg, #13c2c2, #36cfc9);
        }

        &.about-icon {
          background: linear-gradient(135deg, #eb2f96, #f759ab);
        }

        &.message-test-icon {
          background: linear-gradient(135deg, #13c2c2, #36cfc9);
        }
      }

      .menu-content {
        flex: 1;
        font-size: 30rpx;
      }

      .iconfont.icon-arrow-right {
        font-size: 28rpx;
        color: #ccc;
      }
    }
  }
}

.logout-btn {
  margin: 50rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #fff;
  color: $uni-color-error;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
  font-weight: bold;
  &:active {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(2rpx);
  }
}
</style>
