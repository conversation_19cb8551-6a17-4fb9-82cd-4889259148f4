package com.heating.repository;

import com.heating.entity.PatrolItemDictionary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 巡检项目字典数据访问接口
 */
@Repository
public interface PatrolItemDictionaryRepository extends JpaRepository<PatrolItemDictionary, Long> {
    
    /**
     * 根据类别ID查询巡检项目字典
     * @param categoryId 类别ID
     * @return 巡检项目字典列表
     */
    List<PatrolItemDictionary> findByCategoryId(Long categoryId);
    
    /**
     * 根据是否启用状态查询巡检项目字典
     * @param isActive 是否启用
     * @return 巡检项目字典列表
     */
    List<PatrolItemDictionary> findByIsActive(Boolean isActive);
    
    /**
     * 根据类别ID和是否启用状态查询巡检项目字典
     * @param categoryId 类别ID
     * @param isActive 是否启用
     * @return 巡检项目字典列表
     */
    List<PatrolItemDictionary> findByCategoryIdAndIsActive(Long categoryId, Boolean isActive);
}