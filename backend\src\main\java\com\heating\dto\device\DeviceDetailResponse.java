package com.heating.dto.device;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class DeviceDetailResponse {
    private BasicInfo basicInfo;
    private LocationInfo location;
    private MaintenanceInfo maintenance;
    private RealTimeData realTimeData;
    private String manufacturer;
    private Integer period;
    private OperationLogs operationLogs;

    @Data
    public static class BasicInfo {
        private String deviceId;
        private String name;
        private String model;
        private String type; 
        private String status;
        private String manufacturer; 
        private Integer period;
        private String[] photos;
    }

    @Data
    public static class LocationInfo {
        private String building;
        private String floor;
        private String room;
        private Coordinates coordinates;

        @Data
        public static class Coordinates {
            private Double lat;
            private Double lng;
        }
    }

    @Data
    public static class MaintenanceInfo {
        private String lastTime;
        private String nextTime;
        private Integer period;
        private Integer remainingDays;
    }

    @Data
    public static class RealTimeData {
        private ParameterInfo temperature;
        private ParameterInfo pressure;
        private ParameterInfo flow;

        @Data
        public static class ParameterInfo {
            private BigDecimal value;
            private String unit;
            private Range range;
            private Boolean warning;

            @Data
            public static class Range {
                private BigDecimal min;
                private BigDecimal max;
            }
        }
    }

    @Data
    public static class OperationLogs {
        private String time;
        private String operator;
        private String action;
        private String detail;
    }
} 