<template>
	<view class="payment-container">
		<!-- 用户信息卡片 -->
		<view class="user-info-card">
			<view class="card-header">
				<text class="card-title">用户信息</text>
			</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">用户姓名</text>
					<text class="value">{{ userInfo.name }}</text>
				</view>
				<view class="info-item">
					<text class="label">小区名称</text>
					<text class="value">{{ userInfo.community }}</text>
				</view>
				<view class="info-item">
					<text class="label">房间号</text>
					<text class="value">{{ userInfo.roomNumber }}</text>
				</view>
				<view class="info-item">
					<text class="label">供热年度</text>
					<text class="value">{{ userInfo.heatingYear }}</text>
				</view>
			</view>
		</view>
		
		<!-- 缴费信息卡片 -->
		<view class="payment-info-card">
			<view class="card-header">
				<text class="card-title">缴费信息</text>
			</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">供热面积</text>
					<text class="value">{{ paymentInfo.area }} m²</text>
				</view>
				<view class="info-item">
					<text class="label">供热单价</text>
					<text class="value">¥{{ paymentInfo.unitPrice }}/m²</text>
				</view>
				<view class="info-item highlight">
					<text class="label">应缴金额</text>
					<text class="value amount">¥{{ paymentInfo.totalAmount }}</text>
				</view>
			</view>
		</view>
		
		<!-- 支付方式选择 -->
		<view class="payment-method-card">
			<view class="card-header">
				<text class="card-title">支付方式</text>
			</view>
			<view class="method-list">
				<view 
					class="method-item" 
					:class="{ active: selectedMethod === method.id }"
					v-for="method in paymentMethods" 
					:key="method.id"
					@click="selectPaymentMethod(method.id)"
				>
					<image class="method-icon" :src="method.icon" mode="aspectFit"></image>
					<text class="method-name">{{ method.name }}</text>
					<view class="check-icon" v-if="selectedMethod === method.id">
						<text class="iconfont icon-check">✓</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 缴费按钮 -->
		<view class="payment-action">
			<view class="total-amount">
				<text class="label">实付金额：</text>
				<text class="amount">¥{{ paymentInfo.totalAmount }}</text>
			</view>
			<button class="pay-button" @click="handlePayment" :disabled="!selectedMethod">
				立即缴费
			</button>
		</view>
		
		<!-- 加载提示 -->
		<uni-load-more v-if="loading" status="loading" :content-text="loadingText"></uni-load-more>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			loadingText: {
				contentdown: '正在加载...',
				contentrefresh: '加载中...',
				contentnomore: '没有更多数据了'
			},
			
			// 用户信息
			userInfo: {
				name: '张三',
				community: '金色家园',
				roomNumber: '1号楼2单元303',
				heatingYear: '2023-2024'
			},
			
			// 缴费信息
			paymentInfo: {
				area: 120,
				unitPrice: 30,
				totalAmount: 3600
			},
			
			// 支付方式
			selectedMethod: '',
			paymentMethods: [
				{
					id: 'wxpay',
					name: '微信支付',
					icon: '/static/images/payment/wxpay.png'
				},
				{
					id: 'alipay',
					name: '支付宝',
					icon: '/static/images/payment/alipay.png'
				}
			]
		}
	},
	
	onLoad(options) {
		// 页面加载时获取用户缴费信息
		this.getPaymentInfo()
	},
	
	methods: {
		// 获取缴费信息
		getPaymentInfo() {
			this.loading = true
			
			// 模拟API请求
			setTimeout(() => {
				// 实际项目中这里应该调用后端API
				this.loading = false
			}, 1000)
		},
		
		// 选择支付方式
		selectPaymentMethod(methodId) {
			this.selectedMethod = methodId
		},
		
		// 处理缴费
		handlePayment() {
			if (!this.selectedMethod) {
				uni.showToast({
					title: '请选择支付方式',
					icon: 'none'
				})
				return
			}
			
			uni.showLoading({
				title: '正在处理...'
			})
			
			// 模拟支付处理
			setTimeout(() => {
				uni.hideLoading()
				
				// 支付成功后的处理
				uni.showModal({
					title: '支付成功',
					content: '您的供暖费已缴纳成功！',
					showCancel: false,
					success: () => {
						// 返回上一页或跳转到缴费记录页面
						uni.navigateBack()
					}
				})
			}, 2000)
		}
	}
}
</script>

<style lang="scss">
.payment-container {
	min-height: 100vh;
	padding: 30rpx;
	background-color: #f8f8f8;
	
	.page-header {
		margin-bottom: 30rpx;
		
		.page-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.user-info-card,
	.payment-info-card,
	.payment-method-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.card-header {
			margin-bottom: 20rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
		}
		
		.info-content {
			.info-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1px solid #f5f5f5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.label {
					font-size: 28rpx;
					color: #666;
				}
				
				.value {
					font-size: 28rpx;
					color: #333;
					
					&.amount {
						font-size: 36rpx;
						color: #f5222d;
						font-weight: bold;
					}
				}
				
				&.highlight {
					margin-top: 20rpx;
					padding-top: 30rpx;
					border-top: 1px solid #e8e8e8;
				}
			}
		}
	}
	
	.payment-method-card {
		.method-list {
			.method-item {
				display: flex;
				align-items: center;
				padding: 30rpx 20rpx;
				border-bottom: 1px solid #f5f5f5;
				position: relative;
				
				&:last-child {
					border-bottom: none;
				}
				
				&.active {
					background-color: #f6f7f9;
				}
				
				.method-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 20rpx;
				}
				
				.method-name {
					font-size: 28rpx;
					color: #333;
				}
				
				.check-icon {
					position: absolute;
					right: 20rpx;
					color: #1890ff;
					font-size: 32rpx;
				}
			}
		}
	}
	
	.payment-action {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.total-amount {
			.label {
				font-size: 28rpx;
				color: #666;
			}
			
			.amount {
				font-size: 36rpx;
				color: #f5222d;
				font-weight: bold;
			}
		}
		
		.pay-button {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			background-color: #1890ff;
			color: #fff;
			font-size: 28rpx;
			border-radius: 40rpx;
			
			&:active {
				background-color: #096dd9;
			}
			
			&[disabled] {
				background-color: #d9d9d9;
				color: #fff;
			}
		}
	}
}
</style> 