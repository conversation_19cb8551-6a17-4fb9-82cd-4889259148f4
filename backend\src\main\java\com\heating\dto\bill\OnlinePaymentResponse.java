package com.heating.dto.bill;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 在线缴费响应DTO
 */
@Data
public class OnlinePaymentResponse {
    
    /**
     * 缴费记录ID
     */
    private Long paymentId;
    
    /**
     * 账单ID
     */
    private Long billId;
    
    /**
     * 缴费金额
     */
    private BigDecimal amount;
    
    /**
     * 支付方式
     */
    private String paymentMethod;
    
    /**
     * 支付方式文本
     */
    private String paymentMethodText;
    
    /**
     * 第三方交易号
     */
    private String transactionNo;
    
    /**
     * 缴费时间
     */
    private String paymentDate;
    
    /**
     * 账单状态
     */
    private String billStatus;
    
    /**
     * 账单状态文本
     */
    private String billStatusText;
    
    /**
     * 剩余未缴金额
     */
    private BigDecimal remainingAmount;
    
    /**
     * 是否完全缴清
     */
    private Boolean isFullyPaid;
}
