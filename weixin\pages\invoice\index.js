Page({
  data: {
    invoiceList: []
  },

  onLoad() {
    this.loadInvoiceList();
  },

  onShow() {
    this.loadInvoiceList();
  },

  loadInvoiceList() {
    wx.showLoading({
      title: '加载票据中...'
    });

    // 模拟从后端获取票据列表
    setTimeout(() => {
      wx.hideLoading();
      
      const invoiceList = [
        {
          id: 'inv_2024_001',
          period: '2024-2025',
          amount: '2,400.00',
          createTime: '2024-10-05',
          invoiceNo: 'PJ20241005001'
        },
        {
          id: 'inv_2023_001',
          period: '2023-2024',
          amount: '2,400.00',
          createTime: '2023-11-05',
          invoiceNo: 'PJ20231105001'
        }
      ];

      this.setData({
        invoiceList: invoiceList
      });
    }, 1000);
  },

  previewInvoice(e) {
    const invoiceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/invoice/preview?id=' + invoiceId
    });
  },

  downloadPDF(e) {
    const invoiceId = e.currentTarget.dataset.id;
    
    wx.showLoading({
      title: '生成PDF中...'
    });

    // 模拟PDF生成
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: 'PDF已保存到相册',
        icon: 'success'
      });
    }, 2000);
  },

  shareInvoice(e) {
    const invoiceId = e.currentTarget.dataset.id;
    
    wx.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '复制票据链接'],
      success: (res) => {
        const actions = ['微信好友', '朋友圈', '复制链接'];
        wx.showToast({
          title: `已分享到${actions[res.tapIndex]}`,
          icon: 'success'
        });
      }
    });
  },

  goToBill() {
    wx.navigateTo({
      url: '/pages/bill/index'
    });
  }
});