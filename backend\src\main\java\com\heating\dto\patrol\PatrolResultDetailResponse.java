package com.heating.dto.patrol;

import java.util.List;
import lombok.Data;

/**
 * 巡检结果详情响应
 * 严格按照接口设计文档的字段要求
 */
@Data
public class PatrolResultDetailResponse {
    private Long id;
    private Long patrolItemId;
    private String itemName;
    private Long deviceId;
    private String deviceName;
    private String deviceType;
    private String categoryName;
    private String checkMethod;
    private String checkResult;
    private String paramValue;
    private String paramType; 
    private String unit;
    private String description;
    private String checkDescription;
    private List<String> images;
    private Double latitude;
    private Double longitude;
    private String createTime;
    private String updateTime;
} 