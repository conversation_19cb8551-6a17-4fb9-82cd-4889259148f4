package com.heating.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDate;
public record ChangePasswordRequest(
    @NotEmpty(message = "原密码不能为空")
    String oldPassword,
    
    @NotEmpty(message = "新密码不能为空")
    @Size(min = 6, message = "密码长度不能少于6个字符")
    String newPassword
) {
    public ChangePasswordRequest {
        if (oldPassword != null) oldPassword = oldPassword.trim();
        if (newPassword != null) newPassword = newPassword.trim();
    }
} 