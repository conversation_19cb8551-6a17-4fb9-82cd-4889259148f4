package com.heating.service.impl;

import com.heating.dto.bill.OnlinePaymentRequest;
import com.heating.dto.bill.OnlinePaymentResponse;
import com.heating.dto.payment.PaymentSubmitRequest;
import com.heating.dto.payment.PaymentSubmitResponse;
import com.heating.entity.House;
import com.heating.entity.bill.TBill;
import com.heating.entity.bill.TPayment;
import com.heating.entity.bill.TOverdueRecord;
import com.heating.repository.HouseRepository;
import com.heating.repository.TBillRepository;
import com.heating.repository.TPaymentRepository;
import com.heating.repository.TOverdueRecordRepository;
import com.heating.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 缴费服务实现类
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private TPaymentRepository paymentRepository;

    @Autowired
    private TBillRepository billRepository;

    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private TOverdueRecordRepository overdueRecordRepository;

    @Override
    @Transactional
    public OnlinePaymentResponse processOnlinePayment(OnlinePaymentRequest request) {
        try {
            log.info("开始处理在线缴费: {}", request);

            // 验证请求参数
            validatePaymentRequest(request);

            // 查询账单信息
            TBill bill = getBillById(request.getBillId());

            // 验证房屋信息
            House house = getHouseById(request.getHouseId());

            // 验证账单是否属于该房屋
            if (!bill.getHouseId().equals(request.getHouseId())) {
                throw new RuntimeException("账单与房屋信息不匹配");
            }

            // 验证缴费金额
            validatePaymentAmount(bill, request.getAmount());

            // 创建缴费记录
            TPayment payment = createPaymentRecord(request, bill, house);

            // 更新账单状态
            updateBillStatus(bill, request.getAmount());

            // 根据前端传递的用热状态更新房屋的is_heating状态
            if (request.getIsHeating() != null) {
                boolean updateSuccess = updateHouseHeatingStatus(house, request.getIsHeating());
                if (!updateSuccess) {
                    log.warn("更新房屋用热状态失败，但不影响缴费流程: houseId={}, isHeating={}",
                            house.getId(), request.getIsHeating());
                }
            } else {
                log.warn("缴费请求中未包含用热状态信息: billId={}", request.getBillId());
            }

            // 构建响应
            OnlinePaymentResponse response = buildPaymentResponse(payment, bill);

            log.info("在线缴费处理成功: paymentId={}, billId={}, amount={}, isHeating={}",
                    payment.getId(), bill.getId(), request.getAmount(), request.getIsHeating());

            return response;

        } catch (Exception e) {
            log.error("在线缴费处理失败: {}", e.getMessage(), e);
            throw new RuntimeException("缴费处理失败: " + e.getMessage());
        }
    }

    @Override
    public void validatePaymentRequest(OnlinePaymentRequest request) {
        if (request.getBillId() == null) {
            throw new RuntimeException("账单ID不能为空");
        }
        if (request.getHouseId() == null) {
            throw new RuntimeException("房屋ID不能为空");
        }
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("缴费金额必须大于0");
        }
        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new RuntimeException("支付方式不能为空");
        }
        if (request.getTransactionNo() == null || request.getTransactionNo().trim().isEmpty()) {
            throw new RuntimeException("第三方交易号不能为空");
        }
        // 验证用热状态参数（可选，但如果提供则必须是有效值）
        if (request.getIsHeating() != null && request.getIsHeating() != 0 && request.getIsHeating() != 1) {
            throw new RuntimeException("用热状态参数无效，必须是0（不用热）或1（用热）");
        }
    }

    /**
     * 根据ID获取账单信息
     */
    private TBill getBillById(Long billId) {
        Optional<TBill> billOpt = billRepository.findById(billId);
        if (!billOpt.isPresent()) {
            throw new RuntimeException("账单不存在");
        }
        return billOpt.get();
    }

    /**
     * 根据ID获取房屋信息
     */
    private House getHouseById(Long houseId) {
        Optional<House> houseOpt = houseRepository.findById(houseId);
        if (!houseOpt.isPresent()) {
            throw new RuntimeException("房屋信息不存在");
        }
        return houseOpt.get();
    }

    /**
     * 验证缴费金额
     */
    private void validatePaymentAmount(TBill bill, BigDecimal paymentAmount) {
        // 计算剩余未缴金额
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(bill.getPaidAmount());
        if (paymentAmount.compareTo(remainingAmount) > 0) {
            throw new RuntimeException("缴费金额不能超过剩余未缴金额: " + remainingAmount);
        }
    }

    /**
     * 创建缴费记录
     */
    private TPayment createPaymentRecord(OnlinePaymentRequest request, TBill bill, House house) {
        TPayment payment = new TPayment();
        payment.setHouseId(request.getHouseId());
        payment.setBillId(request.getBillId());
        payment.setRoomNo(house.getRoomNo() != null ? house.getRoomNo() : "");
        payment.setHeatYear(bill.getHeatYear());
        payment.setPaymentMethod(TPayment.PaymentMethod.valueOf(request.getPaymentMethod().toLowerCase()));
        payment.setAmount(request.getAmount());
        payment.setTransactionNo(request.getTransactionNo());
        payment.setPaymentDate(LocalDateTime.now());
        payment.setRemark(request.getRemark());
        return paymentRepository.save(payment);
    }

    /**
     * 更新账单状态
     */
    private void updateBillStatus(TBill bill, BigDecimal paymentAmount) {
        // 更新已缴金额
        BigDecimal newPaidAmount = bill.getPaidAmount().add(paymentAmount);
        bill.setPaidAmount(newPaidAmount);

        // 更新最后缴费日期
        bill.setLastPaidDate(LocalDate.now());

        // 更新账单状态
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(newPaidAmount);
        if (remainingAmount.compareTo(BigDecimal.ZERO) == 0) {
            // 完全缴清
            bill.setStatus(TBill.BillStatus.paid);

            // 如果完全缴清，更新对应的逾期记录状态为已结清
            updateOverdueRecordStatus(bill.getId());
        } else {
            // 部分缴费
            bill.setStatus(TBill.BillStatus.partial_paid);
        }

        billRepository.save(bill);
    }

    /**
     * 构建缴费响应
     */
    private OnlinePaymentResponse buildPaymentResponse(TPayment payment, TBill bill) {
        OnlinePaymentResponse response = new OnlinePaymentResponse();
        response.setPaymentId(payment.getId());
        response.setBillId(payment.getBillId());
        response.setAmount(payment.getAmount());
        response.setPaymentMethod(payment.getPaymentMethod().name());
        response.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod().name()));
        response.setTransactionNo(payment.getTransactionNo());
        response.setPaymentDate(payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setBillStatus(bill.getStatus().name());
        response.setBillStatusText(getBillStatusText(bill.getStatus().name()));
        
        // 计算剩余金额
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(bill.getPaidAmount());
        response.setRemainingAmount(remainingAmount);
        response.setIsFullyPaid(remainingAmount.compareTo(BigDecimal.ZERO) == 0);

        return response;
    }

    /**
     * 更新逾期记录状态为已结清
     */
    private void updateOverdueRecordStatus(Long billId) {
        try {
            Optional<TOverdueRecord> overdueRecordOpt = overdueRecordRepository.findByBillId(billId);
            if (overdueRecordOpt.isPresent()) {
                TOverdueRecord overdueRecord = overdueRecordOpt.get();
                if (overdueRecord.getStatus() == TOverdueRecord.OverdueStatus.active) {
                    overdueRecord.setStatus(TOverdueRecord.OverdueStatus.cleared);
                    overdueRecordRepository.save(overdueRecord);
                    log.info("逾期记录状态已更新为已结清: billId={}, overdueRecordId={}",
                            billId, overdueRecord.getId());
                }
            }
        } catch (Exception e) {
            log.error("更新逾期记录状态失败: billId={}, error={}", billId, e.getMessage(), e);
            // 不抛出异常，避免影响主要的缴费流程
        }
    }

    /**
     * 获取支付方式文本
     */
    private String getPaymentMethodText(String paymentMethod) {
        switch (paymentMethod.toLowerCase()) {
            case "wechat":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "bank_transfer":
                return "银行转账";
            case "cash":
                return "现金支付";
            default:
                return paymentMethod;
        }
    }

    /**
     * 获取账单状态文本
     */
    private String getBillStatusText(String status) {
        switch (status.toLowerCase()) {
            case "unpaid":
                return "未缴费";
            case "partial_paid":
                return "部分缴费";
            case "paid":
                return "已缴清";
            case "overdue":
                return "逾期";
            default:
                return status;
        }
    }

    /**
     * 根据用户是否用热更新t_house住户表的is_heating状态
     */
    private boolean updateHouseHeatingStatus(House house, Integer isHeating) {
        try {
            house.setIsHeating(isHeating);
            houseRepository.save(house);
            log.info("房屋用热状态更新成功: houseId={}, isHeating={}", house.getId(), isHeating);
            return true;
        } catch (Exception e) {
            log.error("更新房屋用热状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建错误响应（新版本）
     */
    private PaymentSubmitResponse buildErrorResponse(String errorMessage) {
        PaymentSubmitResponse response = new PaymentSubmitResponse();
        response.setCode(400);
        response.setMessage(errorMessage);
        response.setData(null);
        return response;
    }
}
