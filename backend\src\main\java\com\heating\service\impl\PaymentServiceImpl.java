package com.heating.service.impl;

import com.heating.dto.bill.OnlinePaymentRequest;
import com.heating.dto.bill.OnlinePaymentResponse;
import com.heating.dto.payment.PaymentSubmitRequest;
import com.heating.dto.payment.PaymentSubmitResponse;
import com.heating.entity.House;
import com.heating.entity.bill.TBill;
import com.heating.entity.bill.TPayment;
import com.heating.entity.bill.TOverdueRecord;
import com.heating.repository.HouseRepository;
import com.heating.repository.TBillRepository;
import com.heating.repository.TPaymentRepository;
import com.heating.repository.TOverdueRecordRepository;
import com.heating.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 缴费服务实现类
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private TPaymentRepository paymentRepository;

    @Autowired
    private TBillRepository billRepository;

    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private TOverdueRecordRepository overdueRecordRepository;

    @Override
    @Transactional
    public OnlinePaymentResponse processOnlinePayment(OnlinePaymentRequest request) {
        try {
            log.info("开始处理在线缴费: {}", request);

            // 验证请求参数
            validatePaymentRequest(request);

            // 查询账单信息
            TBill bill = getBillById(request.getBillId());
            
            // 验证房屋信息
            House house = getHouseById(request.getHouseId());
            
            // 验证账单是否属于该房屋
            if (!bill.getHouseId().equals(request.getHouseId())) {
                throw new RuntimeException("账单与房屋信息不匹配");
            }

            // 验证缴费金额
            validatePaymentAmount(bill, request.getAmount());

            // 创建缴费记录
            TPayment payment = createPaymentRecord(request, bill, house);

            // 更新账单状态
            updateBillStatus(bill, request.getAmount());

            // 构建响应
            OnlinePaymentResponse response = buildPaymentResponse(payment, bill);

            log.info("在线缴费处理成功: paymentId={}, billId={}, amount={}", 
                    payment.getId(), bill.getId(), request.getAmount());

            return response;

        } catch (Exception e) {
            log.error("在线缴费处理失败: {}", e.getMessage(), e);
            throw new RuntimeException("缴费处理失败: " + e.getMessage());
        }
    }

    @Override
    public void validatePaymentRequest(OnlinePaymentRequest request) {
        if (request.getBillId() == null) {
            throw new RuntimeException("账单ID不能为空");
        }
        if (request.getHouseId() == null) {
            throw new RuntimeException("房屋ID不能为空");
        }
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("缴费金额必须大于0");
        }
        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new RuntimeException("支付方式不能为空");
        }
        if (request.getTransactionNo() == null || request.getTransactionNo().trim().isEmpty()) {
            throw new RuntimeException("第三方交易号不能为空");
        }
    }

    /**
     * 根据ID获取账单信息
     */
    private TBill getBillById(Long billId) {
        Optional<TBill> billOpt = billRepository.findById(billId);
        if (!billOpt.isPresent()) {
            throw new RuntimeException("账单不存在");
        }
        return billOpt.get();
    }

    /**
     * 根据ID获取房屋信息
     */
    private House getHouseById(Long houseId) {
        Optional<House> houseOpt = houseRepository.findById(houseId);
        if (!houseOpt.isPresent()) {
            throw new RuntimeException("房屋信息不存在");
        }
        return houseOpt.get();
    }

    /**
     * 验证缴费金额
     */
    private void validatePaymentAmount(TBill bill, BigDecimal paymentAmount) {
        // 计算剩余未缴金额
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(bill.getPaidAmount());
        if (paymentAmount.compareTo(remainingAmount) > 0) {
            throw new RuntimeException("缴费金额不能超过剩余未缴金额: " + remainingAmount);
        }
    }

    /**
     * 创建缴费记录
     */
    private TPayment createPaymentRecord(OnlinePaymentRequest request, TBill bill, House house) {
        TPayment payment = new TPayment();
        payment.setHouseId(request.getHouseId());
        payment.setBillId(request.getBillId());
        payment.setRoomNo(house.getRoomNo() != null ? house.getRoomNo() : "");
        payment.setHeatYear(bill.getHeatYear());
        payment.setPaymentMethod(TPayment.PaymentMethod.valueOf(request.getPaymentMethod().toLowerCase()));
        payment.setAmount(request.getAmount());
        payment.setTransactionNo(request.getTransactionNo());
        payment.setPaymentDate(LocalDateTime.now());
        payment.setRemark(request.getRemark());
        return paymentRepository.save(payment);
    }

    /**
     * 更新账单状态
     */
    private void updateBillStatus(TBill bill, BigDecimal paymentAmount) {
        // 更新已缴金额
        BigDecimal newPaidAmount = bill.getPaidAmount().add(paymentAmount);
        bill.setPaidAmount(newPaidAmount);

        // 更新最后缴费日期
        bill.setLastPaidDate(LocalDate.now());

        // 更新账单状态
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(newPaidAmount);
        if (remainingAmount.compareTo(BigDecimal.ZERO) == 0) {
            // 完全缴清
            bill.setStatus(TBill.BillStatus.paid);

            // 如果完全缴清，更新对应的逾期记录状态为已结清
            updateOverdueRecordStatus(bill.getId());
        } else {
            // 部分缴费
            bill.setStatus(TBill.BillStatus.partial_paid);
        }

        billRepository.save(bill);
    }

    /**
     * 构建缴费响应
     */
    private OnlinePaymentResponse buildPaymentResponse(TPayment payment, TBill bill) {
        OnlinePaymentResponse response = new OnlinePaymentResponse();
        response.setPaymentId(payment.getId());
        response.setBillId(payment.getBillId());
        response.setAmount(payment.getAmount());
        response.setPaymentMethod(payment.getPaymentMethod().name());
        response.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod().name()));
        response.setTransactionNo(payment.getTransactionNo());
        response.setPaymentDate(payment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setBillStatus(bill.getStatus().name());
        response.setBillStatusText(getBillStatusText(bill.getStatus().name()));
        
        // 计算剩余金额
        BigDecimal remainingAmount = bill.getTotalAmount().subtract(bill.getPaidAmount());
        response.setRemainingAmount(remainingAmount);
        response.setIsFullyPaid(remainingAmount.compareTo(BigDecimal.ZERO) == 0);

        return response;
    }

    /**
     * 更新逾期记录状态为已结清
     */
    private void updateOverdueRecordStatus(Long billId) {
        try {
            Optional<TOverdueRecord> overdueRecordOpt = overdueRecordRepository.findByBillId(billId);
            if (overdueRecordOpt.isPresent()) {
                TOverdueRecord overdueRecord = overdueRecordOpt.get();
                if (overdueRecord.getStatus() == TOverdueRecord.OverdueStatus.active) {
                    overdueRecord.setStatus(TOverdueRecord.OverdueStatus.cleared);
                    overdueRecordRepository.save(overdueRecord);
                    log.info("逾期记录状态已更新为已结清: billId={}, overdueRecordId={}",
                            billId, overdueRecord.getId());
                }
            }
        } catch (Exception e) {
            log.error("更新逾期记录状态失败: billId={}, error={}", billId, e.getMessage(), e);
            // 不抛出异常，避免影响主要的缴费流程
        }
    }

    /**
     * 获取支付方式文本
     */
    private String getPaymentMethodText(String paymentMethod) {
        switch (paymentMethod.toLowerCase()) {
            case "wechat":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "bank_transfer":
                return "银行转账";
            case "cash":
                return "现金支付";
            default:
                return paymentMethod;
        }
    }

    /**
     * 获取账单状态文本
     */
    private String getBillStatusText(String status) {
        switch (status.toLowerCase()) {
            case "unpaid":
                return "未缴费";
            case "partial_paid":
                return "部分缴费";
            case "paid":
                return "已缴清";
            case "overdue":
                return "逾期";
            default:
                return status;
        }
    }

    @Override
    @Transactional
    public PaymentSubmitResponse submitPayment(PaymentSubmitRequest request) {
        log.info("=== 开始处理缴费提交 ===");
        log.info("请求对象: {}", request);

        if (request == null) {
            log.error("请求对象为null");
            return buildErrorResponse("请求对象不能为空");
        }

        try {
            log.info("请求详情 - billId: {}, houseId: {}, amount: {}, paymentMethod: {}, transactionNo: {}, isHeating: {}, feeType: {}",
                    request.getBillId(), request.getHouseId(), request.getAmount(),
                    request.getPaymentMethod(), request.getTransactionNo(), request.getIsHeating(), request.getFeeType());

            // 1. 基本参数检查
            if (request.getBillId() == null) {
                log.error("billId为null");
                return buildErrorResponse("账单ID不能为空");
            }
            if (request.getHouseId() == null) {
                log.error("houseId为null");
                return buildErrorResponse("房屋ID不能为空");
            }
            if (request.getAmount() == null) {
                log.error("amount为null");
                return buildErrorResponse("缴费金额不能为空");
            }

            log.info("基本参数检查通过");

            // 2. 查询账单信息
            log.info("查询账单信息: billId={}", request.getBillId());
            TBill bill = null;
            try {
                Optional<TBill> billOpt = billRepository.findById(request.getBillId());
                if (!billOpt.isPresent()) {
                    log.error("账单不存在: billId={}", request.getBillId());
                    return buildErrorResponse("账单不存在");
                }
                bill = billOpt.get();
                log.info("账单信息查询成功: billId={}, heatYear={}, totalAmount={}",
                        bill.getId(), bill.getHeatYear(), bill.getTotalAmount());
            } catch (Exception e) {
                log.error("查询账单信息失败: {}", e.getMessage(), e);
                return buildErrorResponse("查询账单信息失败: " + e.getMessage());
            }

            // 3. 查询房屋信息
            log.info("查询房屋信息: houseId={}", request.getHouseId());
            House house = null;
            try {
                Optional<House> houseOpt = houseRepository.findById(request.getHouseId());
                if (!houseOpt.isPresent()) {
                    log.error("房屋信息不存在: houseId={}", request.getHouseId());
                    return buildErrorResponse("房屋信息不存在");
                }
                house = houseOpt.get();
                log.info("房屋信息查询成功: houseId={}, roomNo={}", house.getId(), house.getRoomNo());
            } catch (Exception e) {
                log.error("查询房屋信息失败: {}", e.getMessage(), e);
                return buildErrorResponse("查询房屋信息失败: " + e.getMessage());
            }

            // 4. 创建最简单的缴费记录
            log.info("开始创建缴费记录");
            TPayment payment = new TPayment();
            try {
                payment.setBillId(request.getBillId());
                payment.setHouseId(request.getHouseId());
                payment.setAmount(request.getAmount());

                // 设置支付方式
                if (request.getPaymentMethod() != null) {
                    payment.setPaymentMethod(TPayment.PaymentMethod.valueOf(request.getPaymentMethod().toLowerCase()));
                } else {
                    payment.setPaymentMethod(TPayment.PaymentMethod.wechat);
                }

                payment.setTransactionNo(request.getTransactionNo() != null ? request.getTransactionNo() : "TEST" + System.currentTimeMillis());
                payment.setPaymentDate(LocalDateTime.now());
                payment.setRemark(request.getRemark() != null ? request.getRemark() : "在线缴费");

                // 设置必填字段
                payment.setRoomNo(house.getRoomNo() != null ? house.getRoomNo() : "");
                payment.setHeatYear(bill.getHeatYear() != null ? bill.getHeatYear() : 0);

                log.info("缴费记录对象创建完成，准备保存");
                log.info("Payment对象详情: billId={}, houseId={}, amount={}, roomNo={}, heatYear={}",
                        payment.getBillId(), payment.getHouseId(), payment.getAmount(),
                        payment.getRoomNo(), payment.getHeatYear());

                TPayment savedPayment = paymentRepository.save(payment);
                log.info("缴费记录保存成功: ID={}", savedPayment.getId());

                // 构建简单的成功响应
                PaymentSubmitResponse response = new PaymentSubmitResponse();
                response.setCode(200);
                response.setMessage("缴费提交成功");

                PaymentSubmitResponse.PaymentData data = new PaymentSubmitResponse.PaymentData();
                data.setPaymentId(savedPayment.getId());
                data.setBillId(savedPayment.getBillId());
                data.setAmount(savedPayment.getAmount());
                data.setFeeType(request.getFeeType());
                data.setPaymentMethod(request.getPaymentMethod());
                data.setTransactionNo(savedPayment.getTransactionNo());
                data.setPaymentDate(savedPayment.getPaymentDate().toString());

                response.setData(data);
                // 更新账单状态
                updateBillStatus(bill, request.getAmount());
                log.info("=== 缴费提交处理完成 ===");
                return response;

            } catch (Exception e) {
                log.error("创建缴费记录失败: {}", e.getMessage(), e);
                log.error("异常堆栈:", e);
                return buildErrorResponse("创建缴费记录失败: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("缴费提交处理失败: {}", e.getMessage(), e);
            log.error("异常堆栈:", e);
            return buildErrorResponse("缴费处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证缴费提交请求
     */
    private void validatePaymentSubmitRequest(PaymentSubmitRequest request) {
        if (request.getBillId() == null) {
            throw new IllegalArgumentException("账单ID不能为空");
        }
        if (request.getHouseId() == null) {
            throw new IllegalArgumentException("房屋ID不能为空");
        }
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("缴费金额必须大于0");
        }
        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new IllegalArgumentException("支付方式不能为空");
        }
        if (request.getTransactionNo() == null || request.getTransactionNo().trim().isEmpty()) {
            throw new IllegalArgumentException("交易流水号不能为空");
        }
        if (request.getIsHeating() == null || (request.getIsHeating() != 0 && request.getIsHeating() != 1)) {
            throw new IllegalArgumentException("用热状态必须为0或1");
        }
        if (request.getFeeType() == null || request.getFeeType().trim().isEmpty()) {
            throw new IllegalArgumentException("缴费类型不能为空");
        }
    }

    /**
     * 创建缴费记录（新版本）
     */
    private TPayment createPaymentRecord(PaymentSubmitRequest request, TBill bill, House house) {
        TPayment payment = new TPayment();
        payment.setBillId(request.getBillId());
        payment.setHouseId(request.getHouseId());
        payment.setAmount(request.getAmount());
        payment.setPaymentMethod(TPayment.PaymentMethod.valueOf(request.getPaymentMethod().toUpperCase()));
        payment.setTransactionNo(request.getTransactionNo());
        payment.setPaymentDate(LocalDateTime.now());
        payment.setRemark(request.getRemark());

        // 设置必填字段，从房屋和账单信息中获取
        payment.setRoomNo(house.getRoomNo() != null ? house.getRoomNo() : "");
        payment.setHeatYear(bill.getHeatYear() != null ? bill.getHeatYear() : 0);

        return payment;
    }

    /**
     * 更新账单数据中的实际缴费数据
     */
    private boolean updateBillPaymentData(TBill bill, BigDecimal paymentAmount) {
        try {
            // 更新已缴费金额
            BigDecimal currentPaidAmount = bill.getPaidAmount() != null ? bill.getPaidAmount() : BigDecimal.ZERO;
            BigDecimal newPaidAmount = currentPaidAmount.add(paymentAmount);
            bill.setPaidAmount(newPaidAmount);

            // 更新账单状态
            BigDecimal totalAmount = bill.getTotalAmount();
            if (newPaidAmount.compareTo(totalAmount) >= 0) {
                bill.setStatus(TBill.BillStatus.paid);
            } else {
                bill.setStatus(TBill.BillStatus.partial_paid);
            }

            billRepository.save(bill);
            log.info("账单缴费数据更新成功: billId={}, 新增缴费={}, 总缴费={}",
                    bill.getId(), paymentAmount, newPaidAmount);
            return true;
        } catch (Exception e) {
            log.error("更新账单缴费数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据用户是否用热更新t_house住户表的is_heating状态
     */
    private boolean updateHouseHeatingStatus(House house, Integer isHeating) {
        try {
            house.setIsHeating(isHeating);
            houseRepository.save(house);
            log.info("房屋用热状态更新成功: houseId={}, isHeating={}", house.getId(), isHeating);
            return true;
        } catch (Exception e) {
            log.error("更新房屋用热状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查看是否有欠费记录，同时清除掉欠费记录状态为cleared已清除状态
     */
    private boolean clearOverdueRecords(Long houseId) {
        try {
            // 查找该房屋的所有未清除的欠费记录
            var overdueRecords = overdueRecordRepository.findByHouseIdAndStatus(houseId, TOverdueRecord.OverdueStatus.active);

            if (overdueRecords.isEmpty()) {
                log.info("房屋{}没有需要清除的欠费记录", houseId);
                return true;
            }

            // 将状态更新为已清除
            for (TOverdueRecord record : overdueRecords) {
                record.setStatus(TOverdueRecord.OverdueStatus.cleared);
                record.setUpdatedAt(LocalDateTime.now()); // 修复：使用LocalDateTime而不是LocalDate
            }

            overdueRecordRepository.saveAll(overdueRecords);
            log.info("成功清除房屋{}的{}条欠费记录", houseId, overdueRecords.size());
            return true;
        } catch (Exception e) {
            log.error("清除欠费记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建成功响应
     */
    private PaymentSubmitResponse buildSuccessResponse(TPayment payment, PaymentSubmitRequest request,
            boolean billUpdated, boolean heatingStatusUpdated, boolean overdueRecordsCleared) {
        PaymentSubmitResponse response = new PaymentSubmitResponse();
        response.setCode(200);
        response.setMessage("缴费提交成功");

        PaymentSubmitResponse.PaymentData data = new PaymentSubmitResponse.PaymentData();
        data.setPaymentId(payment.getId());
        data.setBillId(payment.getBillId());
        data.setAmount(payment.getAmount());
        data.setFeeType(request.getFeeType());
        data.setFeeTypeName(request.getFeeType().equals("heating") ? "用热缴费(全额)" : "不用热(管网维护费)");
        data.setPaymentMethod(request.getPaymentMethod());
        data.setPaymentMethodText(getPaymentMethodText(payment.getPaymentMethod().name()));
        data.setTransactionNo(payment.getTransactionNo());

        // 格式化日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        data.setPaymentDate(payment.getPaymentDate().format(dateFormatter));

        data.setHeatingStatusUpdated(heatingStatusUpdated);
        data.setOverdueRecordsCleared(overdueRecordsCleared);
        data.setBillStatusUpdated(billUpdated);

        response.setData(data);
        return response;
    }

    /**
     * 构建错误响应（新版本）
     */
    private PaymentSubmitResponse buildErrorResponse(String errorMessage) {
        PaymentSubmitResponse response = new PaymentSubmitResponse();
        response.setCode(400);
        response.setMessage(errorMessage);
        response.setData(null);
        return response;
    }
}
