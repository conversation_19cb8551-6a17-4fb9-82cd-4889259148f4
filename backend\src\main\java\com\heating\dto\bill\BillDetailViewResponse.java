package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 查看账单详情响应DTO
 */
@Data
public class BillDetailViewResponse {
    
    /**
     * 基本信息
     */
    private BasicInfo basicInfo;
    
    /**
     * 账单信息
     */
    private BillInfo billInfo;
    
    /**
     * 缴费记录
     */
    private List<PaymentRecord> paymentRecords;
    
    /**
     * 逾期记录
     */
    private OverdueInfo overdueInfo;
    
    /**
     * 基本信息
     */
    @Data
    public static class BasicInfo {
        /**
         * 户号
         */
        private String houseNumber;
        
        /**
         * 地址
         */
        private String address;
        
        /**
         * 面积（平方米）
         */
        private BigDecimal area;
        
        /**
         * 供暖年度
         */
        private String heatingYear;
        
        /**
         * 供暖状态
         */
        private String heatingStatus;
        
        /**
         * 供暖状态文本
         */
        private String heatingStatusText;
    }
    
    /**
     * 账单信息
     */
    @Data
    public static class BillInfo {
        /**
         * 账单ID
         */
        private Long billId;
        
        /**
         * 单价（元/平方米）
         */
        private BigDecimal unitPrice;

        /**
         * 供热费用（元）- 本年度供暖费用
         */
        private BigDecimal heatingFee;

        /**
         * 历史欠费金额（元）- 历史年度未缴清的费用
         */
        private BigDecimal historicalDebt;

        /**
         * 应缴费金额（元）- 供热费用 + 历史欠费金额
         */
        private BigDecimal totalPayableAmount;

        /**
         * 实际缴费金额（元）- 用户实际已缴纳的金额
         */
        private BigDecimal actualPaidAmount;

        /**
         * 应缴总金额（元）- 原有字段，保持兼容性
         */
        private BigDecimal totalAmount;

        /**
         * 已缴金额（元）- 原有字段，保持兼容性
         */
        private BigDecimal paidAmount;

        /**
         * 剩余未缴金额（元）
         */
        private BigDecimal remainingAmount;
        
        /**
         * 缴费截止日期
         */
        private String dueDate;
        
        /**
         * 最后缴费日期
         */
        private String lastPaidDate;
        
        /**
         * 账单状态
         */
        private String status;
        
        /**
         * 账单状态文本
         */
        private String statusText;
        
        /**
         * 备注
         */
        private String remark;
    }
    
    /**
     * 缴费记录
     */
    @Data
    public static class PaymentRecord {
        /**
         * 缴费记录ID
         */
        private Long paymentId;
        
        /**
         * 缴费金额（元）
         */
        private BigDecimal amount;
        
        /**
         * 缴费方式
         */
        private String paymentMethod;
        
        /**
         * 缴费方式文本
         */
        private String paymentMethodText;
        
        /**
         * 缴费时间
         */
        private String paymentDate;
        
        /**
         * 第三方交易号
         */
        private String transactionNo;
        
        /**
         * 备注
         */
        private String remark;
    }
    
    /**
     * 逾期信息
     */
    @Data
    public static class OverdueInfo {
        /**
         * 是否有逾期
         */
        private Boolean hasOverdue;
        
        /**
         * 逾期天数
         */
        private Integer overdueDays;
        
        /**
         * 欠费金额（元）
         */
        private BigDecimal overdueAmount;
        
        /**
         * 滞纳金金额（元）
         */
        private BigDecimal penaltyAmount;
        
        /**
         * 滞纳金日利率
         */
        private BigDecimal penaltyRate;
        
        /**
         * 首次逾期日期
         */
        private String firstOverdueDate;
        
        /**
         * 最后更新日期
         */
        private String lastUpdatedDate;
    }
}
