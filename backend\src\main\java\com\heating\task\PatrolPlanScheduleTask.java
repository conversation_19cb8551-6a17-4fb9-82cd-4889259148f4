package com.heating.task;

import com.heating.entity.patrol.TPatrolPlan;
import com.heating.entity.patrol.TPatrolRecord;
import com.heating.repository.PatrolPlanRepository;
import com.heating.repository.PatrolRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 巡检计划定时任务
 * 负责每天凌晨检查巡检计划状态并生成巡检记录
 */
@Slf4j
@Component
public class PatrolPlanScheduleTask {

    @Autowired
    private PatrolPlanRepository patrolPlanRepository;

    @Autowired
    private PatrolRecordRepository patrolRecordRepository;

    /**
     * 每天凌晨1点执行
     * 1. 检查待执行的巡检记录是否超时
     * 2. 更新巡检计划状态
     * 3. 生成当天需要执行的巡检记录
     */
    @Scheduled(cron = "0 0 1 * * ?")
    // @Scheduled(initialDelay = 2000,fixedDelay=Long.MAX_VALUE)
    @Transactional
    public void updatePatrolPlanStatusAndGenerateRecords() {
        log.info("开始执行巡检计划定时任务...");
        
        // 获取当前日期
        LocalDate today = LocalDate.now();
        log.info("当前日期: {}", today);
        
        // 1. 检查待执行的巡检记录是否超时
        checkOverduePatrolRecords(today);
        
        // 2. 更新巡检计划状态
        updatePatrolPlanStatus(today);
        
        // 3. 生成当天需要执行的巡检记录
        generatePatrolRecords(today);
        
        log.info("巡检计划定时任务执行完成");
    }
    
    /**
     * 检查待执行的巡检记录是否超时
     * 如果巡检记录的执行日期早于当前日期，且状态仍为待执行(pending)，则更新状态为超时(overdue)
     * 
     * @param today 当前日期
     */
    private void checkOverduePatrolRecords(LocalDate today) {
        log.info("开始检查待执行巡检记录是否超时...");
        
        // 查询所有状态为待执行(pending)的巡检记录
        List<TPatrolRecord> pendingRecords = patrolRecordRepository.findByStatus("pending");
        log.info("查询到{}个待执行的巡检记录", pendingRecords.size());
        
        int overdueCount = 0;
        
        // 遍历待执行的巡检记录
        for (TPatrolRecord record : pendingRecords) {
            // 如果执行日期早于当前日期，则认为已超时
            if (record.getExecutionDate().isBefore(today)) {
                log.info("巡检记录[{}]已超时，执行日期: {}, 当前日期: {}, 更新状态为超时", 
                        record.getId(), record.getExecutionDate(), today);
                
                // 更新状态为超时(overdue)
                record.setStatus("overdue");
                record.setUpdateTime(LocalDateTime.now());
                patrolRecordRepository.save(record);
                
                overdueCount++;
            }
        }
        
        log.info("巡检记录超时检查完成，共{}条记录超时", overdueCount);
    }
    
    /**
     * 更新巡检计划状态
     * 1. 对于待执行的计划，检查是否当前日期 > 开始日期，更新状态为执行中
     * 2. 对于执行中的计划，检查是否当前日期 > 结束日期，更新状态为已完成
     * 
     * @param today 当前日期
     */
    private void updatePatrolPlanStatus(LocalDate today) {
        log.info("开始更新巡检计划状态...");
        
        // 查询所有待执行的巡检计划
        List<TPatrolPlan> pendingPlans = patrolPlanRepository.findByStatusIn(Arrays.asList("pending"));
        log.info("查询到{}个待执行的巡检计划", pendingPlans.size());
        
        // 更新待执行的计划状态
        for (TPatrolPlan plan : pendingPlans) {
            // 如果当前日期 >= 开始日期，更新状态为执行中
            if (today.compareTo(plan.getStartDate()) >= 0) {
                log.info("巡检计划[{}]({})已到开始日期，更新状态为执行中", plan.getId(), plan.getName());
                plan.setStatus("processing");
                plan.setUpdateTime(LocalDateTime.now());
                patrolPlanRepository.save(plan);
            }
        }
        
        // 查询所有执行中的巡检计划
        List<TPatrolPlan> processingPlans = patrolPlanRepository.findByStatusIn(Arrays.asList("processing"));
        log.info("查询到{}个执行中的巡检计划", processingPlans.size());
        
        // 更新执行中的计划状态
        for (TPatrolPlan plan : processingPlans) {
            // 如果设置了结束日期，并且当前日期 > 结束日期，更新状态为已完成
            if (plan.getEndDate() != null && today.compareTo(plan.getEndDate()) > 0) {
                log.info("巡检计划[{}]({})已超过结束日期，更新状态为已完成", plan.getId(), plan.getName());
                plan.setStatus("completed");
                plan.setUpdateTime(LocalDateTime.now());
                patrolPlanRepository.save(plan);
            }
        }
        
        log.info("巡检计划状态更新完成");
    }
    
    /**
     * 生成当天需要执行的巡检记录
     * 根据巡检计划的周期类型（日、周、月）检查今天是否需要执行
     * 
     * @param today 当前日期
     */
    private void generatePatrolRecords(LocalDate today) {
        log.info("开始生成当天巡检记录...");
        
        // 获取今天是周几（1-7，周一到周日）
        int currentDayOfWeek = today.getDayOfWeek().getValue();
        log.info("当前周几: {}", currentDayOfWeek);
        
        // 获取今天是本月第几天（1-31）
        int currentDayOfMonth = today.getDayOfMonth();
        log.info("当前月天: {}", currentDayOfMonth);
        
        // 查询状态为执行中的巡检计划
        List<TPatrolPlan> processingPlans = patrolPlanRepository.findByStatusIn(Arrays.asList("processing"));
        log.info("查询到{}个执行中的巡检计划", processingPlans.size());
        
        int generatedCount = 0;
        
        // 遍历执行中的巡检计划
        for (TPatrolPlan plan : processingPlans) {
            boolean needsExecution = false;
            
            // 根据调度类型检查今天是否需要执行
            switch (plan.getScheduleType()) {
                case "daily":
                    // 每天执行
                    needsExecution = true;
                    log.debug("计划ID: {}为每日计划，需要执行", plan.getId());
                    break;
                    
                case "weekly":
                    // 检查今天是否是计划的周执行日
                    if (plan.getScheduleWeekDays() != null && plan.getScheduleWeekDays().contains(currentDayOfWeek)) {
                        needsExecution = true;
                        log.debug("计划ID: {}为每周计划，当前星期{}在执行日列表{}中，需要执行", 
                                plan.getId(), currentDayOfWeek, plan.getScheduleWeekDays());
                    }
                    break;
                    
                case "monthly":
                    // 检查今天是否是计划的月执行日
                    if (plan.getScheduleMonthDays() != null && plan.getScheduleMonthDays().contains(currentDayOfMonth)) {
                        needsExecution = true;
                        log.debug("计划ID: {}为每月计划，当前日期{}在执行日列表{}中，需要执行", 
                                plan.getId(), currentDayOfMonth, plan.getScheduleMonthDays());
                    }
                    break;
                    
                case "custom":
                    // 如果是自定义间隔，需要查询最后一次执行日期，判断是否达到间隔天数
                    if (plan.getScheduleInterval() != null) {
                        // 获取最近一次执行记录
                        java.util.Optional<TPatrolRecord> lastRecord = patrolRecordRepository
                                .findTopByPatrolPlanIdOrderByExecutionDateDesc(plan.getId());
                        
                        if (lastRecord.isPresent()) {
                            LocalDate lastExecutionDate = lastRecord.get().getExecutionDate();
                            long daysSinceLastExecution = java.time.temporal.ChronoUnit.DAYS.between(lastExecutionDate, today);
                            
                            if (daysSinceLastExecution >= plan.getScheduleInterval()) {
                                needsExecution = true;
                                log.debug("计划ID: {}为自定义间隔计划，上次执行日期: {}, 间隔: {}, 已过天数: {}, 需要执行", 
                                        plan.getId(), lastExecutionDate, plan.getScheduleInterval(), daysSinceLastExecution);
                            }
                        } else {
                            // 没有执行记录，应该执行
                            needsExecution = true;
                            log.debug("计划ID: {}为自定义间隔计划，无执行记录，需要执行", plan.getId());
                        }
                    }
                    break;
            }
            
            if (needsExecution) {
                // 检查今天是否已经生成了执行记录
                boolean hasRecordToday = patrolRecordRepository.existsByPatrolPlanIdAndExecutionDate(plan.getId(), today);
                
                if (!hasRecordToday) {
                    // 如果今天需要执行且没有生成记录，则创建巡检记录
                    createPatrolRecord(plan, today);
                    generatedCount++;
                } else {
                    log.debug("计划ID: {}今天已有执行记录，跳过", plan.getId());
                }
            }
        }
        
        log.info("巡检记录生成完成，共生成{}条记录", generatedCount);
    }
    
    /**
     * 创建巡检记录
     * 
     * @param plan 巡检计划
     * @param executionDate 执行日期
     */
    private void createPatrolRecord(TPatrolPlan plan, LocalDate executionDate) {
        log.info("为计划[{}]({})创建巡检记录", plan.getId(), plan.getName());
        
        // 获取执行人ID列表
        List<Object> executorIdObjects = plan.getExecutorIds();
        if (executorIdObjects == null || executorIdObjects.isEmpty()) {
            log.warn("计划[{}]({})没有指定执行人，无法创建巡检记录", plan.getId(), plan.getName());
            return;
        }
        
        // 获取第一个执行人ID
        Object firstExecutorObj = executorIdObjects.get(0);
        Long executorId;
        
        // 处理可能的类型转换
        if (firstExecutorObj instanceof Integer) {
            executorId = ((Integer) firstExecutorObj).longValue();
        } else if (firstExecutorObj instanceof Long) {
            executorId = (Long) firstExecutorObj;
        } else {
            // 尝试解析为Long
            try {
                executorId = Long.parseLong(firstExecutorObj.toString());
            } catch (Exception e) {
                log.error("无法解析执行人ID: {}", firstExecutorObj, e);
                return;
            }
        }
        
        // 创建巡检记录
        TPatrolRecord record = new TPatrolRecord();
        record.setPatrolPlanId(plan.getId().intValue());
        record.setExecutorId(executorId);
        record.setExecutionDate(executionDate);


        LocalDate today = LocalDate.now();
        // 设置开始时间为当天 00:00:00
        LocalDateTime startOfDay = today.atStartOfDay();
        // 设置结束时间为当天 23:59:59
        LocalDateTime endOfDay = today.atTime(23, 59, 59, 999_999_999);
        record.setStartTime(startOfDay); // 设置为当前时间，实际执行时会更新
        record.setEndTime(endOfDay);   // 设置为当前时间，实际执行时会更新

        record.setStatus("pending");              // 初始状态为待执行
        record.setRemark("系统自动生成");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        
        // 保存巡检记录
        patrolRecordRepository.save(record);
        
        log.info("巡检记录创建成功，记录ID: {}", record.getId());
    }
} 