<template>
  <view class="station-picker">
    <view class="picker-header">
      <text class="title">选择换热站</text>
      <text class="close-btn" @tap="close">×</text>
    </view>
    
    <view class="search-box">
      <input type="text" v-model="keyword" placeholder="搜索换热站" />
      <text class="search-icon">🔍</text>
    </view>
    
    <view class="station-list">
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>
      <view v-else-if="filteredStations.length === 0" class="empty">
        <text>暂无换热站数据</text>
      </view>
      <view 
        v-else
        v-for="station in filteredStations" 
        :key="station.id"
        class="station-item"
        @tap="selectStation(station)"
      >
        <view class="station-info">
          <text class="station-name">{{ station.name }}</text>
          <text class="station-address">{{ station.address }}</text>
        </view>
        <view class="station-status" :class="station.status">
          <text>{{ station.status === 'online' ? '在线' : '离线' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { heatingStationApi } from '../utils/api.js';

export default {
  name: 'StationPicker',
  data() {
    return {
      stations: [],
      loading: true,
      keyword: '',
    };
  },
  computed: {
    filteredStations() {
      if (!this.keyword) return this.stations;
      
      const lowerKeyword = this.keyword.toLowerCase();
      return this.stations.filter(station => 
        station.name.toLowerCase().includes(lowerKeyword) || 
        (station.address && station.address.toLowerCase().includes(lowerKeyword))
      );
    }
  },
  created() {
    this.fetchStations();
  },
  methods: {
    async fetchStations() {
      this.loading = true;
      try {
        const res = await heatingStationApi.getList();
        if (res.code === 200 && res.data) {
          this.stations = res.data;
        } else {
          uni.showToast({
            title: '获取换热站列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取换热站列表出错:', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    selectStation(station) {
      this.$emit('select', station);
      this.close();
    },
    close() {
      this.$emit('close');
    }
  }
}
</script>

<style>
.station-picker {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 70vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 44rpx;
  color: #999;
  height: 60rpx;
  width: 60rpx;
  text-align: center;
  line-height: 60rpx;
}

.search-box {
  position: relative;
  padding: 20rpx 30rpx;
}

.search-box input {
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 16rpx 60rpx 16rpx 30rpx;
  font-size: 28rpx;
}

.search-icon {
  position: absolute;
  right: 50rpx;
  top: 36rpx;
  color: #999;
  font-size: 32rpx;
}

.station-list {
  flex: 1;
  overflow-y: auto;
}

.station-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.station-info {
  display: flex;
  flex-direction: column;
}

.station-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.station-address {
  font-size: 24rpx;
  color: #999;
}

.station-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.station-status.online {
  background-color: rgba(0, 200, 83, 0.1);
  color: #00c853;
}

.station-status.offline {
  background-color: rgba(239, 83, 80, 0.1);
  color: #ef5350;
}

.loading, .empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}
</style> 