package com.heating.entity.user;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Entity
@Table(name = "t_user_weixin")
@Data
public class TUserWeixin {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotEmpty
    @Size(min = 4, max = 50)
    @Column(unique = true)
    private String username;
    
    @NotEmpty
    @Size(min = 6)
    private String password;
    
    @NotEmpty
    @Size(max = 50)
    private String name;
    
    private String phone;

    @Column(name = "heat_unit_id")
    private Long heatUnitId;

    @Column(name = "house_id")
    private Long houseId;
    
    private Integer status;
    
    @Column(name = "nick_name", length = 50)
    private String nickName;
    
    @Column(length = 255)
    private String avatar;
    
    private Integer gender;
    
    @Column(name = "openid", unique = true, length = 100)
    private String openid;
    
    @Column(name = "unionid", length = 100)
    private String unionid;
    
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}