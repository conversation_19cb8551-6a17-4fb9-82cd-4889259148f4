package com.heating.repository;

import com.heating.entity.bill.TPayment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TPaymentRepository extends JpaRepository<TPayment, Long> {
    
    List<TPayment> findByBillIdOrderByPaymentDateDesc(Long billId);
    
    List<TPayment> findByHouseIdOrderByPaymentDateDesc(Long houseId);
    
    Page<TPayment> findByHouseIdOrderByPaymentDateDesc(Long houseId, Pageable pageable);
    
    @Query("SELECT p FROM TPayment p WHERE p.houseId = :houseId AND p.paymentDate BETWEEN :startDate AND :endDate ORDER BY p.paymentDate DESC")
    Page<TPayment> findByHouseIdAndDateRange(@Param("houseId") Long houseId, 
                                           @Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate, 
                                           Pageable pageable);
}

