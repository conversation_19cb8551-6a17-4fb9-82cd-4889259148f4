一、页面要求：
1、故障列表页面显示内容至少包括：故障编号，换热站，故障类型，故障等级，发生时间，上报人员，故障状态（待确认，已确认，已退回）。
2、故障详情页面显示内容至少包括：故障编号，换热站，故障类型，故障等级，故障描述，图片附件，视频附件，发生时间，上报人员，上报时间，故障状态等。
3、工单列表页面显示内容至少包括：工单编号，换热站，故障类型，故障等级，生成时间，工单状态（待接单，已接单，已完成）。
4、工单详情页面显示内容至少包括：工单编号，换热站，故障类型，故障等级，发生时间，故障描述，故障附件，维修人员，维修内容，维修结果，维修时间，维修附件，工单状态，工单日志（即故障上报及维修操作记录表中对应的日志信息）等。
5、室温列表页面显示内容：页面分为上下两部分，上部分显示当前室外温度，下面部分显示：小区名称、楼栋号、单元号、户号，上报时间，上报温度。

二、业务流程
故障上报及维修完整闭环流程：
（一）、故障上报
1.用户在小程序中选择“故障上报”功能。
2.用户填写故障信息，包括换热站，故障类型，故障等级，故障描述，图片附件，视频附件，发生时间，上报人员，上报时间等。 
故障状态分为：待确认，已确认，已退回
3.用户提交故障信息前，系统进行初步验证，确保信息完整无误。
4.用户提交故障上报。
5.小程序将故障信息发送至后端系统。
6.后端系统记录故障信息，并生成唯一故障编号，编号规则为“年月日时分秒”格式，故障状态为“待确认”。
7.管理员通过查看故障详情后，如无误则确认故障信息并手动指派给维修人员同时生成维修工单，生成的工单编号唯一，且故障状态更改为“已确认”，工单状态为“待接单”；如有误，更改故障状态为“已退回”。
（二）、维修工单处理
1.维修人员通过小程序接收到维修任务通知。
2.维修人员查看通过故障详情后确认是否接单，如确认接单，则确认后更改工单状态为“维修中”；如拒单，则进行“转单”操作，即由该维修人员重新指派其他维修人员。
3.维修完成后，维修人员填写维修记录，包括故障原因，维修内容，维修结果，上传图片或视频附件，维修时间等，提交成功后，更改工单状态为“已完成”。
（三）、故障处理闭环操作
1.维修人员提交维修记录成功后，系统通知故障上报人员维修已完成，并可查看工单详情，如有疑问，可与维修人员沟通。


室温上流程： 
1、小区信息：
提供带搜索功能的选择框，用户可以选择所在小区。
2、换热站信息：
以下拉框形式显示所选小区对应的所有换热站，用户可以选择换热站。
3、楼栋号、单元号、户号：
用户根据提示输入具体的楼栋号、单元号和户号。
4、当前室内温度：
用户手动输入室内温度数值。
系统实时验证温度数值是否在合理范围内。
5、图片上传：
提供拍照和从手机相册选择图片的功能。
系统提示用户上传清晰的室内环境照片。
6、当前室外温度：
系统自动获取当前室外温度。
7、当前定位信息：
   系统自动获取当前地理位置信息（即经度、纬度信息）
 