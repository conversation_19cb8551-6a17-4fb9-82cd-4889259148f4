package com.heating.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 热用户实体类
 * 对应数据库表 t_heat_unit
 */
@Data
@Entity
@Table(name = "t_heat_unit")
public class THeatUnit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String name;
 
    @Column(name = "longitude")
    private Double longitude;
 
    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt; 
} 