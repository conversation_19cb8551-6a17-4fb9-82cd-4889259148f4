package com.heating.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置类
 * 用于提高巡检记录详情等频繁访问数据的加载速度
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置缓存管理器
     * 使用简单的ConcurrentMapCacheManager实现内存缓存
     * 
     * @return 缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 配置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "patrolResultDetails", // 巡检结果详情缓存
            "patrolItems",         // 巡检项目缓存
            "patrolPlans"          // 巡检计划缓存
        ));
        
        return cacheManager;
    }
} 