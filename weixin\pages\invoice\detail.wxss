/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 票据内容 */
.invoice-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 票据头部 */
.invoice-header {
  position: relative;
  text-align: center;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #1890ff;
  margin-bottom: 30rpx;
}

.company-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #1890ff;
  display: block;
  margin-bottom: 10rpx;
}

.company-name-en {
  font-size: 24rpx;
  color: #666;
  letter-spacing: 2rpx;
}

/* 电子章样式 */
.seal-image {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  z-index: 10;
}

/* 顶部电子章 */
.seal-top {
  top: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0.8;
}

/* 支付信息区域 */
.payment-section {
  position: relative;
  margin-bottom: 40rpx;
}

/* 右下角电子章 */
.seal-bottom {
  bottom: -20rpx;
  right: 20rpx;
  opacity: 0.9;
}

/* 移除原来的电子章区域 */
.seal-section {
  display: none;
}

.seal {
  display: none;
}

/* 确保电子章不影响文字布局 */
.info-section {
  position: relative;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
  position: relative;
  z-index: 1;
}

.info-value.amount {
  color: #52c41a;
  font-weight: 600;
  font-size: 32rpx;
}

/* 确保右下角电子章覆盖在文字上 */
.payment-section .info-item:last-child .info-value {
  padding-right: 140rpx;
}

/* 分割线 */
.section-divider {
  text-align: center;
  margin: 30rpx 0;
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: #e8e8e8;
}

.section-title {
  background: #fff;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* 信息区块 */
.info-section {
  margin-bottom: 30rpx;
}

.info-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 4rpx solid #1890ff;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.info-value.amount {
  color: #52c41a;
  font-weight: 600;
  font-size: 32rpx;
}

/* 操作按钮 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  border: none;
  color: #fff;
  font-weight: 600;
}

.download-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.share-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}
