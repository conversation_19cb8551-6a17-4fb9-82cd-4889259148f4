<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <!-- 状态筛选按钮 -->
    <view class="status-filter">
      <view class="filter-btn {{currentStatus === '' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="">全部</view>
      <view class="filter-btn {{currentStatus === 'pending' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="pending">待接单</view>
      <view class="filter-btn {{currentStatus === 'processing' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="processing">维修中</view>
      <view class="filter-btn {{currentStatus === 'completed' ? 'active' : ''}}" 
            bindtap="filterByStatus" data-status="completed">已完成</view>
    </view>
    <!-- 日期筛选 -->
    <picker mode="date" bindchange="onDateChange">
      <view class="date-picker">
        <text>{{selectedDate || '选择日期'}}</text>
      </view>
    </picker>
  </view>

  <!-- 工单列表 -->
  <view class="workorder-list">
    <view class="workorder-item" 
          wx:for="{{orders}}" 
          wx:key="order_id"
          bindtap="goToDetail" 
          data-id="{{item.order_id}}">
      <view class="workorder-header">
        <view class="workorder-no">工单编号：{{item.order_id}}</view>
        <text class="workorder-status {{item.status}}">{{item.status_text}}</text>
      </view>

      <view class="workorder-info">
        <view class="info-item">
          <text class="label">换热站：</text>
          <text class="value">{{item.station_name}}</text>
        </view>
        <view class="info-item">
          <text class="label">故障类型：</text>
          <text class="value">{{item.fault_type}}</text>
        </view>
        <view class="info-item">
          <text class="label">故障等级：</text>
          <text class="level-{{item.fault_level}}">{{item.fault_level_text}}
          </text> 
        </view>
        <view class="info-item">
          <text class="label">生成时间：</text>
          <text class="value">{{item.created_time}}</text>
        </view>
      </view>

      <!-- 接单按钮 -->
      <button class="accept-btn" 
              wx:if="{{item.status === 'pending'}}"
              catchtap="handleAccept" 
              data-id="{{item.order_id}}">接单</button>
    </view>
  </view>
</view> 