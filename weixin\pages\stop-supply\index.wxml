<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">申请停止供暖</text>
    <view class="header-actions">
      <view class="record-btn" bindtap="goToRecords">
        <text class="iconfont">📋</text>
        <text>申请记录</text>
      </view>
    </view>
  </view>

  <!-- 房屋信息卡片 -->
  <view class="card house-card">
    <view class="card-header">
      <text class="card-title">房屋信息</text>
    </view>
    <view class="house-info">
      <view class="info-row">
        <text class="label">房屋地址</text>
        <text class="value">{{houseInfo.address}}</text>
      </view>
      <view class="info-row">
        <text class="label">户号</text>
        <text class="value">{{houseInfo.houseNumber}}</text>
      </view>
    </view>
  </view>

  <!-- 申请信息卡片 -->
  <view class="card form-card">
    <view class="card-header">
      <text class="card-title">申请信息</text>
    </view>
    
    <!-- 停供开始日期 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>停供开始日期</text>
      </view>
      <picker mode="date" 
              value="{{formData.stopStartDate}}" 
              start="{{minDate}}"
              bindchange="onStartDateChange">
        <view class="picker-input {{formData.stopStartDate ? 'selected' : 'placeholder'}}">
          <text>{{formData.stopStartDate || '请选择日期'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
      <view class="form-tip">最早可选：{{minDate}}</view>
    </view>

    <!-- 停供结束日期 -->
    <view class="form-item">
      <view class="form-label">
        <text>停供结束日期</text>
      </view>
      <picker mode="date" 
              value="{{formData.stopEndDate}}" 
              start="{{formData.stopStartDate || minDate}}"
              bindchange="onEndDateChange">
        <view class="picker-input {{formData.stopEndDate ? 'selected' : 'placeholder'}}">
          <text>{{formData.stopEndDate || '请选择日期（可选）'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
      <view class="form-tip">如长期停供可不填</view>
    </view>

    <!-- 申请原因 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>申请原因</text>
      </view>
      <textarea class="textarea-input" 
                placeholder="请输入申请原因，如：长期出差、房屋出租、暂不居住等"
                value="{{formData.reason}}"
                bindinput="onReasonInput"
                maxlength="200">
      </textarea>
      <view class="char-count">{{formData.reason.length}}/200</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
            bindtap="submitApply"
            disabled="{{!canSubmit}}">
      提交申请
    </button>
  </view>
</view>


