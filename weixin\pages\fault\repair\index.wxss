.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-title {
  display: block;
  color: #ffffff;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 表单容器 */
.form-container {
  padding: 0 30rpx;
  margin-top: -30rpx;
  position: relative;
  z-index: 3;
}

/* 表单区块 */
.form-section {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}

.title-subtitle {
  display: block;
  font-size: 26rpx;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 表单项 */
.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  display: block;
  font-size: 30rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.label.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 选择器样式 */
.picker {
  height: 88rpx;
  line-height: 88rpx;
  background: #fafafa;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #262626;
  position: relative;
  transition: all 0.2s ease;
}

.picker.placeholder {
  color: #bfbfbf;
}

.picker.loading {
  color: #bfbfbf;
  background: #f5f5f5;
}

.picker-arrow {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
  font-size: 24rpx;
}

/* 输入框样式 */
.input-field {
  height: 88rpx;
  background: #fafafa;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #262626;
  transition: all 0.2s ease;
}

.input-field:focus {
  border-color: #1890ff;
  background: #ffffff;
}

.input-placeholder {
  color: #bfbfbf;
}

/* 等级选择器 */
.level-selector {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.level-item {
  flex: 1;
  min-width: 140rpx;
  height: 80rpx;
  background: #fafafa;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.level-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.level-text {
  font-size: 28rpx;
  color: #262626;
}

.level-item.active .level-text {
  color: #1890ff;
  font-weight: 500;
}

/* 文本域样式 */
.textarea-field {
  width: 100%;
  min-height: 200rpx;
  background: #fafafa;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 30rpx;
  color: #262626;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.textarea-field:focus {
  border-color: #1890ff;
  background: #ffffff;
}

.textarea-placeholder {
  color: #bfbfbf;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #bfbfbf;
  margin-top: 12rpx;
}

/* 图片上传 */
.upload-container {
  padding: 30rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.delete-icon {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}

.upload-actions {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  background: #fafafa;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.upload-btn:active {
  background: #f0f0f0;
  border-color: #bfbfbf;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #8c8c8c;
}

.upload-tips {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #8c8c8c;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}

/* 提交按钮 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: #bfbfbf;
  transform: none;
}

.submit-btn[disabled] {
  background: #bfbfbf;
  color: #ffffff;
}
