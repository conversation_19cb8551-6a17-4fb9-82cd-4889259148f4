package com.heating.dto.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
 
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonFormat;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Certification {
    private String name;
    private String number;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expireDate;
} 