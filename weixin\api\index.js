const { request } = require('../utils/request.js');

/**
 * 用户相关API
 */
const userApi = {
  // 用户登录
  login(data) {
    return request({
      url: '/api/auth/login',
      method: 'POST',
      data
    });
  },
  // 获取用户信息
  getUserInfo(userId) {
    return request({
      url: `/api/user/${userId}`,
      method: 'GET'
    });
  },
  
};
/**
 * 绑定设备相关API
 */
const bindApi = {
  // 绑定户号
  bindHouse(data) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.username) {
      return Promise.reject({ message: '用户未登录' });
    }
    return request({
      url: '/api/weixin/bind-house',
      method: 'POST',
      data: {
        username: userInfo.username,
        houseNumber: data.houseNumber
      }
    });
  }
};
/**
 * 账单相关API
 */
const billApi = {
  // 获取账单列表
  getBillList(data = {}) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      return Promise.reject({ message: '用户未登录' });
    }
    
    const requestData = {
      userId: userInfo.id,
      status: data.status,
      page: data.page || 1,
      pageSize: data.pageSize || 20
    };
    
    return request({
      url: '/api/weixin/bills',
      method: 'POST',
      data: requestData
    });
  },
  
  // 获取账单详情
  getBillDetail(billId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      return Promise.reject({ message: '用户未登录' });
    }

    return request({
      url: '/api/weixin/bill-detail',
      method: 'POST',
      data: {
        billId: billId,
        userId: userInfo.id
      }
    });
  },

  // 查看账单详情（完整信息：基本信息、账单信息、缴费记录、逾期记录）
  viewBillDetail(data = {}) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      return Promise.reject({ message: '用户未登录' });
    }

    const requestData = {
      userId: userInfo.id,
      houseId: data.houseId || userInfo.houseId,
      heatingYear: data.heatingYear
    };

    return request({
      url: '/api/weixin/bill/view-detail',
      method: 'POST',
      data: requestData
    });
  },
  
  // 获取缴费记录列表 - 只需要账单ID
  getPaymentList(billId) {
    if (!billId) {
      return Promise.reject({ message: '账单ID不能为空' });
    }
    return request({
      url: '/api/weixin/payments',
      method: 'POST',
      data: {
       billId: parseInt(billId) // 确保是数字类型
      }
    });
  },

  // 获取简化的账单信息
  // 根据用热状态返回不同的费用信息，严格按照账单表数据
  getSimpleBillInfo(data = {}) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      return Promise.reject({ message: '用户未绑定房屋信息' });
    }

    const requestData = {
      houseId: data.houseId || userInfo.houseId,
      heatingYear: data.heatingYear
    };

    return request({
      url: '/api/weixin/bill/simple-info',
      method: 'POST',
      data: requestData
    });
  }
};

/**
 * 缴费相关API
 */
const paymentApi = {
  // 根据房屋ID获取所有缴费记录
  getPaymentRecordsByHouse(houseId) {
    if (!houseId) {
      return Promise.reject({ message: '房屋ID不能为空' });
    }
    
    return request({
      url: '/api/weixin/payment-records',
      method: 'POST',
      data: {
        houseId: houseId
      }
    });
  },

  // 获取票据详情
  getInvoiceDetail(paymentId) {
    return request({
      url: '/api/weixin/invoice-detail',
      method: 'POST',
      data: {
        paymentId: paymentId
      }
    });
  },

  // 下载票据PDF
  downloadInvoicePDF(paymentId) {
    return request({
      url: '/api/weixin/download-invoice-pdf',
      method: 'POST',
      data: {
        paymentId: paymentId
      }
    });
  },

  // 在线缴费
  processOnlinePayment(data) {
    return request({
      url: '/api/weixin/payment/online-pay',
      method: 'POST',
      data: data
    });
  },

  // 创建支付订单（用于支付前的准备）
  createPayment(data) {
    return request({
      url: '/api/weixin/payment/create',
      method: 'POST',
      data: data
    });
  },

  // 缴费提交（新版本）
  // 支持用热缴费和管网维护费两种类型
  submitPayment(data) {
    return request({
      url: '/api/weixin/payment/online-pay',
      method: 'POST',
      data: data
    });
  }
};

/**
 * 工单相关API
 */
const workOrderApi = {
  // 获取工单列表
  getWorkOrderList(data) {
    return request({
      url: '/api/work-orders',
      method: 'POST',
      data
    });
  },
  
  // 创建工单
  createWorkOrder(data) {
    return request({
      url: '/api/work-orders',
      method: 'POST',
      data
    });
  },
  
  // 获取工单详情
  getWorkOrderDetail(orderId) {
    return request({
      url: `/api/work-orders/${orderId}`,
      method: 'GET'
    });
  }
};

/**
 * 故障相关API
 */
const faultApi = {
  // 提交故障报修
  reportFault(data) {
    return request({
      url: '/api/weixin/report',
      method: 'POST',
      data
    });
  },

  // 获取故障列表
  getFaultList(userId, page = 1, size = 10, status = null) {
    return request({
      url: '/api/weixin/list',
      method: 'GET',
      data: {
        userId,
        page,
        size,
        status
      }
    });
  },

  // 获取故障详情
  getFaultDetail(id) {
    return request({
      url: `/api/faults/detail/${id}`,
      method: 'GET'
    });
  },

  // 获取用户故障历史记录
  getFaultHistory(userId, page = 1, size = 10, status = null) {
    // 仅在有值时携带 status，避免传空字符串导致后端当作有效条件
    const data = { userId, page, size };
    if (status) {
      data.status = status;
    }
    return request({
      url: '/api/weixin/fault-history',
      method: 'GET',
      data
    });
  },

  // 获取故障详情及跟踪记录（无需用户ID）
  getFaultDetailWithTracking(faultId) {
    return request({
      url: '/api/weixin/fault-detail-by-id',
      method: 'GET',
      data: { faultId }
    });
  },

  // 获取故障统计信息
  getFaultStatistics(userId) {
    return request({
      url: '/api/weixin/fault-statistics',
      method: 'GET',
      data: {
        userId
      }
    });
  }
};

/**
 * 消息相关API
 */
const messageApi = {
  // 获取消息列表
  getMessageList(data) {
    return request({
      url: '/api/messages',
      method: 'GET',
      data
    });
  },
  
  // 标记消息已读
  markMessageRead(messageId) {
    return request({
      url: `/api/messages/${messageId}/read`,
      method: 'PUT'
    });
  }
};

/**
 * 文件上传API
 */
const uploadApi = {
  // 上传文件
  uploadFile(filePath, type = 'image', module = 'general') {
    return new Promise((resolve, reject) => {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      
      const header = {};
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      }
      if (userInfo && userInfo.id) {
        header['X-User-Id'] = userInfo.id.toString();
      }
      
      wx.uploadFile({
        url: 'http://127.0.0.1:8889/api/upload',
        filePath: filePath,
        name: 'file',
        formData: {
          type: type,
          module: module,
          userId: userInfo?.id || ''
        },
        header: header,
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              resolve(data.data);
            } else {
              reject(data);
            }
          } catch (e) {
            reject({ message: '上传失败' });
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
};

/**
 * 字典数据API
 */
const dictApi = {
  // 根据字典ID获取字典数据列表 - 使用微信专用接口
  getDictDataByDictId(dictId) {
    return request({
      url: `/api/weixin/data/${dictId}`,
      method: 'GET'
    }).catch(error => {
      console.error('获取字典数据失败，使用默认数据:', error);

      // 如果接口调用失败，使用默认的故障类型数据作为备选
      const defaultFaultTypes = [
        { name: '供暖不热', code: 'heating_not_hot', value: '供暖不热' },
        { name: '暖气漏水', code: 'radiator_leak', value: '暖气漏水' },
        { name: '暖气片不热', code: 'radiator_not_hot', value: '暖气片不热' },
        { name: '管道堵塞', code: 'pipe_blocked', value: '管道堵塞' },
        { name: '阀门故障', code: 'valve_fault', value: '阀门故障' },
        { name: '温控器故障', code: 'thermostat_fault', value: '温控器故障' },
        { name: '其他故障', code: 'other_fault', value: '其他故障' }
      ];

      // 返回默认数据，保持API响应格式一致
      return Promise.resolve({
        code: 200,
        message: '获取字典数据成功(默认数据)',
        data: defaultFaultTypes
      });
    });
  }
};

/**
 * 停供申请API
 */
const stopSupplyApi = {
  // 提交停供申请
  submitApply(data) {
    return request({
      url: '/api/weixin/stop-supply/apply',
      method: 'POST',
      data
    });
  },

  // 获取停供申请列表
  getApplyList(houseId) {
    return request({
      url: '/api/weixin/stop-supply/list',
      method: 'GET',
      data: houseId ? { houseId } : {}
    });
  }
};

/**
 * 系统相关API
 */
const systemApi = {
  // 获取系统参数
  getSystemParams() {
    return request({
      url: '/api/system/params',
      method: 'GET'
    });
  },

  // 获取字典数据 - 使用微信专用接口
  getDictData(dictId) {
    return request({
      url: `/api/weixin/data/${dictId}`,
      method: 'GET'
    });
  }
};

module.exports = {
  userApi,
  billApi,
  bindApi,
  paymentApi,
  workOrderApi,
  faultApi,
  dictApi,
  stopSupplyApi,
  messageApi,
  uploadApi,
  systemApi
};













