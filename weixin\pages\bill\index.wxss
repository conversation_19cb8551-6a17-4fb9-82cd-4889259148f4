/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 40rpx;
}

/* 页面标题 */
.page-title {
  font-size: 40rpx;
  font-weight: 700;
  text-align: center;
  padding: 60rpx 0 40rpx;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 30rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  color: #999;
}

.loading text {
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 账单区域 */
.bill-section {
  padding: 30rpx  30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  color: #333;
  padding-left: 10rpx;
  border-left: 6rpx solid #667eea;
}

/* 账单列表 */
.bill-list {
  margin-bottom: 30rpx;
}

.bill-item {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.bill-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

/* 账单头部 */
.bill-item-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.bill-item-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.3) 100%);
}

.bill-status-inline {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
}

.status-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.status-text {
  font-size: 26rpx;
  font-weight: 600;
  color: white;
}

.bill-period-inline {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 账单内容 */
.bill-item-content {
  padding: 40rpx 30rpx 30rpx;
  background: white;
}

.bill-info {
  margin-bottom: 30rpx;
}

/* 金额显示 */
.amount-text {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.amount-text::before {
  content: '应缴金额：';
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.amount-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b35;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.paid-amount-text {
  display: block;
  font-size: 28rpx;
  color: #52c41a;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.paid-amount-text::before {
  margin-right: 4rpx;
}

/* 日期信息 */
.due-date, .pay-time {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  position: relative;
}

.due-date::before {
  position: absolute;
  left: 0;
  font-size: 20rpx;
}

.pay-time::before {
  content: '💰';
  position: absolute;
  left: 0;
  font-size: 20rpx;
}

/* 操作按钮区域 */
.bill-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  min-width: 140rpx;
  text-align: center;
}

.pay-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.3);
}

.pay-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
}

/* 状态样式 */
.status-text.unpaid,
.status-text.overdue {
  color: white;
}

.status-text.paid {
  color: white;
}

.status-text.partial_paid {
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  background: white;
  border-radius: 24rpx;
  margin: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
}

.empty-state::before {
  content: '📋';
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-state text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .bill-item {
    margin: 0 20rpx 20rpx;
  }
  
  .bill-item-header,
  .bill-item-content {
    padding: 25rpx 20rpx;
  }
  
  .amount-text {
    font-size: 42rpx;
  }
  
  .section-title {
    font-size: 30rpx;
    margin-bottom: 25rpx;
  }
}

/* 加载动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 卡片悬浮效果 */
.bill-item {
  position: relative;
}

.bill-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 24rpx;
  z-index: -1;
}

.bill-item:active::before {
  opacity: 1;
}

