<scroll-view scroll-y="true" class="page-container">
  <view class="container">
    <!-- 页面标题 -->
    <!-- <view class="page-header">
      <text class="page-title">缴费记录</text>
      <text class="house-info">{{houseInfo}}</text>
    </view> -->

    <!-- 统计信息卡片 -->
    <view class="summary-card" wx:if="{{summary}}">
      <view class="summary-item">
        <text class="summary-label">总缴费次数</text>
        <text class="summary-value">{{summary.totalCount}}次</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">累计缴费金额</text>
        <text class="summary-value amount">¥{{summary.totalAmount}}</text>
      </view>
    </view>

    <!-- 缴费记录列表 -->
    <view class="records-list" wx:if="{{paymentRecords.length > 0}}">
      <view class="record-item" wx:for="{{paymentRecords}}" wx:key="id">
        <view class="record-header">
          <view class="record-status">
            <text class="status-icon">✅</text>
            <text class="status-text">支付成功</text>
          </view>
          <text class="record-amount">¥{{item.amount}}</text>
        </view>
        
        <view class="record-content">
          <view class="record-info">
            <view class="info-row">
              <text class="info-label">供暖期间：</text>
              <text class="info-value">{{item.period}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付时间：</text>
              <text class="info-value">{{item.paymentTime}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付方式：</text>
              <text class="info-value">{{item.paymentMethod}}</text>
            </view>
            <view class="info-row" wx:if="{{item.transactionNo}}">
              <text class="info-label">交易流水号：</text>
              <text class="info-value">{{item.transactionNo}}</text>
            </view>
          </view>
          
          <!-- 功能按钮 -->
          <view class="action-buttons">
            <button class="action-btn preview-btn" bindtap="previewInvoice" data-id="{{item.id}}">
              预览票据
            </button>
            <button class="action-btn download-btn" bindtap="downloadPDF" data-id="{{item.id}}">
              下载PDF
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{paymentRecords.length === 0 && !loading}}">
      <view class="empty-icon">💳</view>
      <text class="empty-title">暂无缴费记录</text>
      <text class="empty-desc">您还没有任何缴费记录</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</scroll-view>
