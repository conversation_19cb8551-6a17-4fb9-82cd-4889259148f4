const app = getApp();

Page({
  data: {
    form: {
      station_id: '',     // 换热站ID
      fault_type: '',     // 故障类型
      fault_level: '',    // 故障等级
      fault_desc: '',     // 故障描述
      occur_time: '',     // 发生时间
      images: [],          // 图片列表
      report_user_id: ''   // 上报人员ID
    },
    stations: [],         // 换热站列表
    stationIndex: -1,      // 当前选中的换热站索引
    types: [],           // 故障类型列表
    typeIndex: -1,        // 当前选中的类型索引
    levels: [],          // 故障等级列表
    levelIndex: -1,       // 当前选中的等级索引
    videoUrl: '',        // 视频路径
    dateTimeArray: [],    // 日期时间选择器数组
    dateTime: [],        // 当前选择的日期时间
  },

  // 页面加载时获取数据
  onLoad() {
    this.loadStations();
    this.loadTypes();
    this.loadLevels();
    this.initDateTime();
  },

  // 加载换热站列表
  loadStations() {
    wx.request({
      url: `http://127.0.0.1:5000/api/stations`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          this.setData({ 
            stations: res.data.data
          });
        }
      }
    });
  },

  // 加载故障类型
  loadTypes() {
    wx.request({
      url: `http://127.0.0.1:5000/api/dict/data`,
      method: 'GET',
      data: {
        dict_id: 21  // 故障类型的字典ID
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({ 
            types: res.data.data
          });
        }
      }
    });
  },

  // 加载故障等级
  loadLevels() {
    wx.request({
      url: `http://127.0.0.1:5000/api/dict/data`,
      method: 'GET',
      data: {
        dict_id: 7  // 故障等级的字典ID
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({ 
            levels: res.data.data
          });
        }
      }
    });
  },

  // 换热站选择
  bindStationChange(e) {
    const index = e.detail.value;
    const station = this.data.stations[index];
    this.setData({
      stationIndex: index,
      'form.station_id': station.id
    });
  },

  // 故障类型选择
  onTypeChange(e) {
    const index = e.detail.value;
    const selectedType = this.data.types[index];
    this.setData({
      typeIndex: index,
      'form.fault_type': selectedType.name
    });
  },

  // 故障等级选择
  onLevelChange(e) {
    const index = parseInt(e.detail.value, 10);  // 确保转换为数字
    const levelValue = index + 1;
    const selectedLevel = this.data.levels[index];
    this.setData({
      levelIndex: index,
      'form.fault_level': levelValue
      // 'form.fault_level': selectedLevel.code
    });
  },

  // 故障描述输入
  onDescInput(e) {
    console.log('故障描述输入：', e.detail.value); // 调试日志
    this.setData({
      'form.fault_desc': e.detail.value
    });
  },
  // 初始化日期时间选择器
  initDateTime() {
    const date = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];
    
    // 生成年份选项（当前年份和前一年）
    for (let i = date.getFullYear() - 1; i <= date.getFullYear(); i++) {
      years.push(i + '年');
    }
    
    // 生成月份选项
    for (let i = 1; i <= 12; i++) {
      months.push(i + '月');
    }
    
    // 生成日期选项
    for (let i = 1; i <= 31; i++) {
      days.push(i + '日');
    }
    
    // 生成小时选项
    for (let i = 0; i < 24; i++) {
      hours.push(i + '时');
    }
    
    // 生成分钟选项
    for (let i = 0; i < 60; i++) {
      minutes.push(i + '分');
    }
    
    // 设置日期时间数组和默认值
    this.setData({
      dateTimeArray: [years, months, days, hours, minutes],
      dateTime: [1, date.getMonth(), date.getDate() - 1, date.getHours(), date.getMinutes()]
    });
  },

  // 日期时间选择改变事件
  onDateTimeChange(e) {
    const arr = e.detail.value;
    const dateTimeArray = this.data.dateTimeArray;
    
    // 构建日期时间字符串
    const year = dateTimeArray[0][arr[0]].replace('年', '');
    const month = dateTimeArray[1][arr[1]].replace('月', '').padStart(2, '0');
    const day = dateTimeArray[2][arr[2]].replace('日', '').padStart(2, '0');
    const hour = dateTimeArray[3][arr[3]].replace('时', '').padStart(2, '0');
    const minute = dateTimeArray[4][arr[4]].replace('分', '').padStart(2, '0');
    
    const occur_time = `${year}-${month}-${day} ${hour}:${minute}:00`;
    
    this.setData({
      dateTime: arr,
      'form.occur_time': occur_time
    });
    
    console.log('选择的时间：', occur_time); // 调试用
  },
  // 从相册选择图片
  chooseFromAlbum() {
    wx.chooseImage({
      count: 4 - this.data.form.images.length, // 最多可选择的图片数量
      sizeType: ['compressed'], // 压缩图
      sourceType: ['album'],
      success: (res) => {
        this.setData({
          'form.images': [...this.data.form.images, ...res.tempFilePaths]
        });
        console.log('当前图片列表：', this.data.form.images);
      }
    });
  },

  // 拍照上传
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.setData({
          'form.images': [...this.data.form.images, ...res.tempFilePaths]
        });
        console.log('当前图片列表：', this.data.form.images);
      }
    });
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.form.images;
    images.splice(index, 1);
    this.setData({
      'form.images': images
    });
    console.log('删除后的图片列表：', this.data.form.images);
  },

  // 预览图片
  previewImage(e) {
    const current = e.currentTarget.dataset.url;
    wx.previewImage({
      current,
      urls: this.data.form.images
    });
  },

  // 选择视频
  chooseVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60, // 最长60秒
      camera: 'back',
      success: (res) => {
        console.log('选择视频成功：', res.tempFilePath);
        this.setData({
          videoUrl: res.tempFilePath
        });
      }
    });
  },

  // 删除视频
  deleteVideo() {
    this.setData({
      videoUrl: ''
    });
    console.log('视频已删除');
  },

  // 表单提交
  submitForm() {
    console.log('提交时的表单数据：', this.data.form);

    // 表单验证
    if (!this.data.form.station_id) {
      wx.showToast({
        title: '请选择换热站',
        icon: 'none'
      });
      return;
    }

    if (!this.data.form.fault_type) {
      wx.showToast({
        title: '请选择故障类型',
        icon: 'none'
      });
      return;
    }

    if (!this.data.form.fault_level) {
      wx.showToast({
        title: '请选择故障等级',
        icon: 'none'
      });
      return;
    }

    if (!this.data.form.fault_desc || this.data.form.fault_desc.trim() === '') {
      wx.showToast({
        title: '请输入故障描述',
        icon: 'none'
      });
      return;
    }

    if (!this.data.form.occur_time) {
      wx.showToast({
        title: '请选择故障发生时间',
        icon: 'none'
      });
      return;
    }

    if (this.data.form.images.length === 0) {
      wx.showToast({
        title: '请至少上传一张故障照片',
        icon: 'none'
      });
      return;
    }

    const userInfo = wx.getStorageSync('userInfo'); 
    this.data.form.report_user_id = userInfo.id; 
    // 提交表单
    wx.showLoading({
      title: '提交中...'
    });

    wx.request({
      url: `http://127.0.0.1:5000/api/fault/report`,
      method: 'POST',
      data: this.data.form,
      success: (res) => {
        if (res.data.success) {
          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          wx.showToast({
            title: res.data.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  }
}); 