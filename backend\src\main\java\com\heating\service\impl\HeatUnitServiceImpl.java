package com.heating.service.impl;

import com.heating.repository.HeatUnitRepository;
import com.heating.service.HeatUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service; 
import com.heating.dto.heatUnit.HeatUnitDTO;
import com.heating.dto.heatUnit.HeatUnitCountResponse;

import java.util.List; 

@Service
public class HeatUnitServiceImpl implements HeatUnitService {
 
    @Autowired
    private HeatUnitRepository heatUnitRepository;

    @Override
    public List<HeatUnitDTO> getHeatUnits() {
        return heatUnitRepository.findHeatUnits();
    }

    @Override
    public HeatUnitCountResponse getHeatUnitCount() {
        // 统计热用户总数
        long count = heatUnitRepository.count();
        return new HeatUnitCountResponse((int) count);
    }
} 