package com.heating.dto.patrol;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 巡检项目字典响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatrolItemDictionaryResponse {
    private Long id;
    private String itemCode;
    private String itemName;
    private Long categoryId;
    private String categoryName;
    private String paramType; // text/number/boolean/selection
    private String unit; 
    private String checkMethod;
    private String importance; // critical/important/normal
    private String description;
    private Boolean isActive;
} 