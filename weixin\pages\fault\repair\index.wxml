<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">在线报修</text>
      <text class="header-subtitle">请详细填写故障信息，我们将尽快为您处理</text>
    </view>
  </view>

  <!-- 报修表单 -->
  <form bindsubmit="submitRepair">
    <view class="form-container">
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">基本信息</text>
        </view>
        
        <view class="form-item">
          <text class="label required">故障类型</text>
          <picker
            bindchange="onFaultTypeChange"
            value="{{faultTypeIndex}}"
            range="{{faultTypes}}"
            range-key="name"
            disabled="{{faultTypesLoading}}">
            <view class="picker {{faultTypeIndex === -1 ? 'placeholder' : ''}} {{faultTypesLoading ? 'loading' : ''}}">
              {{faultTypesLoading ? '加载中...' : (faultTypeIndex === -1 ? '请选择故障类型' : faultTypes[faultTypeIndex].name)}}
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="label required">故障等级</text>
          <view class="level-selector">
            <view 
              class="level-item {{faultLevel === item.value ? 'active' : ''}}"
              wx:for="{{faultLevels}}" 
              wx:key="value"
              bindtap="selectFaultLevel"
              data-level="{{item.value}}">
              <text class="level-text">{{item.name}}</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="label required">发生时间</text>
          <picker 
            mode="multiSelector" 
            value="{{dateTime}}" 
            range="{{dateTimeArray}}" 
            bindchange="onDateTimeChange">
            <view class="picker {{!occurTime ? 'placeholder' : ''}}">
              {{occurTime || '请选择故障发生时间'}}
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 故障描述 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">故障描述</text>
        </view>
        
        <view class="form-item">
          <text class="label required">详细描述</text>
          <textarea 
            class="textarea-field" 
            value="{{faultDesc}}"
            bindinput="onDescInput"
            placeholder="请详细描述故障情况，包括故障现象、影响范围等，便于维修人员快速定位问题"
            placeholder-class="textarea-placeholder"
            maxlength="500"
            show-confirm-bar="{{false}}"
          ></textarea>
          <view class="char-count">{{faultDesc.length}}/500</view>
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">现场照片</text>
          <text class="title-subtitle">上传故障现场照片，有助于快速诊断问题</text>
        </view>
        
        <view class="upload-container">
          <view class="image-grid">
            <!-- 已上传的图片 -->
            <view 
              class="image-item" 
              wx:for="{{imageList}}" 
              wx:key="*this">
              <image 
                src="{{item}}" 
                mode="aspectFill" 
                bindtap="previewImage" 
                data-url="{{item}}"
                class="uploaded-image"
              ></image>
              <view 
                class="delete-btn" 
                catchtap="deleteImage" 
                data-index="{{index}}">
                <text class="delete-icon">×</text>
              </view>
            </view>
            
            <!-- 上传按钮 -->
            <view class="upload-actions" wx:if="{{imageList.length < 6}}">
              <view class="upload-btn" bindtap="chooseFromAlbum">
                <text class="upload-icon">📁</text>
                <text class="upload-text">相册</text>
              </view>
              <view class="upload-btn" bindtap="takePhoto">
                <text class="upload-icon">📷</text>
                <text class="upload-text">拍照</text>
              </view>
            </view>
          </view>
          <view class="upload-tips">
            <text class="tips-text">• 最多可上传6张照片</text>
            <text class="tips-text">• 建议上传清晰的故障现场照片</text>
            <text class="tips-text">• 支持JPG、PNG格式，单张不超过5MB</text>
          </view>
        </view>
      </view>

    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <button 
        class="submit-btn {{isSubmitting ? 'submitting' : ''}}" 
        form-type="submit"
        disabled="{{isSubmitting}}">
        {{isSubmitting ? '提交中...' : '提交报修'}}
      </button>
    </view>
  </form>
</view>
