package com.heating.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.heating.util.ApiResponse;

import com.heating.dto.valve.ValveListRequest;
import com.heating.dto.valve.ValveControlRequest;
import com.heating.service.ValveService;

@RestController
@RequestMapping("/api/valves")
public class ValveController {

    private static final Logger logger = LoggerFactory.getLogger(ValveController.class);
    
    @Autowired
    private ValveService valveService;
    
    /**
     * 获取入户阀门列表
     * @param request 包含查询条件的请求
     * @return 入户阀门列表
     */
    @PostMapping("/list")
    public ResponseEntity<?> getValveList(@RequestBody ValveListRequest request) {
        logger.info("Accessing POST /api/valves/list");
        try {
            List<Map<String, Object>> valveList = valveService.getValveList(request);
            return ResponseEntity.ok(ApiResponse.success("入户阀门列表获取成功", valveList.get(0).get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/valves/list: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error("获取入户阀门列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 控制入户阀门（设置开度）
     * @param request 包含控制命令的请求
     * @return 控制结果
     */
    @PostMapping
    public ResponseEntity<?> controlValve(@RequestBody ValveControlRequest request) {
        logger.info("Accessing POST /api/valves");
        try {
            Map<String, Object> controlResult = valveService.controlValve(request);
            return ResponseEntity.ok(ApiResponse.success("入户阀门开度设置成功", controlResult.get("data")));
        } catch (Exception e) {
            logger.error("Error in POST /api/valves: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error("入户阀门开度设置失败: " + e.getMessage()));
        }
    }
} 