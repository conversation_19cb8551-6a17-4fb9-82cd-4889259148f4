package com.heating.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.heating.dto.order.AcceptOrderRequest;
import com.heating.dto.order.CompleteOrderRequest;
import com.heating.dto.order.CreateOrderRequest;
import com.heating.dto.order.TransferOrderRequest;
import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.dto.order.WorkOrderMessageResponse;

/**
 * Service interface for managing work orders
 */
public interface WorkOrderService {

    /**
     * Complete a work order
     * @param orderId The ID of the work order
     * @param request The request containing completion details
     */
    void completeOrder(CompleteOrderRequest request);

    /**
     * Get a list of work orders
     * @param date The date to filter by (optional)
     * @param status The status to filter by (optional)
     * @param userId The user ID to filter by (optional)
     * @param orderNo The order number to filter by (optional)
     * @param role The user role (optional)
     * @param limit The limit of records to return (optional)
     * @param page The page number (1-based)
     * @param pageSize The number of items per page
     * @return Map containing work orders list and pagination info
     */
    Map<String, Object> getWorkOrderList(Date date, String status, Long userId, String orderNo, String role, Integer limit, Integer page, Integer pageSize);

    /**
     * Get details of a specific work order
     * @param orderId The ID of the work order
     * @return Work order details
     */
    WorkOrderDetailResponse getWorkOrderDetail(long orderId); 
    
    /**
     * Create a new work order
     * @param request The request containing work order information
     */
    void createWorkOrder(CreateOrderRequest request);

    /**
     * Update the status of a work order
     * @param request The request containing update details
     */
    void updateWorkOrderStatus(AcceptOrderRequest request);
    
    /**
     * 获取工单消息列表（待接单状态的工单）
     * @param userId 用户ID (optional)
     * @param role 用户角色 (optional)，如果是'admin'，则获取所有记录，否则根据userId过滤
     * @return 待接单的工单消息列表
     */
    List<WorkOrderMessageResponse> getWorkOrderMessages(Long userId, String role);
    
    /**
     * 转派工单
     * @param request 转派工单请求，包含工单ID、转派人ID、转派目标人员ID、转派原因和转派时间
     */
    void transferOrder(TransferOrderRequest request);

    /**
     * 根据故障ID获取跟踪信息（工单信息和处理记录）
     * @param faultId 故障ID
     * @return 跟踪信息，包含工单状态、处理记录等
     */
    Map<String, Object> getTrackingInfoByFaultId(Long faultId);
}
