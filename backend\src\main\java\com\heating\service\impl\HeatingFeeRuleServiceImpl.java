package com.heating.service.impl;

import com.heating.dto.bill.HeatingFeeRuleResponse;
import com.heating.entity.bill.THeatingFeeRule;
import com.heating.repository.THeatingFeeRuleRepository;
import com.heating.service.HeatingFeeRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Slf4j
@Service
public class HeatingFeeRuleServiceImpl implements HeatingFeeRuleService {

    @Autowired
    private THeatingFeeRuleRepository heatingFeeRuleRepository;

    @Override
    public HeatingFeeRuleResponse getHeatingFeeRuleById(Long id) {
        try {
            log.info("获取供暖计费规则详情: id={}", id);
            
            Optional<THeatingFeeRule> ruleOpt = heatingFeeRuleRepository.findActiveById(id);
            if (!ruleOpt.isPresent()) {
                throw new RuntimeException("供暖计费规则不存在或已停用");
            }
            
            THeatingFeeRule rule = ruleOpt.get();
            return convertToResponse(rule);
            
        } catch (Exception e) {
            log.error("获取供暖计费规则失败", e);
            throw new RuntimeException("获取供暖计费规则失败: " + e.getMessage());
        }
    }

    @Override
    public BigDecimal getUnitPriceByRuleId(Long ruleId) {
        try {
            if (ruleId == null) {
                log.warn("规则ID为空，使用默认单价");
                return new BigDecimal("28.00");
            }
            
            Optional<THeatingFeeRule> ruleOpt = heatingFeeRuleRepository.findActiveById(ruleId);
            if (ruleOpt.isPresent()) {
                BigDecimal unitPrice = ruleOpt.get().getUnitPrice();
                log.info("获取到规则ID={}的单价: {}", ruleId, unitPrice);
                return unitPrice;
            } else {
                log.warn("未找到规则ID={}的计费规则，使用默认单价", ruleId);
                return new BigDecimal("28.00");
            }
            
        } catch (Exception e) {
            log.error("获取单价失败，使用默认单价", e);
            return new BigDecimal("28.00");
        }
    }

    @Override
    public HeatingFeeRuleResponse getCurrentActiveRule() {
        try {
            log.info("获取当前生效的供暖计费规则");
            
            Optional<THeatingFeeRule> ruleOpt = heatingFeeRuleRepository.findCurrentActiveRule();
            if (!ruleOpt.isPresent()) {
                throw new RuntimeException("未找到当前生效的供暖计费规则");
            }
            
            THeatingFeeRule rule = ruleOpt.get();
            return convertToResponse(rule);
            
        } catch (Exception e) {
            log.error("获取当前生效的供暖计费规则失败", e);
            throw new RuntimeException("获取当前生效的供暖计费规则失败: " + e.getMessage());
        }
    }

    private HeatingFeeRuleResponse convertToResponse(THeatingFeeRule rule) {
        HeatingFeeRuleResponse response = new HeatingFeeRuleResponse();
        response.setId(rule.getId());
        response.setUnitPrice(rule.getUnitPrice());
        response.setHeatingStartDate(rule.getHeatingStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        response.setHeatingEndDate(rule.getHeatingEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        response.setMinPaymentRate(rule.getMinPaymentRate());
        response.setPenaltyRate(rule.getPenaltyRate());
        response.setIsActive(rule.getIsActive());
        response.setCreatedAt(rule.getCreatedAt() != null ? 
            rule.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        response.setUpdatedAt(rule.getUpdatedAt() != null ? 
            rule.getUpdatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        return response;
    }
}