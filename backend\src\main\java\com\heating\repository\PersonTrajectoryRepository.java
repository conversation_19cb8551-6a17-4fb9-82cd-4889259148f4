package com.heating.repository;

import com.heating.entity.PersonTrajectory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员轨迹数据访问接口
 */
@Repository
public interface PersonTrajectoryRepository extends JpaRepository<PersonTrajectory, Integer> {

    /**
     * 根据用户ID查询轨迹记录
     * @param userId 用户ID
     * @return 轨迹记录列表
     */
    List<PersonTrajectory> findByUserId(Integer userId);

    /**
     * 根据用户ID和时间范围查询轨迹记录
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹记录列表
     */
    List<PersonTrajectory> findByUserIdAndCreateTimeBetween(Integer userId, LocalDateTime startTime, LocalDateTime endTime);
} 