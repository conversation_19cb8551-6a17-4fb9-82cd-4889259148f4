package com.heating.service;

import com.heating.dto.alarm.AlarmMessageResponse;
import java.util.List;

/**
 * 告警服务接口
 */
public interface AlarmService {
    
    /**
     * 获取告警消息
     * 获取当前所有需要处理的告警消息列表
     * 
     * @return 告警消息列表
     */
    List<AlarmMessageResponse> getAlarmMessages();

    /**
     * 更新告警状态
     * 根据告警ID更新告警记录的状态
     * 
     * @param alarmId 告警记录ID
     * @param status 新的告警状态 (1-已确认, 2-已完成, 3-已忽略)
     * @throws Exception 如果更新失败或告警不存在
     */
    void updateAlarmStatus(Long alarmId, Integer status) throws Exception;
} 