package com.heating.dto.attendance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class AttendanceClockRequest {
    @JsonProperty("user_id")
    private Long userId;
    
    @JsonProperty("clock_type")
    private String clockType;
    
    @JsonProperty("latitude")
    private Double latitude;
    
    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("status")
    private String status;
    
//    @JsonProperty("face_photo")
//    private List<String> facePhoto;
//
//    @JsonProperty("liveness_data")
//    private Map<String, Object> livenessData;
    
    @JsonProperty("outdoor_flag")
    private Integer outdoorFlag;
    
    @JsonProperty("leave_type")
    private String leaveType;
    
    @JsonProperty("leave_reason")
    private String leaveReason;
    
//    @JsonProperty("leave_proof")
//    private List<String> leaveProof;
} 