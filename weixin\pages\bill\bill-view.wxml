<view class="container">
  <!-- 页面头部 - 参考repair样式，去掉标题文字 -->
  <view class="page-header">
    <view class="header-actions">
      <text class="change-year-btn" bindtap="changeHeatingYear">
        {{(basicInfo.heatingYear ? basicInfo.heatingYear + '-' + (basicInfo.heatingYear + 1) + '供暖年度' : (heatingYear ? heatingYear + '-' + (heatingYear + 1) + '供暖年度' : '选择年度'))}}
      </text>
      <text class="refresh-btn" bindtap="onRefresh">刷新</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 账单详情内容 -->
  <view class="bill-content" wx:if="{{!loading && (billDetail || billInfo.status === 'no_bill')}}">
    
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">基本信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">户号：</text>
          <text class="info-value">{{basicInfo.houseNumber}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">地址：</text>
          <text class="info-value">{{basicInfo.address}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">面积：</text>
          <text class="info-value">{{basicInfo.area}}㎡</text>
        </view>
        <view class="info-row">
          <text class="info-label">供暖年度：</text>
          <text class="info-value">{{basicInfo.heatingYear}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">供暖状态：</text>
          <text class="info-value status-{{basicInfo.heatingStatus}}">{{basicInfo.heatingStatusText}}</text>
        </view>
      </view>
    </view>

    <!-- 账单信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">账单信息</text>
        <view class="bill-status status-{{billInfo.status}}">{{billInfo.statusText}}</view>
      </view>

      <!-- 有账单数据时显示详细信息 -->
      <view class="card-content" wx:if="{{billInfo.status !== 'no_bill'}}">
        <view class="amount-section">
          <!-- 用热费用/管网维护费 -->
          <view class="amount-row">
            <text class="amount-label">{{billInfo.feeTypeName || '用热费'}}：</text>
            <text class="amount-value">¥{{billInfo.heatingFee || billInfo.totalAmount}}</text>
          </view>

          <!-- 欠费金额 -->
          <view class="amount-row" wx:if="{{billInfo.overdueAmount && billInfo.overdueAmount > 0}}">
            <text class="amount-label">欠费金额：</text>
            <text class="amount-value debt">¥{{billInfo.overdueAmount}}</text>
          </view>

          <!-- 应缴费金额 -->
          <view class="amount-row main-amount">
            <text class="amount-label">应缴费：</text>
            <text class="amount-value">¥{{billInfo.totalPayableAmount || billInfo.totalAmount}}</text>
          </view>

          <!-- 实际缴费金额（仅已缴费状态显示） -->
          <view class="amount-row" wx:if="{{billInfo.showActualPaidAmount}}">
            <text class="amount-label">实际缴费：</text>
            <text class="amount-value paid">¥{{billInfo.actualPaidAmount || billInfo.paidAmount}}</text>
          </view>

          <!-- 剩余金额 -->
          <view class="amount-row" wx:if="{{billInfo.remainingAmount > 0}}">
            <text class="amount-label">剩余金额：</text>
            <text class="amount-value remaining">¥{{billInfo.remainingAmount}}</text>
          </view>
        </view>

        <view class="info-row">
          <text class="info-label">单价：</text>
          <text class="info-value">¥{{billInfo.unitPrice}}/㎡</text>
        </view>
        <view class="info-row" wx:if="{{billInfo.dueDate}}">
          <text class="info-label">缴费截止：</text>
          <text class="info-value">{{billInfo.dueDate}}</text>
        </view>
        <view class="info-row" wx:if="{{billInfo.lastPaidDate}}">
          <text class="info-label">最后缴费：</text>
          <text class="info-value">{{billInfo.lastPaidDate}}</text>
        </view>
        <view class="info-row" wx:if="{{billInfo.remark}}">
          <text class="info-label">备注：</text>
          <text class="info-value">{{billInfo.remark}}</text>
        </view>
      </view>

      <!-- 无账单数据时显示提示信息 -->
      <view class="card-content" wx:if="{{billInfo.status === 'no_bill'}}">
        <view class="no-bill-tip">
          <view class="no-bill-icon">📋</view>
          <text class="no-bill-title">暂无此供暖季的账单信息</text>
          <text class="no-bill-desc">账单将在供暖季开始前生成，请耐心等待</text>
        </view>
      </view>
    </view>

    <!-- 逾期信息卡片 -->
    <view class="info-card warning-card" wx:if="{{overdueInfo.hasOverdue && billInfo.status !== 'no_bill'}}">
      <view class="card-header">
        <text class="card-title">逾期信息</text>
        <text class="warning-badge">逾期</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">逾期天数：</text>
          <text class="info-value warning">{{overdueInfo.overdueDays}}天</text>
        </view>
        <view class="info-row">
          <text class="info-label">欠费金额：</text>
          <text class="info-value warning">¥{{overdueInfo.overdueAmount}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">滞纳金：</text>
          <text class="info-value warning">¥{{overdueInfo.penaltyAmount}}</text>
        </view>
        <view class="info-row" wx:if="{{overdueInfo.firstOverdueDate}}">
          <text class="info-label">首次逾期：</text>
          <text class="info-value">{{overdueInfo.firstOverdueDate}}</text>
        </view>
      </view>
    </view>

    <!-- 缴费记录卡片 -->
    <view class="info-card" wx:if="{{billInfo.status !== 'no_bill'}}">
      <view class="card-header">
        <text class="card-title">缴费记录</text>
        <text class="record-count" wx:if="{{paymentRecords.length > 0}}">共{{paymentRecords.length}}笔</text>
      </view>

      <!-- 有缴费记录时显示列表 -->
      <view class="card-content" wx:if="{{paymentRecords.length > 0}}">
        <view class="payment-record"
              wx:for="{{paymentRecords}}"
              wx:key="paymentId"
              bindtap="viewPaymentDetail"
              data-payment-id="{{item.paymentId}}">
          <view class="record-header">
            <text class="record-amount">¥{{item.amount}}</text>
            <text class="record-method">{{item.paymentMethodText}}</text>
          </view>
          <view class="record-info">
            <text class="record-date">{{item.paymentDate}}</text>
            <text class="record-transaction" wx:if="{{item.transactionNo}}">{{item.transactionNo}}</text>
          </view>
          <view class="record-remark" wx:if="{{item.remark}}">{{item.remark}}</view>
        </view>
      </view>

      <!-- 无缴费记录时显示提示 -->
      <view class="card-content" wx:if="{{paymentRecords.length === 0}}">
        <view class="empty-state">
          <text class="empty-text">暂无缴费记录</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading && (billDetail || billInfo.status === 'no_bill')}}">
    <button class="action-btn secondary" bindtap="contactService">联系客服</button>
    <button class="action-btn primary"
            wx:if="{{billInfo.status =='unpaid'}}"
            bindtap="goToPay">立即缴费</button>
  </view>
</view>
