package com.heating.service.impl;

import com.heating.dto.PersonTrajectoryDTO;
import com.heating.entity.PersonTrajectory;
import com.heating.repository.PersonTrajectoryRepository;
import com.heating.service.PersonTrajectoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 人员轨迹服务实现类
 */
@Service
public class PersonTrajectoryServiceImpl implements PersonTrajectoryService {

    @Autowired
    private PersonTrajectoryRepository personTrajectoryRepository;

    /**
     * 添加轨迹记录
     * @param dto 轨迹记录DTO
     * @return 添加后的轨迹记录
     */
    @Override
    @Transactional
    public PersonTrajectory addTrajectory(PersonTrajectoryDTO dto) {
        PersonTrajectory trajectory = new PersonTrajectory();
        BeanUtils.copyProperties(dto, trajectory);
        
        // 确保employeeId有值，如果没有就使用userId
        if (trajectory.getEmployeeId() == null && trajectory.getUserId() != null) {
            trajectory.setEmployeeId(trajectory.getUserId());
        }
        
        // 设置默认状态为正常(0)
        if (trajectory.getStatus() == null) {
            trajectory.setStatus(0);
        }
        
        return personTrajectoryRepository.save(trajectory);
    }

    /**
     * 批量添加轨迹记录
     * @param dtoList 轨迹记录DTO列表
     * @return 添加后的轨迹记录列表
     */
    @Override
    @Transactional
    public List<PersonTrajectory> addTrajectoryBatch(List<PersonTrajectoryDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<PersonTrajectory> trajectories = dtoList.stream().map(dto -> {
            PersonTrajectory trajectory = new PersonTrajectory();
            BeanUtils.copyProperties(dto, trajectory);
            
            // 确保employeeId有值，如果没有就使用userId
            if (trajectory.getEmployeeId() == null && trajectory.getUserId() != null) {
                trajectory.setEmployeeId(trajectory.getUserId());
            }
            
            // 设置默认状态为正常(0)
            if (trajectory.getStatus() == null) {
                trajectory.setStatus(0);
            }
            
            return trajectory;
        }).collect(Collectors.toList());
        
        return personTrajectoryRepository.saveAll(trajectories);
    }

    /**
     * 根据用户ID查询轨迹记录
     * @param userId 用户ID
     * @return 轨迹记录列表
     */
    @Override
    public List<PersonTrajectory> getTrajectoryByUserId(Integer userId) {
        return personTrajectoryRepository.findByUserId(userId);
    }

    /**
     * 根据用户ID和时间范围查询轨迹记录
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹记录列表
     */
    @Override
    public List<PersonTrajectory> getTrajectoryByUserIdAndTimeRange(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        return personTrajectoryRepository.findByUserIdAndCreateTimeBetween(userId, startTime, endTime);
    }
} 