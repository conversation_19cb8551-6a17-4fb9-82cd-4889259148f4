const { billApi } = require('../../api/index.js');

Page({
  data: {
    loading: false,
    billList: [], // 统一的账单列表
    currentBill: null, // 移除单独的当前账单
    historyBills: [] // 移除单独的历史账单
  },

  onLoad() {
    this.loadBillList();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadBillList();
  },

  loadBillList() {
    this.setData({ loading: true });
    wx.showLoading({
      title: '加载中...'
    });

    billApi.getBillList({
      page: 1,
      pageSize: 20
    }).then(res => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.log('账单:', res);
       if (res && res.data && res.data.bills) {
        // 格式化所有账单数据
        const allBills = res.data.bills.map(bill => ({
          id: bill.id,
          period: bill.period || `${bill.heatYear}-${bill.heatYear + 1} 供暖季`,
          amount: bill.amount,
          paidAmount: bill.paidAmount || '0',
          status: bill.status,
          statusIcon: this.getStatusIcon(bill.status),
          statusText: this.getStatusText(bill.status),
          createDate: bill.createDate,
          dueDate: bill.dueDate,
          lastPaidDate: bill.lastPaidDate,
          heatYear: bill.heatYear
        }));
        console.log('billList',allBills)
        this.setData({
          billList: allBills
        });
      }
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取账单列表失败:', err);
      wx.showToast({
        title: err.message || '获取账单失败',
        icon: 'none'
      });
    });
  },

  goToPay(e) {
    const billId = e.currentTarget.dataset.id;
    console.log('跳转到缴费页面，账单ID:', billId);
    wx.navigateTo({
      url: '/pages/payment/index?billId=' + billId
    });
  },

  viewBillDetail(e) {
    const billId = e.currentTarget.dataset.id;
    console.log('跳转到账单详情，账单ID:', billId);
    wx.navigateTo({
      url: '/pages/bill/detail?id=' + billId
    });
  },

  // 获取状态图标
  getStatusIcon(status) {
    switch(status) {
      case 'unpaid':
      case 'overdue':
        return '🔴';
      case 'paid':
        return '✅';
      case 'partial_paid':
        return '🟡';
      default:
        return '⚪';
    }
  },

  // 获取状态文本
  getStatusText(status) {
    switch(status) {
      case 'unpaid':
        return '待缴费';
      case 'overdue':
        return '已逾期';
      case 'paid':
        return '已缴费';
      case 'partial_paid':
        return '部分缴费';
      default:
        return '未知状态';
    }
  },
});




