package com.heating.entity.patrol;

import com.heating.entity.TDevicePatrolItem;
import lombok.Data;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_patrol_item")
public class TPatrolItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "patrol_plan_id")
    private Long patrolPlanId; 
 
    @Column(name = "device_patrol_item_id")
    private Long devicePatrolItemId;  

    /**
     * 设备ID（临时字段，不存储到数据库）
     */
    @Transient
    private Long deviceId;
    
    /**
     * 巡检项目字典ID（临时字段，不存储到数据库）
     */
    @Transient
    private Long itemDictionaryId;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 