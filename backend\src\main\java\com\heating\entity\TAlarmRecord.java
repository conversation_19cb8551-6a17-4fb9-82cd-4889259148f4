package com.heating.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警记录实体类
 * 对应数据库表 t_alarm_record
 */
@Data
@Entity
@Table(name = "t_alarm_record")
public class TAlarmRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "heat_unit_id")
    private Long heatUnitId;

    @Column(name = "dev_type", length = 50)
    private String devType;

    @Column(name = "dev_code", length = 50)
    private String devCode;

    @Column(name = "alarm_desc", length = 100)
    private String alarmDesc;

    @Column(name = "alarm_field", length = 50)
    private String alarmField;

    @Column(name = "alarm_value", length = 50)
    private String alarmValue;

    @Column(name = "is_alarm")
    private Integer isAlarm;

    @Column(name = "is_handle")
    private Integer isHandle;

    @Column(name = "status")
    private Integer status;

    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "alarm_type")
    private Integer alarmType;

    @Column(name = "alarm_level")
    private Integer alarmLevel;

    @Column(name = "alarm_duration")
    private Integer alarmDuration;

    @Column(name = "alarm_dt")
    private LocalDateTime alarmDt;

    @Column(name = "alarm_clear_dt")
    private LocalDateTime alarmClearDt;

    @Column(name = "unit", length = 50)
    private String unit;

    @Column(name = "remark", length = 50)
    private String remark;
} 