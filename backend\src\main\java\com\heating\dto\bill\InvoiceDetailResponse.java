package com.heating.dto.bill;

import lombok.Data;

@Data
public class InvoiceDetailResponse {
    private String invoiceNo;           // 票据编号
    private String createDate;          // 生成日期
    private String period;              // 供暖期间
    private String ownerName;           // 户主姓名
    private String houseNumber;         // 房屋编号
    private String address;             // 房屋地址
    private String area;                // 供暖面积
    private String unitPrice;           // 单价
    private String amount;              // 应缴金额
    private String paidAmount;          // 实缴金额
    private String paymentMethodText;   // 支付方式
    private String paymentDate;         // 支付时间
    private String transactionNo;       // 交易流水号
    private Long billId;                // 账单ID
    private Long paymentId;             // 缴费记录ID
}