.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.filter-bar {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  color: #333;
  font-size: 28rpx;
}

.fault-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fault-item {
  background: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
}

.fault-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 16rpx;
}

.fault-no {
  font-size: 28rpx;
  color: #333;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
/* 故障状态样式 */
.status.pending {
  color: #ff9f00;
  background: #fff6e5;
}

.status.confirmed {
  background: #f0f9eb;
  color: #67c23a;
}

.status.returned {
  background: #f4f4f5;
  color: #909399;
}

.fault-info {
  font-size: 28rpx;
}

.info-item {
  display: flex;
  margin-bottom: 12rpx;
}

.info-item .label {
  width: 140rpx;
  color: #666;
}

.info-item .value {
  display: flex;
  flex: 1;
  color: #333;
}

/* 故障状态样式 */
.status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status.confirmed {
  background: #f6ffed;
  color: #52c41a;
}

.status.returned {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 故障等级样式 */
.fault-level {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.fault-level.severe {
  background: #fff2f0;
  color: #ff4d4f;
}

.fault-level.important {
  background: #fff7e6;
  color: #fa8c16;
}

.fault-level.normal {
  background: #f6ffed;
  color: #52c41a;
}

/* 旧的等级样式（保持兼容） */
.info-item .level-1 {
  color: #e6a23c;
}

.info-item .level-2 {
  color: #f56c6c;
}

.info-item .level-3 {
  color: #f56c6c;
  font-weight: bold;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin-top: 200rpx;
}

.status-filter {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.filter-btn {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.filter-btn.active {
  color: #fff;
  background: #409eff;
} 