<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">停供申请记录</text>
  </view>

  <!-- 统计信息 -->
  <view class="stats-card" wx:if="{{recordList.length > 0}}">
    <view class="stats-item">
      <text class="stats-number">{{recordList.length}}</text>
      <text class="stats-label">总申请数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{pendingCount}}</text>
      <text class="stats-label">待审批</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{approvedCount}}</text>
      <text class="stats-label">已通过</text>
    </view>
  </view>

  <!-- 申请记录列表 -->
  <scroll-view scroll-y class="record-list" enable-back-to-top>
    <view class="record-item" 
          wx:for="{{recordList}}" 
          wx:key="id"
          bindtap="viewRecordDetail" 
          data-id="{{item.id}}">
      
      <view class="record-header">
        <view class="record-status">
          <text class="status-dot {{item.status}}"></text>
          <text class="status-text {{item.status}}">{{item.statusText}}</text>
        </view>
        <text class="record-date">{{item.applyDate}}</text>
      </view>

      <view class="record-content">
        <view class="content-row">
          <text class="label">停供时间</text>
          <text class="value">{{item.stopPeriod}}</text>
        </view>
        <view class="content-row">
          <text class="label">申请原因</text>
          <text class="value reason">{{item.reason}}</text>
        </view>
        <view class="content-row" wx:if="{{item.approvedAt}}">
          <text class="label">审批时间</text>
          <text class="value">{{item.approvedAt}}</text>
        </view>
      </view>

      <view class="record-footer">
        <text class="heating-year">{{item.heatingYear}}-{{item.heatingYear + 1}}供暖季</text>
        <view class="arrow-icon">></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{recordList.length === 0 && !loading}}">
      <view class="empty-icon">📋</view>
      <text class="empty-title">暂无申请记录</text>
      <text class="empty-desc">您还没有提交过停供申请</text>
      <button class="empty-btn" bindtap="goToApply">立即申请</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view>