<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">报修记录</text>
      <text class="header-subtitle">查看您的历史报修记录和处理状态</text>
    </view>
  </view>

  <!-- 统计信息卡片 -->
  <view class="statistics-card">
    <view class="stat-item">
      <text class="stat-number">{{statistics.totalCount || 0}}</text>
      <text class="stat-label">总报修数</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.pendingCount || 0}}</text>
      <text class="stat-label">待确认</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.confirmedCount || 0}}</text>
      <text class="stat-label">已确认</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.thisMonthCount || 0}}</text>
      <text class="stat-label">本月报修</text>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-container">
    <view class="filter-tabs">
      <view 
        class="filter-tab {{currentStatus === '' ? 'active' : ''}}"
        bindtap="filterByStatus"
        data-status="">
        全部
      </view>
      <view 
        class="filter-tab {{currentStatus === '待确认' ? 'active' : ''}}"
        bindtap="filterByStatus"
        data-status="待确认">
        待确认
      </view>
      <view 
        class="filter-tab {{currentStatus === '已确认' ? 'active' : ''}}"
        bindtap="filterByStatus"
        data-status="已确认">
        已确认
      </view>
      <view 
        class="filter-tab {{currentStatus === '已退回' ? 'active' : ''}}"
        bindtap="filterByStatus"
        data-status="已退回">
        已退回
      </view>
    </view>
  </view>

  <!-- 故障记录列表 -->
  <view class="fault-list">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && faultList.length === 0}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{!loading && faultList.length === 0}}">
      <view class="empty-icon">
        <text class="icon-text">📝</text>
      </view>
      <text class="empty-text">暂无报修记录</text>
      <text class="empty-desc">您还没有提交过故障报修</text>
      <button class="empty-btn" bindtap="goToRepair">立即报修</button>
    </view>

    <!-- 故障记录项（全新简洁卡片样式） -->
    <view
      class="fault-item"
      wx:for="{{faultList}}"
      wx:key="fault_id"
      bindtap="viewFaultDetail"
      data-fault-id="{{item.fault_id}}">

      <!-- 左侧状态色条 -->
      <view class="status-accent {{item.fault_status === '待确认' ? 'pending' : item.fault_status === '已确认' ? 'confirmed' : 'returned'}}"></view>

      <!-- 主体内容 -->
      <view class="item-main">
        <view class="item-header">
          <text class="item-title">{{item.fault_type || '设备故障'}}</text>
          <view class="status-badge {{item.fault_status === '待确认' ? 'pending' : item.fault_status === '已确认' ? 'confirmed' : 'returned'}}">
            {{item.fault_status || '未知状态'}}
          </view>
        </view>

        <view class="item-meta">
          <text class="meta-time">🕒 {{item.report_time || item.created_time}}</text>
        </view>
      </view>

      <!-- 右侧箭头提示可进入详情 -->
      <text class="chevron">›</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <button class="load-more-btn" bindtap="loadMore">加载更多</button>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && faultList.length > 0}}">
    <text class="no-more-text">没有更多数据了</text>
  </view>
</view>
