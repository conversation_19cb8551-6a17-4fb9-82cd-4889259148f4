package com.heating.repository;

import com.heating.entity.bill.TOverdueRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 欠费记录仓库接口
 * 用于操作 t_overdue_records 表
 */
@Repository
public interface TOverdueRecordRepository extends JpaRepository<TOverdueRecord, Long> {
    
    /**
     * 根据房屋ID查询所有有效的欠费记录
     * @param houseId 房屋ID
     * @return 欠费记录列表
     */
    List<TOverdueRecord> findByHouseIdAndStatus(Long houseId, TOverdueRecord.OverdueStatus status);
    
    /**
     * 根据账单ID查询欠费记录
     * @param billId 账单ID
     * @return 欠费记录
     */
    Optional<TOverdueRecord> findByBillId(Long billId);
    
    /**
     * 根据房屋ID查询所有欠费记录，按创建时间倒序
     * @param houseId 房屋ID
     * @return 欠费记录列表
     */
    List<TOverdueRecord> findByHouseIdOrderByCreatedAtDesc(Long houseId);
    
    /**
     * 根据房屋ID和供暖年度查询欠费记录
     * @param houseId 房屋ID
     * @param heatingYear 供暖年度
     * @return 欠费记录列表
     */
    List<TOverdueRecord> findByHouseIdAndHeatingYear(Long houseId, Integer heatingYear);
    
    /**
     * 查询指定房屋的总欠费金额
     * @param houseId 房屋ID
     * @return 总欠费金额
     */
    @Query("SELECT COALESCE(SUM(o.overdueAmount), 0) FROM TOverdueRecord o WHERE o.houseId = :houseId AND o.status = 'active'")
    Double getTotalOverdueAmountByHouseId(@Param("houseId") Long houseId);
    
    /**
     * 查询指定房屋的总滞纳金金额
     * @param houseId 房屋ID
     * @return 总滞纳金金额
     */
    @Query("SELECT COALESCE(SUM(o.penaltyAmount), 0) FROM TOverdueRecord o WHERE o.houseId = :houseId AND o.status = 'active'")
    Double getTotalPenaltyAmountByHouseId(@Param("houseId") Long houseId);
}
