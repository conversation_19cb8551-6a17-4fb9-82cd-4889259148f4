.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 日期筛选 */
.date-filter {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.selected {
  color: #333;
  font-size: 28rpx;
}

/* 温度统计卡片 */
.temp-stats-card {
  background: linear-gradient(135deg, #4CAF50, #2196F3);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  flex: 1;
}

.stat-item .label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  margin-bottom: 10rpx;
  display: block;
}

.stat-item .value {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.value.high {
  color: #FFE7E7;
}

.value.low {
  color: #E6F7FF;
}

/* 温度列表 */
.temp-list {
  margin-top: 20rpx;
}

.temp-item {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.room-info {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.temp-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.temp {
  font-size: 32rpx;
  font-weight: bold;
}

.temp.normal {
  color: #4CAF50;
}

.temp.high {
  color: #F56C6C;
}

.temp.low {
  color: #409EFF;
}

.time {
  font-size: 26rpx;
  color: #999;
}

.remark {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #666;
  display: block;
} 

.filter-section {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.filter-row {
  display: flex;
  gap: 20rpx;
}

.date-picker, .filter-type {
  flex: 1;
  background: #f5f5f5;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.condition-row {
  margin-top: 20rpx;
}

.condition-picker {
  background: #f5f5f5;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.building-input {
  width: 100%;
  box-sizing: border-box;
  background: #f5f5f5;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}