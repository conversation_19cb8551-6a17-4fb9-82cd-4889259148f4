<view class="container">
  <view class="logo-box">
    <image class="logo" src="/images/logo.png"></image>
    <text class="app-name">供热缴费平台</text>
    <!-- <text class="app-subtitle">智慧供热运维平台</text> -->
  </view>

  <!-- 微信授权登录区域 - 暂时保留但不使用 -->
  <view class="wx-login-section" style="display: none;">
    <button class="wx-login-btn" bindtap="handleWxLogin">
      <text class="unicode-icon">💬</text>
      <text>微信一键登录</text>
    </button>
  </view>

  <!-- 手机号登录区域 -->
  <view class="form-box">
    <view class="form-item">
      <text class="wx-icon">📱</text>
      <input 
        class="input" 
        type="number" 
        placeholder="请输入手机号"
        value="{{phone}}"
        bindinput="onPhoneInput"
      />
    </view>
    
    <view class="form-item">
      <text class="wx-icon">🔒</text>
      <input 
        class="input" 
        type="password" 
        placeholder="请输入密码"
        value="{{password}}"
        bindinput="onPasswordInput"
      />
    </view>

    <view class="remember-box">
      <view class="remember-pwd" bindtap="toggleRemember">
        <view class="checkbox {{isRemember ? 'checked' : ''}}">
          <text class="wx-icon" wx:if="{{isRemember}}">✓</text>
        </view>
        <text>记住密码</text>
      </view>
      <text class="forget-pwd" bindtap="onForgetPwd">忘记密码？</text>
    </view>

    <button class="login-btn" type="primary" bindtap="handleLogin">登录</button>
  </view>

  <view class="register-link">
    <text>还没有账号？</text>
    <text class="link" bindtap="goToRegister">立即注册</text>
  </view>

  <view class="footer">
    <text>登录即代表同意</text>
    <text class="link" bindtap="viewUserAgreement">《用户协议》</text>
    <text>和</text>
    <text class="link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
  </view>
</view>
