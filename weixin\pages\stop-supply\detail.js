Page({
  data: {
    recordDetail: {},
    recordId: null,
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ recordId: options.id });
      this.loadRecordDetail();
    } else {
      this.setData({ loading: false });
    }
  },

  loadRecordDetail() {
    this.setData({ loading: true });
    
    wx.request({
      url: `http://127.0.0.1:8889/api/weixin/stop-supply/detail`,
      method: 'GET',
      data: {
        id: this.data.recordId
      },
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        this.setData({ loading: false });
        
        if (res.statusCode === 200 && res.data.code === 200) {
          const record = res.data.data;
          this.setData({
            recordDetail: {
              ...record,
              applyDate: this.formatDate(record.applyDate),
              stopStartDate: this.formatDate(record.stopStartDate),
              stopEndDate: record.stopEndDate ? this.formatDate(record.stopEndDate) : null,
              approvedAt: record.approvedAt ? this.formatDateTime(record.approvedAt) : null,
              statusText: this.getStatusText(record.status),
              statusDesc: this.getStatusDesc(record.status),
              statusIcon: this.getStatusIcon(record.status)
            }
          });
        } else {
          wx.showToast({
            title: res.data?.message || '获取详情失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        this.setData({ loading: false });
        console.error('获取申请详情失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  cancelApply() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个申请吗？取消后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          this.performCancelApply();
        }
      }
    });
  },

  performCancelApply() {
    wx.showLoading({
      title: '取消中...'
    });

    wx.request({
      url: 'http://127.0.0.1:8889/api/weixin/stop-supply/cancel',
      method: 'POST',
      data: {
        id: this.data.recordId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data.code === 200) {
          wx.showToast({
            title: '取消成功',
            icon: 'success'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data?.message || '取消失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateStr);
      return '暂无';
    }

    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    // 打印原始数据用于调试
    console.log('原始审批时间数据:', dateTimeStr, '类型:', typeof dateTimeStr);

    let date;

    // 处理不同的时间格式
    if (Array.isArray(dateTimeStr)) {
      // 如果是数组格式 [2025, 8, 12, 16, 34, 11]
      const [year, month, day, hour, minute, second] = dateTimeStr;
      date = new Date(year, month - 1, day, hour || 0, minute || 0, second || 0);
    } else if (typeof dateTimeStr === 'string') {
      // 如果是字符串格式
      date = new Date(dateTimeStr);
    } else {
      // 其他格式直接尝试转换
      date = new Date(dateTimeStr);
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期时间格式:', dateTimeStr);
      return '暂无';
    }

    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  getStatusText(status) {
    const statusMap = {
      'pending': '待审批',
      'approved': '已通过',
      'rejected': '已拒绝',
      'canceled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  getStatusDesc(status) {
    const descMap = {
      'pending': '您的申请正在审核中，请耐心等待',
      'approved': '您的申请已通过审批',
      'rejected': '很抱歉，您的申请未通过审批',
      'canceled': '申请已被取消'
    };
    return descMap[status] || '';
  },

  getStatusIcon(status) {
    const iconMap = {
      'pending': '⏳',
      'approved': '✅',
      'rejected': '❌',
      'canceled': '🚫'
    };
    return iconMap[status] || '❓';
  }
});
