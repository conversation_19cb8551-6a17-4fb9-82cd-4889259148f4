Page({
  data: {
    invoiceDetail: {}
  },

  onLoad(options) {
    const invoiceId = options.id;
    this.loadInvoiceDetail(invoiceId);
  },

  loadInvoiceDetail(invoiceId) {
    wx.showLoading({
      title: '加载票据中...'
    });

    // 模拟从后端获取票据详情
    setTimeout(() => {
      wx.hideLoading();
      
      const invoiceDetail = {
        invoiceNo: 'PJ20241005001',
        createDate: '2024年10月05日',
        heatingPeriod: '2024年11月15日 - 2025年03月15日',
        ownerName: '张三',
        houseNumber: 'HT2024000123',
        address: '北京市朝阳区XX小区3栋502',
        area: '85.6',
        unitPrice: '28.00',
        totalAmount: '2,400.00',
        paidAmount: '2,400.00',
        paymentMethod: '微信支付',
        orderNo: 'ORD202410051234',
        paymentTime: '2024-10-05 14:30:22'
      };

      this.setData({
        invoiceDetail: invoiceDetail
      });
    }, 800);
  },

  downloadPDF() {
    wx.showLoading({
      title: '生成PDF中...'
    });

    // 模拟PDF生成和下载
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '下载成功',
        content: 'PDF票据已保存到手机相册',
        showCancel: false,
        confirmText: '知道了'
      });
    }, 2000);
  },

  shareInvoice() {
    wx.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '保存图片到相册'],
      success: (res) => {
        const actions = ['微信好友', '朋友圈', '相册'];
        
        if (res.tapIndex === 2) {
          // 保存图片到相册
          this.saveToAlbum();
        } else {
          wx.showToast({
            title: `已分享到${actions[res.tapIndex]}`,
            icon: 'success'
          });
        }
      }
    });
  },

  saveToAlbum() {
    wx.showLoading({
      title: '生成图片中...'
    });

    // 模拟生成图片并保存
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    }, 1500);
  }
});