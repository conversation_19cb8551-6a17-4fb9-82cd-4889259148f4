package com.heating.repository;

import com.heating.entity.attendance.TAttendanceRecord;
import org.springframework.data.jpa.repository.JpaRepository; 
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface AttendanceRecordRepository extends JpaRepository<TAttendanceRecord, Long> {
     
    
    /**
     * 查询用户当天特定类型的打卡记录
     * @param userId 用户ID
     * @param clockType 打卡类型
     * @param startTime 当天开始时间
     * @param endTime 当天结束时间
     * @return 打卡记录
     */
    Optional<TAttendanceRecord> findByUserIdAndClockTypeAndClockTimeBetween(
            Long userId, String clockType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID、年份和月份查询考勤记录
     * @param userId 用户ID
     * @param startOfMonth 月份开始时间
     * @param endOfMonth 月份结束时间
     * @param status 考勤状态（可选）
     * @return 考勤记录列表
     */
    List<TAttendanceRecord> findByUserIdAndClockTimeBetweenAndStatusOrderByClockTimeDesc(
            Long userId, LocalDateTime startOfMonth, LocalDateTime endOfMonth, String status);

    /**
     * 根据年份和月份查询所有用户的考勤记录
     * @param startOfMonth 月份开始时间
     * @param endOfMonth 月份结束时间
     * @param status 考勤状态（可选）
     * @return 考勤记录列表
     */
    List<TAttendanceRecord> findByClockTimeBetweenAndStatusOrderByClockTimeDesc(
            LocalDateTime startOfMonth, LocalDateTime endOfMonth, String status);

    /**
     * 根据用户ID、年份和月份查询考勤记录（不过滤状态）
     * @param userId 用户ID
     * @param startOfMonth 月份开始时间
     * @param endOfMonth 月份结束时间
     * @return 考勤记录列表
     */
    List<TAttendanceRecord> findByUserIdAndClockTimeBetweenOrderByClockTimeDesc(
            Long userId, LocalDateTime startOfMonth, LocalDateTime endOfMonth);

    /**
     * 根据年份和月份查询所有用户的考勤记录（不过滤状态）
     * @param startOfMonth 月份开始时间
     * @param endOfMonth 月份结束时间
     * @return 考勤记录列表
     */
    List<TAttendanceRecord> findByClockTimeBetweenOrderByClockTimeDesc(
            LocalDateTime startOfMonth, LocalDateTime endOfMonth);
} 