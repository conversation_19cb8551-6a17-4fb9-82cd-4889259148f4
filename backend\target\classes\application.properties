# Server Configuration
server.port=8889
# server.servlet.context-path=/api

# 临时禁用HTTP响应压缩，解决request:fail问题
server.compression.enabled=false
# server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
# server.compression.min-response-size=2048
# server.compression.level=6

# Database Configuration
# spring.datasource.url=**********************************************************************************
# spring.datasource.username=xeh
# spring.datasource.password=111510
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************************************************************************************
spring.datasource.username=tbkj
spring.datasource.password=tbkj2015@)!%
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


# HikariCP ?????
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.pool-name=MyHikariPool
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.keepalive-time=60000
spring.datasource.hikari.connection-test-query=SELECT 1


# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true 
spring.jpa.properties.hibernate.dialect.storage_engine=InnoDB
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.use-new-id-generator-mappings=false
## ?????????????????
#spring.jpa.properties.hibernate.physical-naming-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.hibernate.naming.physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl


# Jackson Configuration
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=Asia/Shanghai
spring.jackson.serialization.fail-on-empty-beans=false

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
device.photo.storage.path=/data/device/photos

# Custom file upload configuration
#file.uploadFolder= E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\web\uploads\
file.uploadFolder: /root/project/tbkj/web/uploads/
file.accessPath=/uploads
file.staticAccessPath=/uploads/**

# Cache Configuration
#spring.cache.type=caffeine
#spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=60s

# Device Real-time Data Configuration
device.realtime.cache.ttl=60
device.operation.log.retention-days=90

# Logging Configuration
logging.level.root=INFO
logging.level.com.heating=DEBUG
logging.level.org.springframework.security=DEBUG
logging.file.name=logs/heating-maintenance.log
logging.file.max-size=10MB
logging.file.max-history=30
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Security Configuration
jwt.secret=yourSecretKey
jwt.expiration=86400000

# WebSocket Configuration
websocket.endpoint=/ws/realtime
websocket.allowed-origins=*

# Swagger Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
spring.swagger-ui.enabled=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# Custom Business Configuration
device.alarm.temperature.min=50
device.alarm.temperature.max=80
device.alarm.pressure.min=0.2
device.alarm.pressure.max=0.8
device.alarm.flow.min=10
device.alarm.flow.max=180

# Task Scheduling Configuration
device.maintenance.check.cron=0 0 1 * * ?
device.data.cleanup.cron=0 0 2 * * ?

# API Rate Limiting
resilience4j.ratelimiter.instances.default.limitForPeriod=100
resilience4j.ratelimiter.instances.default.limitRefreshPeriod=1s
resilience4j.ratelimiter.instances.default.timeoutDuration=0

# Redis Configuration (if needed)
#spring.redis.host=localhost
#spring.redis.port=6379
#spring.redis.password=
#spring.redis.database=0

# Mail Configuration (if needed)
#spring.mail.host=smtp.example.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=your-password
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true

# Security Configuration
spring.security.user.password=tbkj20151022


wechat.appid=wx3500b716c1397a86
wechat.secret=8beed933c0b90152820ef01c0fa9c2b7

spring.profiles.active=test

# logging.level.root=DEBUG

spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# Security Configuration
security.jwt.secret=tbkj20151022tbkj20151022tbkj20151022tbkj20151022tbkj20151022tbkj20151022tbkj20151022tbkj20151022
security.jwt.expiration=86400000


# WebSocket Configuration

# Swagger Configuration

# ?????????

### 4.5 ????
alarm.api.list=http://*************:9881/api/app/alarm/record
### 4.7 ??????
alarm.api.updateStatus=http://*************:9881/api/app/alarm/updateStatus

## 8. ????? 
### 8.1 ????? 
hes.api.list=http://*************:9881/api/app/hes/list
### 8.2 ???????
hes.api.detail=http://*************:9881/api/app/hes/detail
### 8.3 ?????
hes.api.control=http://*************:9881/api/app/hes/control
### 8.4 ???????
hes.api.remote=http://*************:9881/api/app/hes/remote
### 8.5 ???????
hes.api.history=http://*************:9881/api/app/hes/history
### 8.6 ???????
hes.api.chart=http://*************:9881/api/app/hes/chart
### 8.7 ???????
hes.api.alarms.list=http://*************:9881/api/app/hes/alarms/list
### 8.8 ???????
hes.api.alarms.detail=http://*************:9881/api/app/hes/alarms/detail
### 8.9 ???????
hes.api.alarms.stats=http://*************:9881/api/app/hes/alarms/stats

## 9. ??????
### 9.1 ??????    
valve.api.list=http://*************:9881/api/app/valves/list
### 9.2 ???????????? 
valve.api.control=http://*************:9881/api/app/valves/control

## 10.????
### 10.1 ??????(???????????????????????) 
payments.api.records=http://*************:9881/api/app/payments/records
### 10.2 ?????? 
payments.api.auto.open=http://*************:9881/api/app/payments/auto-open-valve
### 10.3 ???????????????? ??????? 
payments.api.stats=http://*************:9881/api/app/payments/stats

# CORS Configuration
spring.mvc.cors.allowed-origins=*
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.allow-credentials=false
spring.mvc.cors.max-age=3600

 