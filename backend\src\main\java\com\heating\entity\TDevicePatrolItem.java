package com.heating.entity;

import lombok.Data;
import jakarta.persistence.*;

/**
 * 设备巡检项实体类
 * 对应t_device_patrol_item表
 */
@Data
@Entity
@Table(name = "t_device_patrol_item")
public class TDevicePatrolItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 设备ID
     */
    @Column(name = "device_id")
    private Long deviceId;
    
    /**
     * 巡检字典项目ID
     */
    @Column(name = "patrol_item_dict_id")
    private Long patrolItemDictId;
    
    /**
     * 巡检正常范围
     */
    @Column(name = "normal_range", length = 50)
    private String normalRange;
} 