package com.heating.controller;

import com.heating.dto.alarm.AlarmMessageResponse;
import com.heating.service.AlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 告警控制器
 * 提供告警相关的API接口
 */
@RestController
@RequestMapping("/api/alarm")
public class AlarmController {

    private static final Logger log = LoggerFactory.getLogger(AlarmController.class);

    @Autowired
    private AlarmService alarmService;

    /**
     * 获取告警消息
     * GET /api/alarm/messages
     * 
     * 返回当前系统中的告警消息列表
     * 
     * @return 告警消息列表
     */
    @GetMapping("/messages")
    public ResponseEntity<Map<String, Object>> getAlarmMessages() {
        log.info("接收到获取告警消息列表请求");
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 调用服务获取告警消息列表
            List<AlarmMessageResponse> messages = alarmService.getAlarmMessages();
            
            // 构建成功响应
            response.put("code", 200);
            response.put("message", "告警列表获取成功");
            response.put("data", messages);
            
            log.info("成功返回{}条告警消息", messages.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            // 记录错误
            log.error("获取告警消息列表失败: {}", e.getMessage(), e);
            
            // 构建错误响应
            response.put("code", 500);
            response.put("message", "获取告警消息列表失败: " + e.getMessage());
            response.put("data", List.of());
            
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 更新告警状态
     * POST /api/alarm/updateStatus
     * 
     * 根据提供的告警ID和状态码更新告警记录的状态。
     * 状态码: 1-已确认, 2-已完成, 3-已忽略
     * 
     * @param params 包含alarm_id和alarm_status的请求体
     * @return 操作结果响应
     */
    @PostMapping("/updateStatus")
    public ResponseEntity<Map<String, Object>> updateAlarmStatus(@RequestBody Map<String, Object> params) {
        Long alarmId = null;
        Integer alarmStatus = null;
        
        try {
            // 从请求体中提取参数
            if (params.containsKey("alarm_id")) {
                if (params.get("alarm_id") instanceof Number) {
                    alarmId = ((Number) params.get("alarm_id")).longValue();
                } else {
                    alarmId = Long.parseLong(params.get("alarm_id").toString());
                }
            }
            
            if (params.containsKey("alarm_status")) {
                if (params.get("alarm_status") instanceof Number) {
                    alarmStatus = ((Number) params.get("alarm_status")).intValue();
                } else {
                    alarmStatus = Integer.parseInt(params.get("alarm_status").toString());
                }
            }
            
            log.info("接收到更新告警状态请求: alarmId={}, status={}", alarmId, alarmStatus);
        } catch (Exception e) {
            log.error("解析请求参数出错: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("code", 400);
            response.put("msg", "参数解析错误: " + e.getMessage());
            response.put("data", null);
            return ResponseEntity.badRequest().body(response);
        }
        
        // 验证参数
        if (alarmId == null) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 400);
            response.put("msg", "参数错误: 缺少 alarm_id 参数");
            response.put("data", null);
            return ResponseEntity.badRequest().body(response);
        }
        
        if (alarmStatus == null) {
            Map<String, Object> response = new HashMap<>();
            response.put("code", 400);
            response.put("msg", "参数错误: 缺少 alarm_status 参数");
            response.put("data", null);
            return ResponseEntity.badRequest().body(response);
        }
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 调用服务更新告警状态
            alarmService.updateAlarmStatus(alarmId, alarmStatus);
            
            // 构建成功响应
            response.put("code", 200);
            response.put("msg", "操作成功"); // 注意：文档中使用 'msg' 而不是 'message'
            response.put("data", null);
            
            log.info("成功更新告警状态: alarmId={}", alarmId);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
             // 处理无效参数（例如，无效的状态码）
             log.error("更新告警状态失败，无效参数: {}", e.getMessage());
             response.put("code", 400);
             response.put("msg", "参数错误: " + e.getMessage());
             response.put("data", null);
             return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            // 记录其他错误
            log.error("更新告警状态失败: alarmId={}, error: {}", alarmId, e.getMessage(), e);
            
            // 构建错误响应
            response.put("code", 500);
            // 根据文档，这里使用 'msg'
            response.put("msg", "操作失败: " + e.getMessage()); 
            response.put("data", null);
            
            return ResponseEntity.ok(response); 
        }
    }
} 