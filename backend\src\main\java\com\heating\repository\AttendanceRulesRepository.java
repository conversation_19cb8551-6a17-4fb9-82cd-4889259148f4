package com.heating.repository;

import com.heating.entity.attendance.TAttendanceRules;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AttendanceRulesRepository extends JpaRepository<TAttendanceRules, Long> {
    /**
     * 查询当前有效的考勤规则
     * @return 考勤规则
     */
    @Query("SELECT r FROM TAttendanceRules r WHERE r.isActive = true ORDER BY r.id DESC")
    Optional<TAttendanceRules> findActiveRules();
} 