package com.heating.repository;

import com.heating.entity.PatrolCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 巡检项目类别数据访问接口
 */
@Repository
public interface PatrolCategoryRepository extends JpaRepository<PatrolCategory, Long> {
    
    /**
     * 根据是否启用状态查询巡检项目类别
     * @param isActive 是否启用
     * @return 巡检项目类别列表
     */
    List<PatrolCategory> findByIsActive(Boolean isActive);
    
    /**
     * 根据上级类别ID查询类别列表
     * @param parentId 上级类别ID
     * @return 巡检项目类别列表
     */
    List<PatrolCategory> findByParentId(Long parentId);
    
    /**
     * 按排序号排序查询所有类别
     * @return 巡检项目类别列表
     */
    List<PatrolCategory> findAllByOrderBySortOrderAsc();
} 