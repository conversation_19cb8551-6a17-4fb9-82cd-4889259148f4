/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 - 参考repair样式 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 30rpx 30rpx 50rpx;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

.page-header::after {
  content: '';
  position: absolute;
  top: -30%;
  left: -15%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  position: relative;
  z-index: 2;
  padding: 10rpx 0;
}
.change-year-btn{
  line-height:0
}
.change-year-btn, .refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  padding: 0 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 35rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  color: white;
  backdrop-filter: blur(15rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.change-year-btn:active, .refresh-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

.change-year-btn {
  flex: 1;
  max-width: 320rpx;
  min-width: 200rpx;
}

.refresh-btn {
  min-width: 100rpx;
  padding: 0 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 账单内容 */
.bill-content {
  padding: 0 30rpx;
  margin-top: -30rpx;
  position: relative;
  z-index: 3;
}

/* 信息卡片 - 参考repair样式 */
.info-card {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.warning-card {
  border-left: 8rpx solid #ff4d4f;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.bill-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.bill-status.status-unpaid {
  background: #fff2e8;
  color: #fa8c16;
}

.bill-status.status-partial_paid {
  background: #e6f7ff;
  color: #1890ff;
}

.bill-status.status-paid {
  background: #f6ffed;
  color: #52c41a;
}

.bill-status.status-overdue {
  background: #fff2f0;
  color: #ff4d4f;
}

.bill-status.status-no_bill {
  background: #f5f5f5;
  color: #999;
}

.warning-badge {
  background: #ff4d4f;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.record-count {
  color: #999;
  font-size: 24rpx;
}

.card-content {
  padding: 20rpx 30rpx 30rpx;
}

/* 信息行 */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-size: 28rpx;
  flex-shrink: 0;
}

.info-value {
  color: #1f1f1f;
  font-size: 28rpx;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.info-value.status-normal {
  color: #52c41a;
}

.info-value.status-stopped {
  color: #ff4d4f;
}

.info-value.warning {
  color: #ff4d4f;
  font-weight: 600;
}

/* 金额区域 */
.amount-section {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.amount-row:last-child {
  margin-bottom: 0;
}

.amount-row.main-amount .amount-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f1f1f;
}

.amount-row.main-amount .amount-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #1890ff;
}

.amount-label {
  color: #666;
  font-size: 28rpx;
}

.amount-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f1f1f;
}

.amount-value.paid {
  color: #52c41a;
}

.amount-value.remaining {
  color: #fa8c16;
  font-weight: 600;
}

/* 历史欠费样式 */
.amount-value.debt {
  color: #ff4d4f;
  font-weight: 600;
}

/* 供热费用样式 */
.amount-row:first-child .amount-label {
  color: #1890ff;
  font-weight: 500;
}

.amount-row:first-child .amount-value {
  color: #1890ff;
  font-weight: 600;
}

/* 卡片操作 */
.card-actions {
  padding: 0 30rpx 30rpx;
}

.pay-btn {
  width: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border: none;
  border-radius: 12rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 缴费记录 */
.payment-record {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-record:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f1f1f;
}

.record-method {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.record-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.record-date {
  color: #666;
  font-size: 24rpx;
}

.record-transaction {
  color: #999;
  font-size: 22rpx;
}

.record-remark {
  color: #999;
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 40rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 无账单提示样式 */
.no-bill-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.no-bill-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-bill-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.no-bill-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}
