package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.order.TWorkOrderAttachment;

import java.util.List;
import java.util.Map;

@Repository
public interface WorkOrderAttachmentRepository extends JpaRepository<TWorkOrderAttachment, String> {
    
    @Query(
        "SELECT new map(" +
            "wa.fileType as file_type, " +
            "wa.filePath as file_path" +
        ") " +
        "FROM TWorkOrderAttachment wa " +
        "WHERE wa.workOrderId = :orderId " +
        "ORDER BY wa.createdAt DESC"
    )
    List<Map<String, Object>> findByOrderId(@Param("orderId") long orderId);
} 