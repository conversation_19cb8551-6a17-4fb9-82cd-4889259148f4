package com.heating.service.impl;

import com.heating.dto.MaterialsInfoDTO;
import com.heating.entity.MaterialsInfo;
import com.heating.repository.MaterialsInfoRepository;
import com.heating.service.MaterialsInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 材料信息服务实现类
 */
@Service
public class MaterialsInfoServiceImpl implements MaterialsInfoService {

    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;

    @Override
    public List<MaterialsInfoDTO> getAllMaterials() {
        List<MaterialsInfo> materialsInfos = materialsInfoRepository.findAll();
        return materialsInfos.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     * @param materialsInfo 材料信息实体
     * @return 材料信息DTO
     */
    private MaterialsInfoDTO convertToDTO(MaterialsInfo materialsInfo) {
        MaterialsInfoDTO dto = new MaterialsInfoDTO();
        BeanUtils.copyProperties(materialsInfo, dto);
        return dto;
    }
} 