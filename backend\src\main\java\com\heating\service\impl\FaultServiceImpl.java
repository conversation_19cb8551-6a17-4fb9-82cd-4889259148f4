package com.heating.service.impl;

import com.heating.dto.fault.FaultSetStatusRequest;
import com.heating.entity.fault.TFault;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.repository.FaultAttachmentRepository;
import com.heating.repository.FaultRepository;
import com.heating.repository.OperationLogRepository;
import com.heating.repository.WorkOrderRepository;
import com.heating.repository.ManagerHeatUnitRepository;
import com.heating.entity.TManagerHeatUnit;
import com.heating.service.FaultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.heating.dto.fault.FaultReportRequest;
import com.heating.dto.fault.FaultAttachmentRequest;
import com.heating.dto.fault.FaultWeeklyCountResponse;
import com.heating.dto.fault.FaultMessageResponse;
import com.heating.dto.fault.FaultRequest;

import java.util.Optional;


import java.sql.Date;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

@Service
public class FaultServiceImpl implements FaultService {

    private static final Logger logger = LoggerFactory.getLogger(FaultServiceImpl.class);

    @Autowired
    private FaultRepository faultRepository;

    @Autowired
    private FaultAttachmentRepository faultAttachmentRepository;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private ManagerHeatUnitRepository managerHeatUnitRepository; 

    @Override
    @Transactional
    public void reportFault(FaultReportRequest request) {
        try { 
              // 创建故障对象
              TFault fault = new TFault();
              fault.setFaultNo(generateFaultNo());  
              fault.setHeatUnitId(request.getHeatUnitId());
              fault.setHouseId(request.getHouseId());
              fault.setAlarmId(request.getAlarmId());
              fault.setFaultType(request.getFaultType());
              fault.setFaultSource(request.getFaultSource());
              fault.setFaultLevel(request.getFaultLevel() != null ? request.getFaultLevel() : "1");
              fault.setFaultDesc(request.getFaultDesc());
              fault.setOccurTime(request.getOccurTime());
              fault.setReportUserId(request.getReportUserId());
              fault.setAddress(request.getAddress());
              fault.setReportTime(LocalDateTime.now());
              
              // 判断故障状态，如果没有设置或为空，则设置为"待确认"
              String faultStatus = request.getFaultStatus();
              if (faultStatus == null || faultStatus.trim().isEmpty()) {
                  faultStatus = "待确认";
              }
              fault.setFaultStatus(faultStatus);
              
              fault.setManagerId(request.getManagerId());
              fault.setCreatedAt(LocalDateTime.now());  
              fault.setUpdatedAt(LocalDateTime.now());  
              faultRepository.save(fault);

              // 保存故障附件
              for (FaultAttachmentRequest attachment : request.getAttachment()) {
                  TFaultAttachment faultAttachment = new TFaultAttachment();
                faultAttachment.setFaultId(fault.getId());
                faultAttachment.setFileType(attachment.getFileType());
                faultAttachment.setFilePath(attachment.getFilePath());
                faultAttachment.setCreatedAt(LocalDateTime.now());
                faultAttachmentRepository.save(faultAttachment);
              }
              
              // 如果故障状态为"已确认"，自动创建工单
              if ("已确认".equals(faultStatus)) {
                  logger.info("故障状态为已确认，自动创建工单");
                  
                  // 创建工单
                  TWorkOrder workOrder = new TWorkOrder();
                  workOrder.setOrderNo(generateOrderNo());
                  workOrder.setFaultId(fault.getId());
                  workOrder.setRepairUserId(request.getRepairtUserId());
                  workOrder.setRepairContent(fault.getFaultDesc()); 
                  workOrder.setOrderStatus("待接单");
                  workOrder.setCreatedAt(LocalDateTime.now());
                  workOrder.setUpdatedAt(LocalDateTime.now());
                  workOrderRepository.save(workOrder);
                  
                  // 创建操作日志
                  TOperationLog log = new TOperationLog();
                  log.setWorkOrderId(workOrder.getId());
                  log.setOperationType("故障确认");
                  log.setOperationDesc("系统自动确认故障并生成工单");
                  log.setOperatorId(request.getReportUserId()); // 使用上报人作为操作人
                  log.setCreatedAt(LocalDateTime.now());
                  operationLogRepository.save(log);
                  
                  logger.info("已为故障 {} 创建工单 {}", fault.getId(), workOrder.getId());
              }
                 
        } catch (Exception e) {
            logger.error("Error reporting fault: {}", e.getMessage(), e);
            throw new RuntimeException("故障上报失败: " + e.getMessage());
        }
    }
 

    @Override
    public Map<String, Object> getFaultList(String status, Date date, Long reportUserId, String role, Integer page, Integer pageSize) {
        try {
            // 默认值处理
            page = (page == null || page < 1) ? 1 : page;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
            
            // 计算偏移量
            int offset = (page - 1) * pageSize;

            // 声明变量
            List<Map<String, Object>> faults;
            long total;
            
            // 处理多角色情况（角色可能是逗号分隔的字符串）
            boolean hasAdminRole = false;
            if (role != null && !role.isEmpty()) {
                String[] roles = role.split(",");
                for (String r : roles) {
                    String roleTrim = r.trim();
                    if ("admin".equals(roleTrim) || "manage".equals(roleTrim)) {
                        hasAdminRole = true;
                        break;
                    }
                }
            }
            
            // 根据角色和用户ID进行不同的查询
            if (hasAdminRole) {
                // 管理员角色，获取所有记录
                faults = faultRepository.findFaultListWithPaging(status, date, offset, pageSize);
                total = faultRepository.countFaultList(status, date);
            } else {
                // 普通用户，只获取用户自己上报的故障
                if (reportUserId == null) {
                    throw new IllegalArgumentException("非管理员用户必须提供用户ID");
                }
                faults = faultRepository.findFaultListByUserWithPaging(status, date, reportUserId, offset, pageSize);
                total = faultRepository.countFaultListByUser(status, date, reportUserId);
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", faults);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", (total + pageSize - 1) / pageSize);
            
            return result;
        } catch (Exception e) {
            logger.error("获取故障列表失败", e);
            throw new RuntimeException("获取故障列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFaultDetail(long faultId) {
        try {
            logger.info("=== Fault Detail Debug ===");
            logger.info("Requested fault_id: {}", faultId);
            
            // 获取故障基本信息
            Map<String, Object> faultDetail = faultRepository.findFaultDetail(faultId);
            if (faultDetail == null) {
                throw new RuntimeException("故障信息不存在");
            }
            
            // 获取附件信息
            List<TFaultAttachment> attachments = faultAttachmentRepository.findByFaultId(faultId);
            logger.info("Found {} attachments for fault ID: {}", attachments.size(), faultId);
            
            // 分离图片和视频附件
            List<String> imageUrls = new ArrayList<>();
            String videoUrl = null;
            
            for (TFaultAttachment attachment : attachments) {
                logger.info("Processing attachment: type={}, path={}", attachment.getFileType(), attachment.getFilePath());
                
                if ("image".equals(attachment.getFileType())) {
                    String imagePath = attachment.getFilePath();
                    // 确保路径格式正确
                    if (imagePath != null && !imagePath.isEmpty()) {
                        imageUrls.add(imagePath);
                        logger.info("Added image URL: {}", imagePath);
                    }
                } else if ("video".equals(attachment.getFileType())) {
                    videoUrl = attachment.getFilePath();
                    logger.info("Found video URL: {}", videoUrl);
                }
            }
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("fault_info", faultDetail);
            data.put("images", imageUrls);
            if (videoUrl != null) {
                data.put("video", videoUrl);
            }
            
            logger.info("Returning data: images={}, video={}", imageUrls.size(), videoUrl != null);
            return data;
        } catch (Exception e) {
            logger.error("Error in get_fault_detail: {}", e.getMessage(), e);
            logger.error("Error type: {}", e.getClass().getName());
            logger.error("Stacktrace:", e);
            throw new RuntimeException("获取故障详情失败: " + e.getMessage());
        }
    }

    private String generateFaultNo() {
        return "FAULT-"+LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    private String generateOrderNo() {
        return "ORDER-"+LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    } 

    @Override
    public Map<String, Object> getFaultStatistics() {
        Map<String, Object> result = new HashMap<>();
        result.put("total", faultRepository.getTotalCount());
        result.put("pending", faultRepository.countByStatus("待确认"));
        result.put("processing", faultRepository.countByStatus("已确认"));  
        result.put("returned", faultRepository.countByStatus("已退回"));
        return result;
    }

    @Override
    public List<Map<String, Object>> getRecentFaults() {
        return faultRepository.findRecentFaults();
    }   

    @Override
    public void setFaultStatus(FaultSetStatusRequest request) {
        logger.info("Confirming fault with ID: {}", request.getFaultId());
        TFault fault = faultRepository.findById(String.valueOf(request.getFaultId())).orElseThrow(() -> new RuntimeException("Fault not found"));
        if (fault == null) {
            throw new RuntimeException("故障信息不存在");
        }

        TOperationLog log = new TOperationLog(); 
        if ("已确认".equals(request.getFaultStatus())) {

            // 更新故障状态
            fault.setFaultStatus("已确认");
            if (request.getManagerId() != null) {
                fault.setManagerId(request.getManagerId());
            }
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);

            // 生成工单
            TWorkOrder workOrder = new TWorkOrder();
            workOrder.setOrderNo(generateOrderNo());
            workOrder.setFaultId(request.getFaultId());
            // 使用请求中的维修人员ID，如果没有则使用操作员ID作为备选
            Long repairUserId = request.getRepairUserId() != null ? request.getRepairUserId() : request.getOperatorId();
            workOrder.setRepairUserId(repairUserId);
            workOrder.setRepairContent(fault.getFaultDesc()); 
            workOrder.setOrderStatus("待接单");
            workOrder.setCreatedAt(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder); 

            // 生成日志 
            log.setWorkOrderId(workOrder != null ? workOrder.getId() : null); 
            log.setOperationType("故障确认");
            log.setOperationDesc("管理员确认故障并生成工单");
            log.setOperatorId(request.getOperatorId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);  // 保存日志

        } else if ("已退回".equals(request.getFaultStatus())) {
            fault.setFaultStatus("已退回");
            if (request.getManagerId() != null) {
                fault.setManagerId(request.getManagerId());
            }
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);   
            
//            // 撤销工单
//            Optional<TWorkOrder> workOrder = workOrderRepository.findByFaultId(request.getFaultId());
//            if (workOrder.isPresent()) {
//                workOrder.get().setOrderStatus("已撤销");
//                workOrder.get().setUpdatedAt(LocalDateTime.now());
//                workOrderRepository.save(workOrder.get());
//            }
//
//            // 生成日志
//            log.setWorkOrderId(workOrder.get().getId());
//            log.setOperationType("故障退回");
//            log.setOperationDesc("管理员退回故障");
//            log.setOperatorId(request.getOperatorId());
//            operationLogRepository.save(log);  // 保存日志

        } else if ("已完成".equals(request.getFaultStatus())){
            fault.setFaultStatus("已完成");
            fault.setUpdatedAt(LocalDateTime.now());
            faultRepository.save(fault);

            // 改变工单状态
            Optional<TWorkOrder> workOrder = workOrderRepository.findByFaultId(request.getFaultId());
            if (workOrder.isPresent()) {
                workOrder.get().setOrderStatus("已完成");
                workOrder.get().setUpdatedAt(LocalDateTime.now());
                workOrderRepository.save(workOrder.get());
            }   

            // 生成日志 
            log.setWorkOrderId(workOrder.get().getId());
            log.setOperationType("故障完成");
            log.setOperationDesc("管理员确认故障已完成");
            log.setOperatorId(request.getOperatorId()); 
            operationLogRepository.save(log);  // 保存日志  
            
        } else {
            throw new RuntimeException("无效的故障状态");
        }  
    }

    /**
     * 获取本周故障告警数量
     * @return 本周故障告警数量
     */
    @Override
    public FaultWeeklyCountResponse getWeeklyFaultCount() {
        Long count = faultRepository.countCurrentWeekFaults();
        return new FaultWeeklyCountResponse(count.intValue());
    }

    @Override
    public List<Map<String, Object>> getFaultsByManagerId(Long managerId) {
        if (managerId == null) {
            throw new IllegalArgumentException("管理员ID不能为空");
        }
        return faultRepository.findByManagerId(managerId);
    }

 
    /**
     * 获取管理员ID列表
     * 根据热用户ID从管理人员与热用户关联表中获取管理员ID列表
     * 
     * @param heatUnitId 热用户ID
     * @return 管理员ID列表
     */
    private List<Long> getManagerIds(Object heatUnitId) { 
        if (heatUnitId == null) {
            logger.warn("热用户ID为空，无法获取管理员列表");
            return new ArrayList<>();
        }
        
        Long unitId;
        try {
            unitId = Long.parseLong(heatUnitId.toString());
        } catch (NumberFormatException e) {
            logger.error("热用户ID格式错误: {}", heatUnitId);
            return new ArrayList<>();
        }
        
        // 从管理人员与热用户关联表中获取管理员ID列表  
        List<TManagerHeatUnit> managerHeatUnit = managerHeatUnitRepository.findByHeatUnitId(unitId);
        
        List<Long> managerIds = new ArrayList<>();
        for (TManagerHeatUnit item : managerHeatUnit) {
            managerIds.add(item.getManagerId());
        } 
        
        // 返回管理员ID列表
        return managerIds;
    }

    /**
     * 获取故障消息
     * 从第三方接口获取当前所有需要处理的故障消息列表
     * 
     * @return 故障消息列表
     */
    @Override
    public List<FaultMessageResponse> getFaultMessages() {
        logger.info("开始获取故障消息列表"); 
        List<FaultMessageResponse> faultMessages = new ArrayList<>();  
        try {  
            // 获取fault_status= '待确认' 的故障数据列表
            List<Map<String, Object>> apiResponseList =faultRepository.findFaultList("待确认",null);

            // 遍历apiResponseList，将每个故障数据转换为FaultMessageResponse对象
            for (Map<String, Object> apiResponse : apiResponseList) {
                FaultMessageResponse faultMessage = new FaultMessageResponse();
                faultMessage.setId(Long.parseLong(apiResponse.get("fault_id").toString()));
                faultMessage.setHeatUnitId(Long.parseLong(apiResponse.get("heat_unit_id").toString()));     
                faultMessage.setHeatUnitName(apiResponse.get("heat_unit_name").toString());
                faultMessage.setManagerIds(getManagerIds(apiResponse.get("heat_unit_id")));
                faultMessage.setFaultDesc(apiResponse.get("fault_desc").toString());
                faultMessage.setOccurTime(apiResponse.get("occur_time").toString());
                faultMessages.add(faultMessage);
            } 
            return faultMessages;
        } catch (Exception e) {
            logger.error("获取故障消息列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取故障消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据房屋ID获取故障历史记录
     */
    @Override
    public Map<String, Object> getFaultHistoryByHouseId(Long houseId, int page, int size, String status) {
        logger.info("获取房屋故障历史记录，房屋ID: {}, 页码: {}, 每页大小: {}, 状态: {}", houseId, page, size, status);

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询故障记录
            List<Map<String, Object>> faultList = faultRepository.findFaultHistoryByHouseId(houseId, status, offset, size);

            // 查询总数
            Long totalCount = faultRepository.countFaultsByHouseId(houseId, status);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / size);

            // 组装返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("list", faultList);
            result.put("total", totalCount);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", totalPages);

            logger.info("获取故障历史记录成功，共{}条记录", totalCount);
            return result;

        } catch (Exception e) {
            logger.error("获取故障历史记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取故障历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据故障ID获取故障详情
     */
    @Override
    public Map<String, Object> getFaultDetailById(Long faultId) {
        logger.info("获取故障详情，故障ID: {}", faultId);

        try {
            Map<String, Object> raw = faultRepository.findFaultDetailById(faultId);

            if (raw != null) {
                // Hibernate 的 TupleBackedMap 是不可变的，这里拷贝为可变 Map 再追加字段
                Map<String, Object> faultDetail = new HashMap<>(raw);
                // 获取故障附件
                List<Map<String, Object>> attachments = faultAttachmentRepository.findAttachmentsByFaultId(faultId);
                faultDetail.put("attachments", attachments);

                logger.info("获取故障详情成功，故障ID: {}", faultId);
                return faultDetail;
            } else {
                logger.warn("故障记录不存在，故障ID: {}", faultId);
                return null;
            }

        } catch (Exception e) {
            logger.error("获取故障详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取故障详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据房屋ID获取故障统计信息
     */
    @Override
    public Map<String, Object> getFaultStatisticsByHouseId(Long houseId) {
        logger.info("获取房屋故障统计信息，房屋ID: {}", houseId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总故障数
            Long totalCount = faultRepository.countFaultsByHouseId(houseId, null);
            statistics.put("totalCount", totalCount);

            // 待确认故障数
            Long pendingCount = faultRepository.countFaultsByHouseId(houseId, "待确认");
            statistics.put("pendingCount", pendingCount);

            // 已确认故障数
            Long confirmedCount = faultRepository.countFaultsByHouseId(houseId, "已确认");
            statistics.put("confirmedCount", confirmedCount);

            // 已退回故障数
            Long returnedCount = faultRepository.countFaultsByHouseId(houseId, "已退回");
            statistics.put("returnedCount", returnedCount);

            // 本月故障数
            Long thisMonthCount = faultRepository.countFaultsByHouseIdAndMonth(houseId);
            statistics.put("thisMonthCount", thisMonthCount);

            // 最近一次故障时间
            String lastFaultTime = faultRepository.findLastFaultTimeByHouseId(houseId);
            statistics.put("lastFaultTime", lastFaultTime);

            logger.info("获取故障统计信息成功，房屋ID: {}, 总数: {}", houseId, totalCount);
            return statistics;

        } catch (Exception e) {
            logger.error("获取故障统计信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取故障统计信息失败: " + e.getMessage());
        }
    }

}