# 年度选择和基本信息显示问题修复

## 修复的问题

根据您提供的界面截图和错误信息，发现并修复了以下问题：

### 1. 年度选择显示问题
**问题**：显示"undefined-2025供暖年"
**原因**：WXML中的年度显示逻辑有误
**修复**：

#### 修复前
```xml
{{basicInfo.heatingYear + '-' + (heatingYear + 1) + '供暖年度'  || (heatingYear ? heatingYear + '-' + (heatingYear + 1) + '供暖年度' : '选择年度')}}
```

#### 修复后
```xml
{{(basicInfo.heatingYear ? basicInfo.heatingYear + '-' + (basicInfo.heatingYear + 1) + '供暖年度' : (heatingYear ? heatingYear + '-' + (heatingYear + 1) + '供暖年度' : '选择年度'))}}
```

**说明**：修复了逻辑运算符优先级问题，确保正确显示年度信息。

### 2. 基本信息显示问题
**问题**：即使没有账单信息，基本信息也应该正常显示
**原因**：后端在没有账单时直接返回错误，前端没有正确处理这种情况

#### 后端修复

##### 修改getSimpleBillInfo方法逻辑
```java
// 修复前：没有账单时直接返回错误
if (!billOpt.isPresent()) {
    return buildErrorResponse("未找到" + heatingYear + "年度的账单信息");
}

// 修复后：没有账单时返回基本信息和空账单状态
if (!billOpt.isPresent()) {
    log.info("未找到{}年度的账单信息，返回基本房屋信息", heatingYear);
    
    response.setCode(200);
    response.setMessage("未找到" + heatingYear + "年度的账单信息");
    
    // 设置空的账单信息
    billData.setBillId(null);
    billData.setBillFeeInfo(buildEmptyBillFeeInfo());
    billData.setPaymentStatusInfo(buildEmptyPaymentStatusInfo());
    billData.setPaymentRecords(new ArrayList<>());
    
    response.setData(billData);
    return response;
}
```

##### 添加空数据构建方法
```java
/**
 * 构建空的账单费用信息
 */
private SimpleBillInfoResponse.BillFeeInfo buildEmptyBillFeeInfo() {
    SimpleBillInfoResponse.BillFeeInfo billFeeInfo = new SimpleBillInfoResponse.BillFeeInfo();
    billFeeInfo.setHeatingFee(BigDecimal.ZERO);
    billFeeInfo.setFeeTypeName("用热费");
    billFeeInfo.setUnitPrice(BigDecimal.ZERO);
    billFeeInfo.setOverdueAmount(BigDecimal.ZERO);
    billFeeInfo.setTotalPayableAmount(BigDecimal.ZERO);
    billFeeInfo.setActualPaidAmount(BigDecimal.ZERO);
    return billFeeInfo;
}

/**
 * 构建空的缴费状态信息
 */
private SimpleBillInfoResponse.PaymentStatusInfo buildEmptyPaymentStatusInfo() {
    SimpleBillInfoResponse.PaymentStatusInfo paymentStatusInfo = new SimpleBillInfoResponse.PaymentStatusInfo();
    paymentStatusInfo.setPaymentStatus("no_bill");
    paymentStatusInfo.setPaymentStatusText("暂无账单");
    paymentStatusInfo.setShowActualPaidAmount(false);
    paymentStatusInfo.setRemainingAmount(BigDecimal.ZERO);
    paymentStatusInfo.setDueDate(null);
    paymentStatusInfo.setLastPaidDate(null);
    return paymentStatusInfo;
}
```

#### 前端修复

##### 修改响应处理逻辑
```javascript
// 修复前：只处理有数据的情况
if (response && response.code === 200 && response.data) {
    // 处理数据...
} else {
    // 显示错误状态
}

// 修复后：区分有账单和无账单的情况
if (response && response.code === 200) {
    const data = response.data;

    // 构建基本信息（无论是否有账单都要显示）
    const basicInfo = data && data.houseInfo ? {
        houseNumber: data.houseInfo.houseNumber,
        address: data.houseInfo.address,
        area: data.houseInfo.area,
        heatingYear: data.houseInfo.heatingYear,
        heatingStatusText: data.houseInfo.heatingStatusText
    } : {};

    // 检查是否有账单数据
    if (data && data.billId && data.paymentStatusInfo && data.paymentStatusInfo.paymentStatus !== 'no_bill') {
        // 有账单数据的处理...
    } else {
        // 没有账单数据时，显示基本信息和空账单状态
        this.setData({
            billDetail: data,
            basicInfo: basicInfo,
            billInfo: { 
                status: 'no_bill', 
                statusText: response.message || '暂无账单',
                unitPrice: '0.00'
            },
            paymentRecords: [],
            overdueInfo: {}
        });
    }
}
```

## 修复后的响应结构

### 有账单数据时
```json
{
  "code": 200,
  "message": "获取账单信息成功",
  "data": {
    "billId": 123,
    "houseInfo": {
      "houseNumber": "HT2024000123",
      "address": "1-1-0101",
      "area": 115.7,
      "heatingYear": 2025,
      "heatingStatusText": "正常供暖"
    },
    "billFeeInfo": { /* 完整的费用信息 */ },
    "paymentStatusInfo": { /* 完整的缴费状态 */ },
    "paymentRecords": [ /* 缴费记录列表 */ ]
  }
}
```

### 无账单数据时
```json
{
  "code": 200,
  "message": "未找到2024年度的账单信息",
  "data": {
    "billId": null,
    "houseInfo": {
      "houseNumber": "HT2024000123",
      "address": "1-1-0101", 
      "area": 115.7,
      "heatingYear": 2024,
      "heatingStatusText": "正常供暖"
    },
    "billFeeInfo": {
      "heatingFee": 0,
      "feeTypeName": "用热费",
      "unitPrice": 0,
      "overdueAmount": 0,
      "totalPayableAmount": 0,
      "actualPaidAmount": 0
    },
    "paymentStatusInfo": {
      "paymentStatus": "no_bill",
      "paymentStatusText": "暂无账单",
      "showActualPaidAmount": false,
      "remainingAmount": 0,
      "dueDate": null,
      "lastPaidDate": null
    },
    "paymentRecords": []
  }
}
```

## 修复后的显示效果

### 选择2024-2025年度时

#### 年度选择按钮
- **修复前**：显示"undefined-2025供暖年"
- **修复后**：显示"2024-2025供暖年度"

#### 基本信息
- **修复前**：全部为空
- **修复后**：正常显示房屋信息
  - 户号：HT2024000123
  - 地址：1-1-0101
  - 面积：115.7㎡
  - 供暖年度：2024
  - 供暖状态：正常供暖

#### 账单信息
- **修复前**：显示错误信息
- **修复后**：显示"暂无账单"状态，不再报错

## 测试验证

1. **年度选择显示**：验证各个年度选择后按钮显示正确
2. **基本信息显示**：验证无账单时基本信息正常显示
3. **错误处理**：验证不再出现JavaScript错误
4. **用户体验**：验证界面友好，不会因为没有账单而显示错误

所有问题现在都已修复，用户可以正常查看不同年度的信息，即使某个年度没有账单数据。
