Page({
  data: {
    amount: '0.00',
    orderNo: '',
    payTime: '',
    paymentId: ''
  },

  onLoad(options) {
    console.log('支付成功页面接收到的参数:', options);

    const amount = options.amount || '0.00';
    const paymentId = options.paymentId || '';
    const billId = options.billId || '';
    const orderNo = paymentId ? `PAY${paymentId}` : 'HT' + Date.now();
    const payTime = this.formatTime(new Date());

    this.setData({
      amount: amount,
      orderNo: orderNo,
      payTime: payTime,
      paymentId: paymentId,
      billId: billId
    });

    console.log('支付成功页面数据设置完成:', this.data);
  },

  formatTime(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  viewInvoice() {
    if (this.data.paymentId) {
      wx.navigateTo({
        url: `/pages/invoice/detail?paymentId=${this.data.paymentId}`
      });
    } else {
      wx.navigateTo({
        url: '/pages/invoice/index'
      });
    }
  },

  // 查看账单详情
  viewBillDetail() {
    if (this.data.billId) {
      wx.navigateTo({
        url: `/pages/bill/bill-view?billId=${this.data.billId}`
      });
    } else {
      wx.navigateTo({
        url: '/pages/bill/bill-view'
      });
    }
  },

  // 查看缴费记录
  viewPaymentRecords() {
    wx.navigateTo({
      url: '/pages/payment/records'
    });
  }
});