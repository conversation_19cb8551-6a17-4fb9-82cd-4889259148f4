package com.heating.controller;

import com.heating.dto.system.AppSystemParamResponse;
import com.heating.service.AppSystemParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 系统参数Controller
 */
@RestController
@RequestMapping("/api/system")
public class AppSystemParamController {
    
    private static final Logger log = LoggerFactory.getLogger(AppSystemParamController.class);
    
    @Autowired
    private AppSystemParamService appSystemParamService;
    
    /**
     * 获取系统参数
     * @param id 系统参数ID，默认为1
     * @return 系统参数
     */
    @GetMapping("/params")
    public ResponseEntity<Map<String, Object>> getSystemParams(@RequestParam(required = false) Integer id) {
        try {
            AppSystemParamResponse systemParam = appSystemParamService.getSystemParam(id);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取系统参数成功",
                    "data", systemParam
            ));
        } catch (Exception e) {
            log.error("Failed to get system parameters", e);
            return ResponseEntity.ok(Map.of(
                    "code", 500,
                    "message", "获取系统参数失败"
            ));
        }
    }
} 