const { faultApi, dictApi } = require('../../../api/index.js');

Page({
  data: {
    // 故障类型选项（从后端动态获取）
    faultTypes: [],
    faultTypeIndex: -1,
    faultTypesLoading: true,
    
    // 故障等级选项
    faultLevels: [
      { name: '一般', value: '一般' },
      { name: '重要', value: '重要' },
      { name: '严重', value: '严重' },
      { name: '紧急', value: '紧急' }
    ],
    faultLevel: '',

    // 表单数据
    faultDesc: '',
    imageList: [],
    
    // 时间选择器
    dateTime: [0, 0, 0, 0, 0],
    dateTimeArray: [[], [], [], [], []],
    occurTime: '',
    
    // 提交状态
    isSubmitting: false
  },

  onLoad() {
    this.initDateTime();
    this.loadUserInfo();
    this.loadFaultTypes();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    console.log('用户信息:', userInfo);
  },

  // 加载故障类型
  async loadFaultTypes() {
    try {
      this.setData({
        faultTypesLoading: true
      });

      console.log('开始调用微信专用字典接口...');

      // 调用微信专用字典API获取故障类型
      // 字典ID为7，对应故障类型字典
      const result = await dictApi.getDictDataByDictId(5);

      console.log('微信字典API响应:', result);

      if (result.code === 200 && result.data && Array.isArray(result.data)) {
        const faultTypes = result.data.map(item => ({
          name: item.name || item.label,
          value: item.value || item.code || item.name || item.label
        }));

        this.setData({
          faultTypes: faultTypes,
          faultTypesLoading: false
        });

        console.log('故障类型加载成功:', faultTypes);

        wx.showToast({
          title: '故障类型加载成功',
          icon: 'success',
          duration: 1000
        });
      } else {
        throw new Error(result.message || '获取故障类型失败');
      }
    } catch (error) {
      console.error('获取故障类型失败:', error);
      this.setData({
        faultTypesLoading: false
      });

      // 使用默认的故障类型作为备选
      const defaultFaultTypes = [
        { name: '供暖不热', value: '供暖不热' },
        { name: '暖气漏水', value: '暖气漏水' },
        { name: '暖气片不热', value: '暖气片不热' },
        { name: '管道堵塞', value: '管道堵塞' },
        { name: '其他故障', value: '其他故障' }
      ];

      this.setData({
        faultTypes: defaultFaultTypes
      });

      console.log('使用默认故障类型:', defaultFaultTypes);

      wx.showToast({
        title: '已加载默认故障类型',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 初始化时间选择器
  initDateTime() {
    const now = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 年份（当前年份）
    years.push(now.getFullYear());

    // 月份（1-12）
    for (let i = 1; i <= 12; i++) {
      months.push(i < 10 ? '0' + i : '' + i);
    }

    // 日期（1-31）
    for (let i = 1; i <= 31; i++) {
      days.push(i < 10 ? '0' + i : '' + i);
    }

    // 小时（0-23）
    for (let i = 0; i < 24; i++) {
      hours.push(i < 10 ? '0' + i : '' + i);
    }

    // 分钟（0-59）
    for (let i = 0; i < 60; i++) {
      minutes.push(i < 10 ? '0' + i : '' + i);
    }

    // 设置默认时间为当前时间
    const currentMonth = now.getMonth() + 1;
    const currentDate = now.getDate();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    const defaultOccurTime = `${now.getFullYear()}-${currentMonth < 10 ? '0' + currentMonth : currentMonth}-${currentDate < 10 ? '0' + currentDate : currentDate} ${currentHour < 10 ? '0' + currentHour : currentHour}:${currentMinute < 10 ? '0' + currentMinute : currentMinute}`;

    this.setData({
      dateTimeArray: [years, months, days, hours, minutes],
      dateTime: [0, now.getMonth(), now.getDate() - 1, now.getHours(), now.getMinutes()],
      occurTime: defaultOccurTime
    });
  },

  // 故障类型选择
  onFaultTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      faultTypeIndex: index
    });
  },

  // 故障等级选择
  selectFaultLevel(e) {
    const level = e.currentTarget.dataset.level;
    this.setData({
      faultLevel: level
    });
  },

  // 时间选择
  onDateTimeChange(e) {
    const values = e.detail.value;
    const dateTimeArray = this.data.dateTimeArray;
    
    const year = dateTimeArray[0][values[0]];
    const month = dateTimeArray[1][values[1]];
    const day = dateTimeArray[2][values[2]];
    const hour = dateTimeArray[3][values[3]];
    const minute = dateTimeArray[4][values[4]];
    
    const occurTime = `${year}-${month}-${day} ${hour}:${minute}`;
    
    this.setData({
      dateTime: values,
      occurTime: occurTime
    });
  },

  // 故障描述输入
  onDescInput(e) {
    this.setData({
      faultDesc: e.detail.value
    });
  },

  // 从相册选择图片
  chooseFromAlbum() {
    this.chooseImage('album');
  },

  // 拍照
  takePhoto() {
    this.chooseImage('camera');
  },

  // 选择图片
  chooseImage(sourceType) {
    const maxCount = 6 - this.data.imageList.length;
    if (maxCount <= 0) {
      wx.showToast({
        title: '最多只能上传6张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: maxCount,
      mediaType: ['image'],
      sourceType: [sourceType],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles;
        const newImages = tempFiles.map(file => file.tempFilePath);
        
        this.setData({
          imageList: [...this.data.imageList, ...newImages]
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.imageList
    });
  },

  // 删除图片
  deleteImage(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const imageList = [...this.data.imageList];
    imageList.splice(index, 1);
    
    this.setData({
      imageList: imageList
    });
  },

  // 表单验证
  validateForm() {
    const { faultTypeIndex, faultLevel, occurTime, faultDesc } = this.data;

    if (faultTypeIndex === -1) {
      wx.showToast({
        title: '请选择故障类型',
        icon: 'none'
      });
      return false;
    }

    if (!faultLevel) {
      wx.showToast({
        title: '请选择故障等级',
        icon: 'none'
      });
      return false;
    }

    if (!occurTime) {
      wx.showToast({
        title: '请选择发生时间',
        icon: 'none'
      });
      return false;
    }

    if (!faultDesc.trim()) {
      wx.showToast({
        title: '请输入故障描述',
        icon: 'none'
      });
      return false;
    }

    if (faultDesc.trim().length < 10) {
      wx.showToast({
        title: '故障描述至少需要10个字符',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交报修
  submitRepair() {
    if (!this.validateForm()) {
      return;
    }
    
    if (this.data.isSubmitting) {
      return;
    }
    
    this.setData({
      isSubmitting: true
    });
    
    wx.showLoading({
      title: '提交中...'
    });
    
    // 准备提交数据
    this.prepareSubmitData();
  },

  // 准备提交数据
  async prepareSubmitData() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        throw new Error('用户信息不完整，请重新登录');
      }

      // 上传图片
      const attachments = await this.uploadImages();
      
      // 构建提交数据
      const submitData = {
        heat_unit_id: userInfo.heatUnitId || 1, // 热用户ID，从用户信息获取
        alarm_id: null, // 用户报修时通常没有关联告警
        fault_source: '用户投诉', // 故障来源
        fault_type: this.data.faultTypes[this.data.faultTypeIndex].name,
        fault_level: this.data.faultLevel,
        fault_desc: this.data.faultDesc.trim(),
        address: userInfo.address || '', // 使用用户绑定的地址信息
        occur_time: this.data.occurTime + ':00', // 添加秒数
        report_user_id: userInfo.id,
        house_id: userInfo.houseId || 1, // 房屋ID，从用户信息获取
        attachment: attachments
      };
      
      console.log('提交数据:', submitData);
      
      // 调用API提交
      await this.callSubmitAPI(submitData);
      
    } catch (error) {
      console.error('准备提交数据失败:', error);
      wx.hideLoading();
      this.setData({
        isSubmitting: false
      });
      
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  // 上传图片
  async uploadImages() {
    if (this.data.imageList.length === 0) {
      return [];
    }

    const attachments = [];

    for (let i = 0; i < this.data.imageList.length; i++) {
      try {
        const filePath = this.data.imageList[i];

        // 这里应该调用实际的文件上传接口
        // 暂时模拟上传成功，返回文件路径
        const uploadResult = await this.uploadSingleImage(filePath);

        attachments.push({
          file_type: '图片',
          file_path: uploadResult.url || filePath
        });
      } catch (error) {
        console.error(`上传第${i + 1}张图片失败:`, error);
        // 继续上传其他图片，不中断流程
      }
    }

    return attachments;
  },

  // 上传单张图片
  uploadSingleImage(filePath) {
    return new Promise((resolve, reject) => {
      // 这里应该实现实际的图片上传逻辑
      // 暂时模拟上传成功
      setTimeout(() => {
        resolve({
          url: filePath // 实际应该返回服务器上的文件URL
        });
      }, 500);

      // 实际的上传代码示例：
      /*
      wx.uploadFile({
        url: 'https://your-server.com/api/upload',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code === 200) {
            resolve(data.data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
      */
    });
  },

  // 调用提交API
  async callSubmitAPI(data) {
    try {
      const result = await faultApi.reportFault(data);

      wx.hideLoading();
      this.setData({
        isSubmitting: false
      });

      if (result.code === 200) {
        wx.showToast({
          title: '报修提交成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.message || '提交失败');
      }

    } catch (error) {
      wx.hideLoading();
      this.setData({
        isSubmitting: false
      });

      console.error('API调用失败:', error);
      wx.showToast({
        title: error.message || '网络异常，请稍后重试',
        icon: 'none'
      });
    }
  }
});
