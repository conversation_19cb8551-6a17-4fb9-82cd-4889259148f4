package com.heating.dto.payment;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 缴费提交响应DTO
 */
@Data
public class PaymentSubmitResponse {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 缴费数据
     */
    private PaymentData data;
    
    /**
     * 缴费数据内部类
     */
    @Data
    public static class PaymentData {
        
        /**
         * 缴费记录ID
         */
        private Long paymentId;
        
        /**
         * 账单ID
         */
        private Long billId;
        
        /**
         * 缴费金额
         */
        private BigDecimal amount;
        
        /**
         * 缴费类型
         */
        private String feeType;
        
        /**
         * 缴费类型名称
         */
        private String feeTypeName;
        
        /**
         * 支付方式
         */
        private String paymentMethod;
        
        /**
         * 支付方式名称
         */
        private String paymentMethodText;
        
        /**
         * 交易流水号
         */
        private String transactionNo;
        
        /**
         * 缴费日期
         */
        private String paymentDate;
        
        /**
         * 用热状态更新结果
         */
        private Boolean heatingStatusUpdated;
        
        /**
         * 欠费记录清除结果
         */
        private Boolean overdueRecordsCleared;
        
        /**
         * 账单状态更新结果
         */
        private Boolean billStatusUpdated;
    }
}
