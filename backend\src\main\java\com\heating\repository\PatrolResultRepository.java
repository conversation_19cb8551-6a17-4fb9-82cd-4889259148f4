package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.patrol.TPatrolResult;
import java.util.List;

@Repository
public interface PatrolResultRepository extends JpaRepository<TPatrolResult, Long> {
    /**
     * 根据巡检记录ID删除所有结果
     * @param patrolRecordId 巡检记录ID
     */
    void deleteByPatrolRecordId(Long patrolRecordId);
    
    /**
     * 根据巡检记录ID查询巡检结果
     * @param patrolRecordId 巡检记录ID
     * @return 巡检结果列表
     */
    List<TPatrolResult> findByPatrolRecordId(Long patrolRecordId);
    
    /**
     * 根据巡检记录ID联合查询巡检结果和项目信息
     * @param patrolRecordId 巡检记录ID
     * @return 巡检结果和项目信息联合查询结果
     */
    @Query("SELECT pr FROM TPatrolResult pr WHERE pr.patrolRecordId = :recordId ORDER BY pr.createTime ASC")
    List<TPatrolResult> findResultsWithItemsByRecordId(@Param("recordId") Long recordId);
    
    /**
     * 优化的查询：一次性查询巡检结果及相关数据
     * 使用JOIN FETCH预加载关联数据，显著减少数据库查询次数
     * 
     * @param recordId 巡检记录ID
     * @return 预加载了关联数据的巡检结果列表
     */
    @Query(value = "SELECT pr.id, pr.patrol_record_id, pr.patrol_item_id, pr.check_result, pr.param_value, " +
           "pr.description, pr.images, pr.latitude, pr.longitude, pr.create_time, pr.update_time, " +
           "pi.device_patrol_item_id, " +
           "dict.item_name, dict.param_type, dict.unit, dict.check_method, dict.description AS check_description, dict.category_name, " +
           "dev.id AS device_id, dev.name AS device_name, dev.type AS device_type " +
           "FROM t_patrol_result pr " +
           "JOIN t_patrol_item pi ON pr.patrol_item_id = pi.id " +
           "JOIN t_device_patrol_item dpi ON pi.device_patrol_item_id = dpi.id " +
           "JOIN t_patrol_item_dictionary dict ON dpi.patrol_item_dict_id = dict.id " +
           "JOIN t_device dev ON dpi.device_id = dev.id " +
           "WHERE pr.patrol_record_id = :recordId " +
           "ORDER BY pr.create_time ASC", nativeQuery = true)
    List<Object[]> findAllResultDataByRecordId(@Param("recordId") Long recordId);
    
    /**
     * 备用查询方法：只查询巡检结果信息，不进行表关联
     * 当复杂的JOIN查询出错时使用此方法
     * 
     * @param recordId 巡检记录ID
     * @return 巡检结果信息
     */
    @Query(value = "SELECT * FROM t_patrol_result WHERE patrol_record_id = :recordId ORDER BY create_time ASC", 
           nativeQuery = true)
    List<TPatrolResult> findSimpleResultsByRecordId(@Param("recordId") Long recordId);
} 