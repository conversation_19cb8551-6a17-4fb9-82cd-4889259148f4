package com.heating.entity.patrol;

import com.heating.converter.JsonConverter;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "t_patrol_plan")
public class TPatrolPlan {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "plan_no", unique = true)
    private String planNo;

    @Column(nullable = false)
    private String name;

    @Column(name = "patrol_type")
    private String patrolType;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<Object> executorIds;

    @Column(name = "schedule_type")
    private String scheduleType;

    @Column(name = "schedule_interval")
    private Integer scheduleInterval;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<Integer> scheduleWeekDays;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<Integer> scheduleMonthDays;

    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<Object> deviceIds;

    @Column(nullable = false)
    private String status;

    @Column(name = "locations")
    private String locations;

    @Column(name = "create_time")
    private LocalDateTime  createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

} 