package com.heating.repository;

import com.heating.entity.permission.TSysRolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色权限关联数据访问接口
 */
@Repository
public interface SysRolePermissionRepository extends JpaRepository<TSysRolePermission, Long> {
    
    /**
     * 根据角色编码查询所有有效的权限关联记录
     * @param roleCode 角色编码
     * @return 权限关联列表
     */
    @Query("SELECT rp FROM TSysRolePermission rp WHERE rp.roleCode = :roleCode AND rp.status = true")
    List<TSysRolePermission> findByRoleCodeAndStatusTrue(@Param("roleCode") String roleCode);
} 