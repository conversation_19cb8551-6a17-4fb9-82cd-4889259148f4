const { request } = require('./request.js');

// 故障报修API模块
const faultApi = {
  // 提交故障报修
  reportFault(data) {
    return request({
      url: '/api/weixin/report',
      method: 'POST',
      data
    });
  },

  // 获取故障列表
  getFaultList(params) {
    return request({
      url: '/api/weixin/list',
      method: 'GET',
      data: params
    });
  },

  // 获取故障详情
  getFaultDetail(id) {
    return request({
      url: `/api/faults/detail/${id}`,
      method: 'GET'
    });
  },

  // 获取用户故障历史记录
  getFaultHistory(userId, page = 1, size = 10, status = null) {
    return request({
      url: '/api/weixin/fault-history',
      method: 'GET',
      data: {
        userId,
        page,
        size,
        status
      }
    });
  },

  // 获取故障详情及跟踪记录
  getFaultDetailWithTracking(faultId, userId) {
    return request({
      url: '/api/weixin/fault-detail',
      method: 'GET',
      data: {
        faultId,
        userId
      }
    });
  },

  // 获取故障统计信息
  getFaultStatistics(userId) {
    return request({
      url: '/api/weixin/fault-statistics',
      method: 'GET',
      data: {
        userId
      }
    });
  }
};

// 字典数据API模块
const dictApi = {
  // 根据字典ID获取字典数据列表 - 使用微信专用接口
  getDictDataByDictId(dictId) {
    return request({
      url: `/api/weixin/data/${dictId}`,
      method: 'GET'
    }).catch(error => {
      console.error('获取字典数据失败，使用默认数据:', error);

      // 如果接口调用失败，使用默认的故障类型数据作为备选
      const defaultFaultTypes = [
        { name: '供暖不热', code: 'heating_not_hot', value: '供暖不热' },
        { name: '暖气漏水', code: 'radiator_leak', value: '暖气漏水' },
        { name: '暖气片不热', code: 'radiator_not_hot', value: '暖气片不热' },
        { name: '管道堵塞', code: 'pipe_blocked', value: '管道堵塞' },
        { name: '阀门故障', code: 'valve_fault', value: '阀门故障' },
        { name: '温控器故障', code: 'thermostat_fault', value: '温控器故障' },
        { name: '其他故障', code: 'other_fault', value: '其他故障' }
      ];

      // 返回默认数据，保持API响应格式一致
      return Promise.resolve({
        code: 200,
        message: '获取字典数据成功(默认数据)',
        data: defaultFaultTypes
      });
    });
  }
};

// 停供申请API模块
const stopSupplyApi = {
  // 提交停供申请
  submitApply(data) {
    return request({
      url: '/api/stop-supply/apply',
      method: 'POST',
      data
    });
  },

  // 获取停供申请列表
  getApplyList(houseId) {
    return request({
      url: '/api/stop-supply/list',
      method: 'GET',
      data: houseId ? { houseId } : {}
    });
  }
};

module.exports = {
  faultApi,
  dictApi,
  stopSupplyApi
};