缴费记录管理 t_payment
功能 ：查看用户缴费记录、手动录入线下缴费。
操作 ：搜索用户 → 查看缴费记录列表 → 添加新缴费。
数据表影响 ：t_payment表新增记录，bills 表的 paid_amount 累加。

账单管理  t_bills 
功能 ：批量生成账单、手动调整账单、查看欠费用户。
操作 ：选择供暖年度 → 勾选用户 → 一键生成账单。
数据表影响 ：bills 表新增记录，total_amount 由 heating_fee_rules 计算生成。

@bill-generate.vue 批量生成账单修改为一件生成账单，默认生成说有住户的账单信息；
1.在@BillController.java 文件中封装一个生成账单的接口，@BillServiceImpl.java 在此文件实现具体的方法；保证可以正常生成所以的账单，计算供暖的供暖费，按照每年的11月15 到次年的3月15为供暖期；供暖费=4个月*供热面积*供暖费；


退费管理 t_refunds 
功能 ：审核退费申请、处理退款。
操作 ：查看待审核退费 → 批准/拒绝 → 更新退费状态。
数据表影响 ：t_refunds 表新增记录，t_bills 表的 paid_amount 回滚。


发票管理t_invoices 
功能 ：开具发票、作废发票。
操作 ：选择缴费记录 → 开具发票 → 下载电子发票。
数据表影响 ：invoices 表新增记录，关联 bills 表。


欠费管理
功能 ：查看逾期账单、发送催缴通知。
操作 ：筛选逾期用户 → 批量发送短信/邮件催缴。
数据表影响 ：overdue_records 表新增记录，自动计算滞纳金。


财务报表
功能 ：按月/年统计收入、欠费、退费数据。
操作 ：选择时间范围 → 导出Excel报表。
数据表影响 ：financial_summary 表每日自动生成统计数据。

小程序流程：
1.住户注册/登录进去
2.首页显示该住户的缴费号或者户号编码（唯一）；要和住户进行绑定，若是住户登陆进去，未绑定户号，则显示绑定户号，跳转到绑定户号界面，若是已经绑定，则需要显示当前的当前住户信息，


手动录入缴费界面。要求显示如下：
1.搜索区域（顶部）房屋地址 / 户号（输入框）
或者：户主姓名（输入框）
[搜索] 按钮
2.住户信息展示区（卡片式）：
房屋地址：XX小区1栋101
户主姓名：张三
建筑面积：100㎡
供暖年度：2024
应缴总金额：2500.00 元
已缴金额：750.00 元
剩余待缴金额：1750.00 元
账单截止日：2024-10-31
账单状态：部分缴清（partial_paid）
提示信息 ：
如果账单状态为“已缴清”，则不能继续录入缴费。
如果账单逾期，显示“⚠️ 已逾期X天，滞纳金：￥12.50”
3.缴费录入表单（核心操作区）
关联账单ID：[自动生成/可选下拉选择] （只读）
缴费金额：[输入框，默认为剩余金额]（必填）
支付方式：[下拉选择] 微信 / 支付宝 / 银行转账 / 现金（默认为银行转账）
缴费日期：[日期选择器，默认今天]（可修改）
备注说明：[多行文本框]（可选）
校验逻辑 ：
缴费金额 ≤ 剩余待缴金额
缴费金额 > 0
不能为空

4.示该住户所有缴费记录（来自 t_payment 表） 
时间   │ 金额      │ 支付方式   │关联账单| 备注     │
请按照以上界面设计调整；使用中文回答


实际收费场景示例
场景1：用户A一次性缴清全年费用
操作流程 ：
管理员在后台生成账单（heating_fee_rules 规则：单价5元/㎡/月，供暖周期5个月，建筑面积100㎡）。
系统自动计算账单金额：100 * 5 * 5 = 2500元，插入 bills 表，状态为 unpaid。
用户A登录后看到账单，点击“立即缴费” → 支付2500元。
系统生成缴费记录（payment_records 表），更新 bills 表的 paid_amount = 2500，状态变为 paid。
用户A申请电子发票，系统生成发票记录（invoices 表）。
数据表变化 ：
bills: paid_amount=2500, status=paid
payment_records: 新增一条记录，金额2500元
invoices: 新增一条发票记录，关联该账单
场景2：用户B分两次缴费（30% + 70%）
操作流程 ：
管理员生成账单（总金额2500元），状态为 unpaid。
用户B首次缴费：支付750元（30%）。
payment_records 表新增记录，金额750元。
bills 表更新 paid_amount=750，状态变为 partial_paid。
用户B第二次缴费：支付1750元（70%）。
payment_records 表新增记录，金额1750元。
bills 表更新 paid_amount=2500，状态变为 paid。
数据表变化 ：
bills: paid_amount=2500, status=paid
payment_records: 新增两条记录（750元、1750元）
场景3：用户C逾期未缴费，触发欠费管理
操作流程 ：
用户C未在截止日期前缴费（due_date=2024-10-31）。
系统每日定时任务检测逾期账单（paid_amount < total_amount）。
自动生成欠费记录（overdue_records 表），计算滞纳金（如日利率0.05%）。
管理员在“欠费管理”模块查看逾期用户，发送催缴短信。
数据表变化 ：
overdue_records: 新增记录，overdue_days=10, penalty_amount=12.5元（2500 * 0.05% * 10天）



用户行为                                 系统处理方式                    是否产生欠费
未申请停供                              按正常供暖计费（100%）   是（逾期未缴）
申请停供 + 批准 + 收取30%     生成账单（30%费用）        是（若未缴）
申请停供 + 批准 + 免费           不生成账单                         否
申请停供 + 未批准                   按正常供暖计费                  是

缴费流程：
供暖前：默认生成所有的账单（默认供暖）
供暖开始:从缴费截至日期开始，默认生成对应的欠费记录，欠费记录为账单计算的缴费金额记录，
若是用户申请停止供暖，则根据申请日期判断，若是日期为供热之前，则更改欠费记录为账单（30%费用，来源于规则）
若是供热之后且逾期，则更新欠费记录的欠费金额，账单（30%费用）+逾期0.05%*天数*账单；

供热开始前申请：
账单应缴金额 = 原账单金额 × 最低缴费比例
不产生欠费记录

供热开始后申请：
账单应缴金额 = 原账单金额 × 最低缴费比例
如果超过逾期日，需要处理欠费记录

2. 欠费记录处理逻辑
当申请日期超过逾期日时：
欠费金额计算：最低缴费金额 + 滞纳金
滞纳金计算：原账单金额 × 滞纳金日利率 × 逾期天数
自动创建或更新欠费记录



供暖计费规则表 t_heating_fee_rule
CREATE TABLE t_heating_fee_rule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单位价格（元/平米/月）',
    heating_start_date DATE NOT NULL COMMENT '供暖开始日期（如2024-11-01）',
    heating_end_date DATE NOT NULL COMMENT '供暖结束日期（如2025-03-31）',
    min_payment_rate DECIMAL(5,2) DEFAULT 0.3 COMMENT '最低缴费比例（如0.3表示30%）',
    penalty_rate DECIMAL(5,4) DEFAULT 0.0005 COMMENT '滞纳金日利率（如0.05%）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否生效（TRUE=生效，FALSE=停用）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT='供暖费用规则配置表，定义单价、周期、滞纳金等规则';



缴费记录表 t_payment
CREATE TABLE t_payment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '缴费记录ID',
    house_id BIGINT NOT NULL COMMENT '关联房屋ID',
    bill_id BIGINT NOT NULL COMMENT '关联账单ID',
    payment_method ENUM('wechat', 'alipay', 'bank_transfer', 'cash') NOT NULL COMMENT '支付方式（wechat=微信，alipay=支付宝，bank_transfer=银行转账，cash=现金）',
    amount DECIMAL(10,2) NOT NULL COMMENT '缴费金额（元）',
    transaction_no VARCHAR(100) COMMENT '交易流水号（第三方支付平台提供）',
    payment_date DATE NOT NULL COMMENT '缴费日期',
    remark TEXT COMMENT '备注（如说明分次缴费）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (house_id) REFERENCES t_house(id),
    FOREIGN KEY (bill_id) REFERENCES t_bill(id)
) COMMENT='房屋缴费记录表，支持多次缴费';

账单表 t_bill
CREATE TABLE t_bill (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '账单ID',
    house_id BIGINT NOT NULL COMMENT '关联房屋ID',
    heating_year INT NOT NULL COMMENT '供暖年度（如2024）',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '应缴总金额（元）',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已缴金额（元）',
    status ENUM('unpaid', 'partial_paid', 'paid', 'overdue') NOT NULL DEFAULT 'unpaid' COMMENT '账单状态（unpaid=未缴，partial_paid=部分缴，paid=已缴，overdue=逾期）',
    due_date DATE NOT NULL COMMENT '缴费截止日期',
    last_paid_date DATE COMMENT '最后一次缴费日期',
    remark TEXT COMMENT '备注（如减免说明）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    FOREIGN KEY (house_id) REFERENCES t_house(id)
) COMMENT='供暖账单表，记录每期账单的应缴、已缴金额及状态';



退费记录表 t_refund
CREATE TABLE t_refund (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '退费记录ID',
    house_id BIGINT NOT NULL COMMENT '关联房屋ID',
    payment_id BIGINT NOT NULL COMMENT '关联缴费记录ID',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退费金额（元）',
    refund_reason TEXT NOT NULL COMMENT '退费原因（如房屋空置、多缴等）',
    status ENUM('pending', 'approved', 'rejected', 'completed') NOT NULL DEFAULT 'pending' COMMENT '退费状态（pending=待审核，approved=已批准，rejected=已拒绝，completed=已完成）',
    admin_remark TEXT COMMENT '管理员审核备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    FOREIGN KEY (house_id) REFERENCES t_house(id),
    FOREIGN KEY (payment_id) REFERENCES t_payment(id)
) COMMENT='退费申请与处理记录表';

发票信息表 t_invoice
CREATE TABLE t_invoice (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '发票ID',
    house_id BIGINT NOT NULL COMMENT '关联房屋ID',
    bill_id BIGINT NOT NULL COMMENT '关联账单ID',
    invoice_number VARCHAR(50) NOT NULL UNIQUE COMMENT '发票编号（唯一）',
    amount DECIMAL(10,2) NOT NULL COMMENT '发票金额（元）',
    status ENUM('issued', 'voided', 'canceled') NOT NULL DEFAULT 'issued' COMMENT '发票状态（issued=已开，voided=作废，canceled=已取消）',
    issue_date DATE NOT NULL COMMENT '发票开具日期',
    remark TEXT COMMENT '备注（如发票类型说明）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (house_id) REFERENCES t_house(id),
    FOREIGN KEY (bill_id) REFERENCES t_bill(id)
) COMMENT='发票管理表，记录电子/纸质发票的开具与状态';

欠费记录表
CREATE TABLE t_overdue_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '欠费记录ID',
    bill_id BIGINT NOT NULL COMMENT '关联账单ID',
    house_id BIGINT NOT NULL COMMENT '关联房屋ID',
    heating_year INT NOT NULL COMMENT '所属供暖年度',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '账单总金额（元）',
    paid_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '已缴金额（元）',
    overdue_amount DECIMAL(10,2) NOT NULL COMMENT '欠费金额（= total_amount - paid_amount）',
    overdue_days INT NOT NULL COMMENT '逾期天数（从 due_date 到当前日期）',
    penalty_rate DECIMAL(5,4) DEFAULT 0.0005 COMMENT '滞纳金日利率（取自规则表）',
    penalty_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '滞纳金金额（= overdue_amount * penalty_rate * overdue_days）',
    status ENUM('active', 'cleared', 'written_off') NOT NULL DEFAULT 'active' COMMENT '状态：active=生效中，cleared=已结清，written_off=已核销',
    first_overdue_date DATE NOT NULL COMMENT '首次逾期日期（即 due_date + 1 天）',
    last_updated_date DATE NOT NULL COMMENT '最后更新日期（每日任务刷新）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    
    FOREIGN KEY (bill_id) REFERENCES t_bill(id) ON DELETE CASCADE,
    FOREIGN KEY (house_id) REFERENCES t_house(id),
    INDEX idx_house_id (house_id),
    INDEX idx_bill_id (bill_id),
    INDEX idx_heating_year (heating_year),
    INDEX idx_status (status),
    UNIQUE KEY uk_bill_unique (bill_id) -- 每个账单只生成一条欠费记录
) COMMENT='欠费记录表，记录逾期账单的欠费金额、滞纳金及状态';


 财务报表汇总表 t_financial_summary（可选）
CREATE TABLE t_financial_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '汇总ID',
    report_month DATE NOT NULL COMMENT '统计月份（如2024-12）',
    total_income DECIMAL(15,2) NOT NULL COMMENT '总收入（元）',
    total_refund DECIMAL(15,2) DEFAULT 0 COMMENT '总退费金额（元）',
    net_income DECIMAL(15,2) NOT NULL COMMENT '净收入（元）',
    house_count INT NOT NULL COMMENT '缴费房屋总数',
    unpaid_houses INT NOT NULL COMMENT '未缴房屋数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间'
) COMMENT='财务报表汇总表，按月/年统计收入、欠费等数据';



微信小程序流程

场景1：在供暖开始前缴费（正常流程）
用户登录小程序，进入【查看账单】
系统展示本年度账单：
text 应缴总金额：2140.00 元 (计算：85.6㎡ * 5元/㎡/月 * 5个月) 已缴金额：0.00 元 缴费截止日：2024-10-31 账单状态：未缴清（unpaid）
支付成功，系统自动：
* 在 t_payment 表中创建记录。
* 更新 t_bill 表：paid_amount = 2140.00, status = paid。
用户可在【电子发票】中为该笔缴费申请发票。



场景2：在供暖开始后缴费（产生滞纳金）
用户行为：逾期缴费。
操作流程：用户在11月15日登录，进入【查看账单】。
系统展示账单，并突出显示逾期信息：

应缴总金额：2140.00 元
已缴金额：0.00 元
缴费截止日：2024-10-31
账单状态：已逾期15天（overdue）
⚠️ 滞纳金：16.05元  (计算：2140 * 0.05% * 15天)
---------------------------------
🔴 当前需支付：2156.05元

用户点击【在线缴费】，系统自动计算并要求支付 2140 + 16.05 = 2156.05元。
支付成功后，系统更新 t_bill 和 t_payment。
t_overdue_records 表中该记录状态更新为 cleared。




业务逻辑：
滞纳金由系统每日定时任务自动计算并累加。
用户最终支付的总金额包含本金和滞纳金。

场景3：在供暖开始前申请停供
用户行为：提前申请，享受减免。

操作流程：用户在10月20日点击【申请停供】。
填写停供开始日期（如2024-11-15）、原因，提交申请。
系统在 t_stop_supply_apply 表中创建 pending 状态的申请。
管理员在 @backend 审批通过。
系统自动执行：
* 计算最低基础费用：2140 * 0.3 = 642.00元。
* 关键：在生成账单时，系统已查询到此申请，故 t_bill.total_amount 直接设置为 642.00元。
用户在【查看账单】中看到：
text 应缴总金额：642.00 元 已缴金额：0.00 元 缴费截止日：2024-10-31 账单状态：未缴清（unpaid） 备注：已申请停供，按30%收取基础热损费
用户在线缴纳642.00元，完成缴费。


场景4：在供暖开始后申请停供

情况A：供暖后申请停供，且此前未缴费


用户行为：试暖后决定不用，申请停供。
用户在12月1日点击【申请停供】，提交申请。
管理员在 @backend 审批通过

系统自动计算：
* 实际供暖天数：11.15 - 12.1 = 17天。
* 实际费用：(2140 / 121) * 17 ≈ 300.00元。
* 最低基础费用：2140 * 0.3 = 642.00元。
* 最终应收：MAX(300, 642) = 642.00元。

 系统自动向用户推送消息：
> “您的停供申请已通过！根据规定，您需缴纳642.00元的基础热损费。[立即缴费]”
用户点击通知，进入小程序，看到待缴金额为642.00元。
用户在线支付642.00元。
系统更新 t_bill：
* paid_amount = 642.00
* status = partial_paid (因 total_amount 仍为2140.00元)
* remark 增加说明。

原始账单 total_amount 不变，但用户只需支付642.00元。


情况B：供暖后申请停供，但此前已全额缴费
用户行为：已缴费后，申请停供并要求退费。
操作流程：
用户在12月1日点击【申请停供】，提交申请。
管理员审批通过。
系统自动计算：
应收金额：642.00元 (最低基础费)。
应退金额：2140 - 642 = 1498.00元。
系统自动在 @backend 生成一条“退费申请”：
关联已支付的 t_payment 记录。
refund_amount = 1498.00。
status = pending。
管理员在“退费管理”模块审核通过。
财务部门将1498.00元退还给用户（原路退回或银行转账）。
t_refund 表状态更新为 completed。

业务逻辑：
通过 t_refund 表处理多缴费用，保证了 t_bill 的原始性。





流程
用户未缴费 → 供暖开始 → 系统自动创建欠费记录（overdue_records） → 
用户在小程序申请停供 → 管理员后台审批通过 → 
系统自动：1. 计算结算金额；2. 终结原欠费记录；3. 生成待缴任务 → 
向用户推送“结算缴费”通知 → 
用户在小程序上支付 → 系统自动更新所有数据 → 业务闭环


当用户在供暖开始后申请停供时，正确的处理流程是“先算旧账，再立新规”。

场景设定
用户：用户D
账单：total_amount = 2500.00元（100㎡ * 5元 * 5个月）
缴费截止日：2024-10-31
当前日期：2024-12-01（已逾期31天）
已供暖天数：47天（11.15 - 12.01）
滞纳金：38.75元（2500 *
0.05% * 31天）
最低基础费：750.00元（2500 * 30%）


正确流程
计算“应缴”与“已欠”
应缴：用户应为已供暖的47天付费。
每天费用：2500 / 121 ≈ 20.66元/天
47天费用：20.66 * 47 ≈ 971.02元
已欠：由于用户未在10月31日前缴费，已产生 38.75元 的滞纳金。
确定最终结算金额
原则：用户需缴纳“实际供暖费用”与“最低基础费”中的较高者。
MAX(971.02, 750.00) = 971.02元


最终结算金额：971.02元（包含47天的供暖费，不包含滞纳金）。
为什么免除滞纳金？ 因为用户申请停供并获批，意味着他不再使用未来的服务。供热公司通常会将此视为用户主动“终止服务”，为了鼓励用户及时申请，会免除滞纳金，但不会免除已发生的供暖费。 
处理数据库
t_overdue_records：
status 从 active 改为 written_off。
remark 添加：“因停供申请获批，原欠费记录已核销，需按实际供暖天数结算”。
t_bill：
total_amount 和 paid_amount 保持不变。
remark 添加：“已生成停供结算，待缴971.02元”。


t_payment：
用户在小程序上支付 971.02元 后，系统新增一条记录，关联此账单。
用户在小程序上操作
收到通知：“您的停供申请已通过，您需缴纳971.02元（47天供暖费），请立即缴费。”
用户支付 971.02元，账单状态变为 partial_paid，但业务上视为“已结清”。


定时任务执行 生成欠费记录：

开始
  ↓
每日定时任务启动 (00:30)
  ↓
查询所有 "可能逾期" 的账单
  (due_date < 今日 且 paid_amount < total_amount)
  ↓
遍历每一个账单
  ↓
对每个账单执行：
  ├─ 步骤1：计算最新的逾期天数和滞纳金
  │     overdue_days = TODAY - due_date
  │     penalty_amount = overdue_amount * penalty_rate * overdue_days
  │
  └─ 步骤2：检查数据库中是否已有记录
        ├─ 如果没有 → 执行 INSERT (生成)
        └─ 如果有 → 执行 UPDATE (更新)
  ↓
所有账单处理完毕
  ↓
结束










