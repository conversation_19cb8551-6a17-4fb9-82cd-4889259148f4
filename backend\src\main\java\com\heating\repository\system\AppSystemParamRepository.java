package com.heating.repository.system;

import com.heating.entity.system.TAppSystemParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 系统参数Repository接口
 */
@Repository
public interface AppSystemParamRepository extends JpaRepository<TAppSystemParam, Integer> {
    
    /**
     * 根据ID查询系统参数
     * @param id 系统参数ID
     * @return 系统参数对象
     */
    TAppSystemParam findById(int id);
} 