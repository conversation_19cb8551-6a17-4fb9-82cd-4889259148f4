// pages/register/complete/index.js
Page({
  data: {
    avatarUrl: '',
    nickName: ''
  },

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    this.setData({
      avatarUrl
    });
  },

  onNickNameInput(e) {
    this.setData({
      nickName: e.detail.value
    });
  },

  handleComplete() {
    const { avatarUrl, nickName } = this.data;
    
    // 更新本地用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    userInfo.avatar = avatarUrl;
    userInfo.nickName = nickName;
    wx.setStorageSync('userInfo', userInfo);

    wx.showToast({
      title: '信息完善成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 1500);
      }
    });
  }
})
