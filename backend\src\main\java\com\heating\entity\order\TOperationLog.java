package com.heating.entity.order;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_operation_log")
@Data
public class TOperationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "work_order_id")
    private long workOrderId; 

    @Column(name = "operation_type")
    private String operationType;

    @Column(name = "operation_desc")
    private String operationDesc;

    @Column(name = "operator_id")
    private long operatorId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Transient
    private String operatorName;

    public TOperationLog() {
    }

    public TOperationLog(long id, long workOrderId, String operationType, 
                    String operationDesc, long operatorId, 
                    LocalDateTime createdAt, String operatorName) {
            this.id = id;
            this.workOrderId = workOrderId;
            this.operationType = operationType;
            this.operationDesc = operationDesc;
            this.operatorId = operatorId;
            this.createdAt = createdAt;
            this.operatorName = operatorName;
    }

    public String getOperatorName() {
        return operatorName;
    }

}