.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 200rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #8c8c8c;
}

/* 故障详情卡片 */
.detail-card {
  background: #ffffff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 30rpx;
}

.fault-no {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 故障状态样式 */
.status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status.confirmed {
  background: #f6ffed;
  color: #52c41a;
}

.status.returned {
  background: #fff2f0;
  color: #ff4d4f;
}

.status.unknown {
  background: #f5f5f5;
  color: #8c8c8c;
}

/* 信息列表 */
.info-list {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  min-height: 44rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item.desc {
  align-items: flex-start;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #8c8c8c;
  flex-shrink: 0;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #262626;
  line-height: 1.6;
}

/* 故障等级样式 */
.fault-level {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.fault-level.severe {
  background: #fff2f0;
  color: #ff4d4f;
}

.fault-level.important {
  background: #fff7e6;
  color: #fa8c16;
}

.fault-level.normal {
  background: #f6ffed;
  color: #52c41a;
}

/* 区块标题 */
.section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #262626;
}

/* 附件列表 */
.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.attachment-item {
  width: 200rpx;
  height: 200rpx;
}

.attachment-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 跟踪卡片 */
.tracking-card {
  background: #ffffff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* 状态信息 */
.status-info {
  margin-bottom: 30rpx;
}

.current-status {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.status-label {
  font-size: 28rpx;
  color: #8c8c8c;
  margin-right: 12rpx;
}

.status-value {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-desc {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.6;
}

/* 子区块标题 */
.subsection-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 工单信息 */
.work-orders {
  margin-bottom: 30rpx;
}

.work-order-item {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.work-order-item:last-child {
  margin-bottom: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-no {
  font-size: 28rpx;
  font-weight: 500;
  color: #262626;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  background: #e6f7ff;
  color: #1890ff;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.repair-user,
.create-time,
.repair-time,
.repair-result {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 操作记录时间线 */
.operation-logs {
  margin-bottom: 30rpx;
}

.timeline {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 60rpx;
  padding-bottom: 30rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 40rpx;
  bottom: -30rpx;
  width: 2rpx;
  background: #e8e8e8;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: 12rpx;
  top: 12rpx;
  width: 16rpx;
  height: 16rpx;
  background: #1890ff;
  border-radius: 50%;
  z-index: 2;
}

.timeline-content {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.operation-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #262626;
}

.operation-time {
  font-size: 24rpx;
  color: #8c8c8c;
}

.operation-desc {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.operator-name {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 无工单状态 */
.no-workorder {
  text-align: center;
  padding: 60rpx 0;
}

.no-workorder-text {
  font-size: 28rpx;
  color: #8c8c8c;
  margin-bottom: 12rpx;
  display: block;
}

.no-workorder-desc {
  font-size: 24rpx;
  color: #bfbfbf;
  display: block;
}