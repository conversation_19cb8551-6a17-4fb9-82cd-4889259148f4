const { paymentApi } = require('../../api/index.js');

Page({
  data: {
    billInfo: {},
    paymentResult: {},
    loading: false
  },

  onLoad(options) {
    // 从支付成功回调中获取参数
    const { billId, amount, transactionNo, paymentMethod } = options;
    
    if (billId && amount && transactionNo && paymentMethod) {
      this.setData({
        billInfo: {
          billId: billId,
          amount: amount,
          paymentMethod: paymentMethod,
          transactionNo: transactionNo
        }
      });
      
      // 处理支付成功后的缴费记录生成
      this.processPaymentSuccess();
    } else {
      wx.showToast({
        title: '支付参数错误',
        icon: 'none'
      });
    }
  },

  /**
   * 处理支付成功后的缴费记录生成
   */
  async processPaymentSuccess() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '处理缴费记录中...'
    });

    try {
      const { billInfo } = this.data;
      
      // 调用后端接口生成缴费记录并更新账单状态
      const response = await paymentApi.processOnlinePayment({
        billId: parseInt(billInfo.billId),
        houseId: userInfo.houseId,
        amount: parseFloat(billInfo.amount),
        paymentMethod: billInfo.paymentMethod,
        transactionNo: billInfo.transactionNo,
        remark: '微信小程序在线缴费'
      });

      wx.hideLoading();
      this.setData({ 
        loading: false,
        paymentResult: response
      });

      // 显示成功提示
      wx.showToast({
        title: '缴费成功',
        icon: 'success',
        duration: 2000
      });

      console.log('缴费处理成功:', response);

    } catch (error) {
      wx.hideLoading();
      this.setData({ loading: false });
      
      console.error('缴费处理失败:', error);
      
      wx.showModal({
        title: '缴费处理失败',
        content: error.message || '系统异常，请联系客服',
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  /**
   * 查看缴费详情
   */
  viewPaymentDetail() {
    const { paymentResult } = this.data;
    if (paymentResult && paymentResult.paymentId) {
      wx.navigateTo({
        url: `/pages/payment/detail?paymentId=${paymentResult.paymentId}`
      });
    }
  },

  /**
   * 查看账单详情
   */
  viewBillDetail() {
    const { billInfo } = this.data;
    if (billInfo && billInfo.billId) {
      wx.navigateTo({
        url: `/pages/bill/detail?billId=${billInfo.billId}`
      });
    }
  },

  /**
   * 返回首页
   */
  goHome() {
    wx.reLaunch({
      url: '/pages/index/index'
    });
  },

  /**
   * 查看缴费记录
   */
  viewPaymentRecords() {
    wx.navigateTo({
      url: '/pages/payment/records'
    });
  }
});
