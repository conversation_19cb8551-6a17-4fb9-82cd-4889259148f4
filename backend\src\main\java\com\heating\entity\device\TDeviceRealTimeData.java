package com.heating.entity.device;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_device_realtime_data")
public class TDeviceRealTimeData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Column(columnDefinition = "DECIMAL(10,2)")
    private BigDecimal temperature;

    @Column(columnDefinition = "DECIMAL(10,2)")
    private BigDecimal pressure;

    @Column(columnDefinition = "DECIMAL(10,2)")
    private BigDecimal flow;

    @Column(name = "collect_time", nullable = false)
    private LocalDateTime collectTime;

    @PrePersist
    protected void onCreate() {
        if (collectTime == null) {
            collectTime = LocalDateTime.now();
        }
    }
} 