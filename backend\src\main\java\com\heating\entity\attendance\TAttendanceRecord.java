package com.heating.entity.attendance;

import com.heating.converter.JsonConverter;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Entity
@Table(name = "t_attendance_record")
public class TAttendanceRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "clock_type", nullable = false, length = 20)
    private String clockType;

    @Column(name = "clock_time", nullable = false)
    private LocalDateTime clockTime;
 
    @Column(name = "latitude")
    private Double latitude;
 
    @Column(name = "longitude")
    private Double longitude;

    @Convert(converter = JsonConverter.class)
    @Column(name = "face_photo", columnDefinition = "json", nullable = false)
    private List<String> facePhoto;

    @Convert(converter = JsonConverter.class)
    @Column(name = "liveness_data", columnDefinition = "json")
    private Map<String, Object> livenessData;

    @Column(length = 20, nullable = false)
    private String status = "normal";

    @Column(name = "leave_type", length = 20)
    private String leaveType;

    @Column(name = "leave_reason")
    private String leaveReason;

    @Convert(converter = JsonConverter.class)
    @Column(name = "leave_proof", columnDefinition = "json")
    private List<String> leaveProof;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
} 