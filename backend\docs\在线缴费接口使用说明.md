# 在线缴费接口使用说明

## 接口概述

本接口用于处理微信小程序在线支付成功后的缴费记录生成和账单状态更新。

## 接口地址

```
POST /api/weixin/payment/online-pay
```

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer {token}
```

### 请求体参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| billId | Long | 是 | 账单ID |
| houseId | Long | 是 | 房屋ID |
| amount | BigDecimal | 是 | 缴费金额 |
| paymentMethod | String | 是 | 支付方式(wechat/alipay) |
| transactionNo | String | 是 | 第三方支付交易号 |
| remark | String | 否 | 备注信息 |

### 请求示例

```json
{
  "billId": 123,
  "houseId": 456,
  "amount": 1500.00,
  "paymentMethod": "wechat",
  "transactionNo": "wx_20250812_123456789",
  "remark": "微信小程序在线缴费"
}
```

## 响应参数

### 成功响应

```json
{
  "code": 200,
  "message": "缴费成功",
  "data": {
    "paymentId": 789,
    "billId": 123,
    "amount": 1500.00,
    "paymentMethod": "wechat",
    "paymentMethodText": "微信支付",
    "transactionNo": "wx_20250812_123456789",
    "paymentDate": "2025-08-12 14:30:00",
    "billStatus": "paid",
    "billStatusText": "已缴清",
    "remainingAmount": 0.00,
    "isFullyPaid": true
  }
}
```

### 失败响应

```json
{
  "code": 400,
  "message": "缴费失败：账单不存在"
}
```

## 业务逻辑

1. **参数验证**：验证必填参数和数据格式
2. **账单验证**：验证账单是否存在且属于指定房屋
3. **金额验证**：验证缴费金额不超过剩余未缴金额
4. **生成缴费记录**：在t_payment表中创建缴费记录
5. **更新账单状态**：更新t_bill表中的已缴金额和状态

## 数据库操作

### t_payment表新增记录

```sql
INSERT INTO t_payment (
  house_id, bill_id, room_no, heat_year, 
  payment_method, amount, transaction_no, 
  payment_date, remark, created_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
```

### t_bill表状态更新

```sql
UPDATE t_bill SET 
  paid_amount = paid_amount + ?,
  last_paid_date = ?,
  status = ?,
  updated_at = ?
WHERE id = ?;
```

## 状态说明

### 账单状态
- `unpaid`: 未缴费
- `partial_paid`: 部分缴费
- `paid`: 已缴清
- `overdue`: 逾期

### 支付方式
- `wechat`: 微信支付
- `alipay`: 支付宝
- `bank_transfer`: 银行转账
- `cash`: 现金支付

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 账单或房屋不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 微信小程序调用示例

```javascript
const { paymentApi } = require('../../api/index.js');

// 支付成功后调用
async function handlePaymentSuccess(paymentData) {
  try {
    const response = await paymentApi.processOnlinePayment({
      billId: paymentData.billId,
      houseId: paymentData.houseId,
      amount: paymentData.amount,
      paymentMethod: 'wechat',
      transactionNo: paymentData.transactionNo,
      remark: '微信小程序在线缴费'
    });
    
    console.log('缴费成功:', response);
    // 跳转到成功页面
    wx.navigateTo({
      url: '/pages/payment/success'
    });
    
  } catch (error) {
    console.error('缴费失败:', error);
    wx.showToast({
      title: error.message || '缴费失败',
      icon: 'none'
    });
  }
}
```

## 注意事项

1. 接口需要用户登录认证，请确保请求头包含有效的Authorization token
2. 缴费金额不能超过账单的剩余未缴金额
3. 第三方交易号应确保唯一性，避免重复缴费
4. 建议在支付成功回调中调用此接口，确保支付和缴费记录的一致性
5. 接口具有事务性，如果任何步骤失败都会回滚，确保数据一致性
