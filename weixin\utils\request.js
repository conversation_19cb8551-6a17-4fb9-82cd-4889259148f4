const BASE_URL = 'http://127.0.0.1:8889';

function request(options) {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync('token');

    // 准备请求头
    const header = {
      'Content-Type': 'application/json',
      ...(options.header || {})
    };

    // 添加token (如果有)
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }
    
    // 完整的URL
    const url = options.url.startsWith('http') ? options.url : BASE_URL + options.url;
    
    console.log('Request URL:', url);
    console.log('Request Data:', options.data);
    
    wx.request({
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      timeout: options.timeout || 10000,
      success: (res) => {
        console.log('Response:', res);
        
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data && res.data.code === 200) {
            resolve(res.data);
          } else {
            // 业务失败
            reject({
              message: res.data?.message || '请求失败',
              code: res.data?.code || 400,
              data: res.data
            });
          }
        } else if (res.statusCode === 401) {
          // 认证失败
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          wx.reLaunch({
            url: '/pages/login/index'
          });
          reject({
            message: '认证失败，请重新登录',
            statusCode: 401
          });
        } else {
          // HTTP状态码错误
          reject({
            message: `请求失败: ${res.statusCode}`,
            statusCode: res.statusCode,
            data: res.data
          });
        }
      },
      fail: (err) => {
        console.error('Request failed:', err);
        reject({
          message: err.errMsg || '网络请求失败',
          error: err
        });
      }
    });
  });
}

module.exports = {
  request
};

