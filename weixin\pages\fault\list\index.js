const { faultApi } = require('../../../api/index.js');


Page({
  data: {
    selectedDate: '',
    currentStatus: '',
    faults: [],
    originalFaults: [],
    userInfo: null
  },

  onLoad() {
    this.loadUserInfo();
    this.loadFaultList();

    // 临时添加测试数据，用于界面调试
    this.loadTestData();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
      return;
    }

    this.setData({
      userInfo: userInfo
    });
  },

  async loadFaultList(params = {}) {
    try {
      wx.showLoading({ title: '加载中' });

      const userInfo = this.data.userInfo;
      if (!userInfo) {
        throw new Error('用户信息不存在');
      }

      // 使用新的API获取故障列表
      const result = await faultApi.getFaultList(
        userInfo.id,
        1, // 页码
        50, // 每页大小，获取更多数据
        params.status || null // 状态筛选
      );

      if (result.code === 200) {
        const faultList = result.data.list || [];

        // 如果有日期筛选，在前端进行过滤
        let filteredFaults = faultList;
        if (params.date) {
          filteredFaults = faultList.filter(fault => {
            const faultDate = fault.occur_time ? fault.occur_time.split(' ')[0] : '';
            return faultDate === params.date;
          });
        }

        this.setData({
          faults: filteredFaults,
          originalFaults: faultList
        });
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载故障列表失败:', error);
      wx.showToast({
        title: error.message || '网络错误',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  onDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({ selectedDate });
    
    this.loadFaultList({ date: selectedDate });
  },

  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ currentStatus: status });

    const params = {};
    if (status) params.status = status;
    if (this.data.selectedDate) params.date = this.data.selectedDate;

    this.loadFaultList(params);
  },

  onPullDownRefresh() {
    const params = {};
    if (this.data.currentStatus) params.status = this.data.currentStatus;
    if (this.data.selectedDate) params.date = this.data.selectedDate;
    
    this.loadFaultList(params);
  },

  goToDetail(e) {
    const faultId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/fault/detail/index?faultId=${faultId}`
    });
  },

  // 加载测试数据（临时用于界面调试）
  loadTestData() {
    const testFaultList = [
      {
        fault_id: 1,
        fault_no: 'GZ202501120001',
        fault_type: '供暖不热',
        fault_level: '重要',
        fault_desc: '客厅暖气片不热，温度明显偏低，影响正常生活',
        fault_status: '待确认',
        fault_source: '用户上报',
        occur_time: '2025-01-12 08:30',
        report_time: '2025-01-12 09:15',
        created_time: '2025-01-12 09:15'
      },
      {
        fault_id: 2,
        fault_no: 'GZ202501110002',
        fault_type: '暖气漏水',
        fault_level: '严重',
        fault_desc: '卧室暖气片接口处漏水，地面已有积水',
        fault_status: '已确认',
        fault_source: '用户上报',
        occur_time: '2025-01-11 14:20',
        report_time: '2025-01-11 14:25',
        created_time: '2025-01-11 14:25'
      },
      {
        fault_id: 3,
        fault_no: 'GZ202501100003',
        fault_type: '阀门故障',
        fault_level: '一般',
        fault_desc: '温控阀无法正常调节，一直处于最大开度',
        fault_status: '已退回',
        fault_source: '用户上报',
        occur_time: '2025-01-10 16:45',
        report_time: '2025-01-10 17:00',
        created_time: '2025-01-10 17:00'
      }
    ];

    this.setData({
      faults: testFaultList,
      originalFaults: testFaultList
    });

    console.log('故障列表测试数据加载完成');
  }
});