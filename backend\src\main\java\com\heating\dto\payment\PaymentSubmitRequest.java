package com.heating.dto.payment;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 缴费提交请求DTO
 * 支持用热缴费和管网维护费两种类型
 */
@Data
public class PaymentSubmitRequest {
    
    /**
     * 账单ID（必填）
     */
    private Long billId;
    
    /**
     * 房屋ID（必填）
     */
    private Long houseId;
    
    /**
     * 缴费金额（必填）
     */
    private BigDecimal amount;
    
    /**
     * 支付方式（必填）
     * wechat: 微信支付
     * alipay: 支付宝
     */
    private String paymentMethod;
    
    /**
     * 交易流水号（必填）
     */
    private String transactionNo;
    
    /**
     * 用热状态（必填）
     * 1: 用热
     * 0: 不用热
     */
    private Integer isHeating;
    
    /**
     * 缴费类型（必填）
     * heating: 用热缴费(全额)
     * maintenance: 不用热(管网维护费)
     */
    private String feeType;
    
    /**
     * 备注信息（可选）
     */
    private String remark;
}
