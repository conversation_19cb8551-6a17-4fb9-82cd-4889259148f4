package com.heating.service;

import java.sql.Date;

import java.util.List;
import java.util.Map;
import com.heating.dto.temperature.RoomTemperatureResponse;
import com.heating.entity.temperature.TRoomTemperature;

/**
 * Service interface for managing temperature-related operations
 */
public interface TemperatureService {
    
    /**
     * Get the current outdoor temperature
     * @return The current outdoor temperature
     */
    Double getCurrentOutdoorTemperature();
    
    /**
     * Save a room temperature record
     * @param temperature The room temperature record to save
     * @return The saved room temperature record
     */
    TRoomTemperature saveRoomTemperature(TRoomTemperature temperature);
    
    /**
     * Get temperature records filtered by community name and/or date
     * @param heat_unit_name The name of the community (optional)
     * @param date The date of the temperature records (optional)
     * @return List of room temperature records
     */
    List<RoomTemperatureResponse>  getTemperatures(String heat_unit_name, Date date);
    
    /**
     * Get a temperature record by its ID
     * @param id The ID of the temperature record
     * @return The room temperature record, or null if not found
     */
    TRoomTemperature getTemperatureById(Long id);
 
}