package com.heating.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.heating.entity.temperature.TOutTemperature;

@Repository
public interface OutTemperatureRepository extends JpaRepository<TOutTemperature, Long> {

    @Query("SELECT temperature FROM TOutTemperature ot " +
           "WHERE ot.recordTime = CURRENT_DATE() " +    
           "ORDER BY ot.recordTime DESC LIMIT 1")
    Double findCurrentOutdoorTemperature();
}