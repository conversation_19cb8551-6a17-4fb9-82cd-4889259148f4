package com.heating.dto.attendance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceTodayResponse {
    private String clockInTime; // 打卡时间
    private String clockInStatus; // 打卡状态：normal-正常,late-迟到
    private String clockOutTime; // 打卡时间
    private String clockOutStatus; // 打卡状态：normal-正常,early-早退
    private String location; // 位置信息
} 