// attendance.js - 考勤相关状态管理

import { attendanceApi } from '@/utils/api.js';

// 全局状态管理 - 考勤模块
export default {
  namespaced: true, // 明确设置为命名空间模块
  state: {
    // 定位上传相关
    locationUploadTimer: null,
    isUploadingLocation: false,
    useNativeGeolocation: true,
    locationUploadInterval: 1, // 默认1分钟
    latitude: 0,
    longitude: 0,
    // 打卡记录
    clockInTime: '',
    clockOutTime: '',
  },
  
  mutations: {
    // 设置定时器
    SET_LOCATION_UPLOAD_TIMER(state, timer) {
      state.locationUploadTimer = timer;
    },
    
    // 设置是否正在上传位置
    SET_IS_UPLOADING_LOCATION(state, status) {
      state.isUploadingLocation = status;
    },
    
    // 设置定位方式
    SET_USE_NATIVE_GEOLOCATION(state, value) {
      state.useNativeGeolocation = value;
    },
    
    // 设置位置上传间隔
    SET_LOCATION_UPLOAD_INTERVAL(state, minutes) {
      state.locationUploadInterval = minutes;
    },
    
    // 设置经纬度
    SET_LOCATION(state, { latitude, longitude }) {
      state.latitude = latitude;
      state.longitude = longitude;
    },
    
    // 设置打卡记录
    SET_CLOCK_RECORDS(state, { clockInTime, clockOutTime }) {
      state.clockInTime = clockInTime || '';
      state.clockOutTime = clockOutTime || '';
    }
  },
  
  actions: {
    // 获取今日打卡记录
    getTodayClockRecord({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        attendanceApi.getTodayRecord()
          .then(res => {
            console.log('获取今日打卡记录:', res);
            if (res.code === 200) {
              // 即使res.data为null也进行处理
              const data = res.data || {};
              const clockInTime = data.clockInTime || '';
              const clockOutTime = data.clockOutTime || '';
              
              // 更新打卡记录
              commit('SET_CLOCK_RECORDS', { 
                clockInTime: clockInTime, 
                clockOutTime: clockOutTime 
              });
              
              // 获取是否开启定位上传的设置
              const isPositioning = uni.getStorageSync('isPositioning') || 0;
              console.log('定位上传设置:', isPositioning);
              
              // 只有当开启了定位上传功能，才执行后续的定位上传逻辑
              if (isPositioning === 1) {
                // 检查是否需要开始或停止定位上传
                // 只有当clockInTime存在且不为空字符串，且clockOutTime不存在或为空字符串时，才启动定位上传
                if (clockInTime && clockInTime !== '' && (!clockOutTime || clockOutTime === '')) {
                  // 已上班打卡但未下班打卡，启动定位上传
                  console.log('检测到已上班但未下班，启动定位上传');
                  dispatch('startLocationUpload');
                } else if (clockOutTime && clockOutTime !== '') {
                  // 已下班打卡，停止定位上传
                  console.log('检测到已下班，停止定位上传');
                  dispatch('stopLocationUpload');
                } else {
                  // 没有打卡记录或返回异常数据，停止定位上传
                  console.log('没有有效的打卡记录，不启动定位上传');
                  dispatch('stopLocationUpload');
                }
              } else {
                // 未开启定位上传功能，确保停止定位上传
                console.log('未开启定位上传功能，确保停止定位上传');
                dispatch('stopLocationUpload');
              }
              
              resolve(data);
            } else {
              console.error('获取打卡记录失败:', res.message);
              reject(new Error('获取打卡记录失败: ' + (res.message || '未知错误')));
            }
          })
          .catch(err => {
            console.error('获取今日打卡记录失败:', err);
            reject(err);
          });
      });
    },
    
    // 开始定时上传位置
    startLocationUpload({ commit, state, dispatch }) {
      // 如果已经在上传，则不重复启动
      if (state.isUploadingLocation) {
        return;
      }
      
      // 如果已经有定时器在运行，先清除
      if (state.locationUploadTimer) {
        clearInterval(state.locationUploadTimer);
      }
      
      // 标记为正在上传位置
      commit('SET_IS_UPLOADING_LOCATION', true);
      
      // 获取上传间隔时间（分钟）
      const interval = state.locationUploadInterval || 1;
      const intervalMs = interval * 60 * 1000; // 转换为毫秒
      
      console.log(`开始定时上传位置，间隔时间: ${interval}分钟`);
      
      // 立即上传一次位置
      dispatch('uploadLocation');
      
      // 设置定时器，定时上传位置
      const timer = setInterval(() => {
        dispatch('uploadLocation');
      }, intervalMs);
      
      // 保存定时器ID
      commit('SET_LOCATION_UPLOAD_TIMER', timer);
    },
    
    // 停止定时上传位置
    stopLocationUpload({ commit, state }) {
      if (state.locationUploadTimer) {
        console.log('停止定时上传位置');
        clearInterval(state.locationUploadTimer);
        commit('SET_LOCATION_UPLOAD_TIMER', null);
      }
      
      // 标记为不在上传位置
      commit('SET_IS_UPLOADING_LOCATION', false);
    },
    
    // 上传位置
    uploadLocation({ state, dispatch }) {
      console.log('准备获取位置并上传轨迹...');
      
      // 获取最新位置
      if (state.useNativeGeolocation) {
        // 优先使用原生定位
        dispatch('getNativeLocation')
          .then(position => {
            // 使用获取到的位置上传轨迹
            const coords = position.coords;
            dispatch('uploadTrajectoryRecord', { 
              latitude: coords.latitude, 
              longitude: coords.longitude 
            });
          })
          .catch(err => {
            console.error('获取原生位置失败，尝试使用uni定位:', err);
            // 回退到uni定位
            dispatch('getUniLocation')
              .then(res => {
                // 使用获取到的位置上传轨迹
                dispatch('uploadTrajectoryRecord', { 
                  latitude: res.latitude, 
                  longitude: res.longitude 
                });
              })
              .catch(err => {
                console.error('位置上传失败，无法获取位置:', err);
              });
          });
      } else {
        // 直接使用uni定位
        dispatch('getUniLocation')
          .then(res => {
            // 使用获取到的位置上传轨迹
            dispatch('uploadTrajectoryRecord', { 
              latitude: res.latitude, 
              longitude: res.longitude 
            });
          })
          .catch(err => {
            console.error('位置上传失败，无法获取位置:', err);
          });
      }
    },
    
    // 使用原生定位API获取位置
    getNativeLocation({ dispatch }) {
      return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        try {
          plus.geolocation.getCurrentPosition(
            (position) => {
              console.log('原生定位成功:', position);
              
              // 提取位置信息
              const coords = position.coords;
              
              resolve(position);
            },
            (err) => {
              console.error('原生定位失败:', err);
              reject({
                errMsg: err.message || '获取位置失败',
                detail: err
              });
            },
            {
              enableHighAccuracy: true, // 高精度定位
              timeout: 15000, // 超时时间
              maximumAge: 0, // 不使用缓存
              provider: 'system', // 使用系统定位
              geocode: false // 不获取地理编码信息
            }
          );
        } catch (e) {
          console.error('调用原生定位API异常:', e);
          reject({
            errMsg: '调用定位服务失败',
            detail: e.message
          });
        }
        // #endif
        
        // #ifndef APP-PLUS
        // 非APP环境下，使用uni定位API
        dispatch('getUniLocation').then(resolve).catch(reject);
        // #endif
      });
    },
    
    // 使用uni.getLocation获取位置
    getUniLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          isHighAccuracy: true,
          highAccuracyExpireTime: 5000,
          success: (res) => {
            console.log('uni位置获取成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('uni获取位置失败:', err);
            reject(err);
          }
        });
      });
    },
    
    // 上传位置轨迹记录
    uploadTrajectoryRecord({ commit }, { latitude, longitude }) {
      // 检查经纬度是否有效
      if (!longitude || !latitude || longitude === 0 || latitude === 0) {
        console.error('经纬度无效，无法上传位置轨迹:', {
          longitude: longitude,
          latitude: latitude
        });
        return Promise.reject({errMsg: '位置参数无效'});
      }
      
      // 更新当前位置
      commit('SET_LOCATION', { latitude, longitude });
      
      // 获取用户ID
      const userId = uni.getStorageSync('userId');
      if (!userId) {
        console.warn('未找到用户ID，无法上传位置轨迹');
        return Promise.reject({errMsg: '未找到用户ID'});
      }
      
      console.log('上传位置轨迹:', {
        userId: userId,
        longitude: longitude,
        latitude: latitude
      });
      
      // 调用API上传位置数据
      return attendanceApi.uploadPersonTrajectory({
        userId: userId, // 传递userId参数
        employeeId: userId, // 同时传递employeeId参数，值与userId相同
        longitude: longitude,
        latitude: latitude
      }).then(res => {
        // 无论服务器返回什么状态码，只要有data且code为200，就认为成功
        if (res && res.code === 200) {
          console.log('位置轨迹上传成功:', res.data);
          return res.data;
        } else {
          const errorMsg = (res && res.message) ? res.message : '未知错误';
          console.error('位置轨迹上传失败:', errorMsg, res);
          return Promise.reject({
            errMsg: '位置轨迹上传失败: ' + errorMsg,
            detail: res
          });
        }
      }).catch(err => {
        // 特殊处理：如果是HTTP 201并且包含有效数据，视为成功
        if (err && err.statusCode === 201 && err.data && err.data.code === 200) {
          console.log('位置轨迹上传成功(201):', err.data.data);
          return err.data.data;
        }
        
        console.error('位置轨迹上传请求异常:', err);
        return Promise.reject(err);
      });
    }
  }
}; 