package com.heating.entity.device;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_device_installation")
public class TDeviceInstallation {
    @Id 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", nullable = false, length = 50)
    private String deviceId;

    @Column(name = "installer_id", nullable = false)
    private Long installerId;

    @Column(name = "install_date", nullable = false)
    private LocalDate installDate;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
}
