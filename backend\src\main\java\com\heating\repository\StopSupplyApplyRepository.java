package com.heating.repository;

import com.heating.entity.bill.TStopSupplyApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StopSupplyApplyRepository extends JpaRepository<TStopSupplyApply, Long> {

    /**
     * 根据房屋ID查询停供申请列表，按创建时间倒序
     * @param houseId 房屋ID
     * @return 停供申请列表
     */
    List<TStopSupplyApply> findByHouseIdOrderByCreatedAtDesc(Long houseId);

    /**
     * 查询所有停供申请列表，按创建时间倒序
     * @return 停供申请列表
     */
    List<TStopSupplyApply> findAllByOrderByCreatedAtDesc();

    /**
     * 根据房屋ID和供暖年度查询已批准的停供申请
     * @param houseId 房屋ID
     * @param heatingYear 供暖年度
     * @param status 申请状态
     * @return 停供申请
     */
    Optional<TStopSupplyApply> findByHouseIdAndHeatingYearAndStatus(Long houseId, Integer heatingYear, TStopSupplyApply.ApplyStatus status);
}