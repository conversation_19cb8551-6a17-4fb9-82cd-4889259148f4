package com.heating.repository.user;

import com.heating.entity.user.TUserWeixin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TUserWeixinRepository extends JpaRepository<TUserWeixin, Long> {

    Optional<TUserWeixin> findByUsername(String username);

    Optional<TUserWeixin> findByOpenid(String openid);

    Optional<TUserWeixin> findByPhone(String phone);

    Optional<TUserWeixin> findByHouseId(Long houseId);
}