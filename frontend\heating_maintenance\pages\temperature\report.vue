<template>
  <view class="container">
    <view class="form-wrapper">
      <!-- 热用户选择 -->
      <view class="form-item">
        <text class="form-label">热用户名称</text>
        <picker
          class="form-input"
          @change="handleHeatUnitChange"
          :value="heatUnitIndex"
          :range="heatUnitOptions"
        >
          <view class="picker-text">{{ heatUnitOptions[heatUnitIndex] || '请选择热用户' }}</view>
        </picker>
      </view>

      <!-- 楼栋号 -->
      <view class="form-item">
        <text class="form-label">楼栋号</text>
        <input
          class="form-input"
          type="text"
          v-model="buildingNo"
          placeholder="请输入楼栋号，如：1号楼"
        />
      </view>

      <!-- 单元号 -->
      <view class="form-item">
        <text class="form-label">单元号</text>
        <input
          class="form-input"
          type="text"
          v-model="unitNo"
          placeholder="请输入单元号，如：1单元"
        />
      </view>

      <!-- 户号 -->
      <view class="form-item">
        <text class="form-label">户号</text>
        <input
          class="form-input"
          type="text"
          v-model="roomNo"
          placeholder="请输入户号，如：1-1-0101"
        />
      </view>

      <!-- 当前室内温度 -->
      <view class="form-item">
        <text class="form-label">当前室内温度</text>
        <view class="temp-slider-container">
          <slider
            @change="handleTempChange"
            :value="reportTemp"
            :min="10"
            :max="35"
            show-value
          />
          <text class="temp-value">{{ reportTemp }}°C</text>
        </view>
      </view>

      <!-- 当前室外温度 -->
      <view class="form-item">
        <text class="form-label">当前室外温度</text>
        <view class="outdoor-temp">{{ outdoorTemp }}°C</view>
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <text class="form-label">图片上传</text>
        <view class="upload-container">
          <view class="upload-list">
            <view
              class="upload-item"
              v-for="(item, index) in uploadImages"
              :key="index"
            >
              <image class="upload-image" :src="item" mode="aspectFill"></image>
              <text class="upload-delete" @click="deleteImage(index)">×</text>
            </view>
            <view
              class="upload-button"
              @click="chooseImage"
              v-if="uploadImages.length < 3"
            >
              <text class="upload-icon">+</text>
              <text class="upload-text">上传图片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="form-item">
        <text class="form-label">备注信息</text>
        <textarea
          class="form-textarea"
          placeholder="请输入备注信息(选填)"
          v-model="reportRemark"
        />
      </view>

      <!-- 提交按钮 -->
      <button 
        class="submit-btn" 
        @click="submitTempReport"
        :disabled="submitting"
      >
        {{ submitting ? '提交中...' : '提交' }}
      </button>
    </view>
  </view>
</template>

<script>
import { heatUnitApi, temperatureReportApi } from '@/utils/api.js'

export default {
  data() {
    return {
      heatUnitList: [], // 热用户列表数据
      heatUnitOptions: [], // 热用户名称列表
      heatUnitIndex: 0,
      buildingNo: '',
      unitNo: '',
      roomNo: '',
      reportTemp: 22,
      reportRemark: '',
      outdoorTemp: 0,
      uploadImages: [],
      latitude: null,
      longitude: null,
      submitting: false
    }
  },
  onLoad() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
      try {
        await Promise.all([
          this.getHeatUnitList(),
          this.getOutdoorTemperature(),
          this.getLocation()
        ])
      } catch (error) {
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 获取热用户列表
    async getHeatUnitList() {
      try {
        const res = await heatUnitApi.getList()
        if (Array.isArray(res.data)) {
          this.heatUnitList = res.data
          this.heatUnitOptions = this.heatUnitList.map(item => item.name)
        } else {
          throw new Error('获取热用户列表失败')
        }
      } catch (err) {
        throw new Error('获取热用户列表失败')
      }
    },

    // 获取室外温度
    getOutdoorTemperature() {
      return new Promise((resolve) => {
        setTimeout(() => {
          this.outdoorTemp = (Math.random() * 10 + 15).toFixed(1)
          resolve()
        }, 500)
      })
    },

    // 获取位置信息
    getLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          isHighAccuracy: true,
          highAccuracyExpireTime: 3000,
          success: (res) => {
            this.latitude = res.latitude
            this.longitude = res.longitude
            resolve(res)
          },
          fail: (err) => {
            console.error('获取位置失败:', err)
            // 使用默认坐标（西安市中心）
            this.latitude = 34.343147
            this.longitude = 108.939621
            resolve({
              latitude: this.latitude,
              longitude: this.longitude
            })
          }
        })
      })
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.uploadImages.length,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths]
        }
      })
    },

    // 删除图片
    deleteImage(index) {
      this.uploadImages.splice(index, 1)
    },

    // 处理温度变化
    handleTempChange(e) {
      this.reportTemp = e.detail.value
    },

    // 处理热用户选择
    handleHeatUnitChange(e) {
      this.heatUnitIndex = e.detail.value
    },

    // 上传图片
    async uploadImage(filePath) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: '/api/upload/image',
          filePath: filePath,
          name: 'file',
          header: {
            Authorization: 'Bearer ' + uni.getStorageSync('token')
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 200) {
                resolve(data.data.url)
              } else {
                reject(new Error(data.message || '上传失败'))
              }
            } catch (e) {
              reject(new Error('解析响应失败'))
            }
          },
          fail: (err) => {
            reject(new Error('网络请求失败'))
          }
        })
      })
    },

    // 提交室温上报
    async submitTempReport() {
      try {
        // 表单验证
        if (this.heatUnitOptions.length === 0) {
          throw new Error('请等待热用户数据加载')
        }

        const validations = [
          { value: this.buildingNo.trim(), message: '请输入楼栋号' },
          { value: this.unitNo.trim(), message: '请输入单元号' },
          { value: this.roomNo.trim(), message: '请输入户号' }
        ]

        for (const validation of validations) {
          if (!validation.value) {
            throw new Error(validation.message)
          }
        }

        this.submitting = true
        uni.showLoading({
          title: '正在提交',
          mask: true
        })

        // 上传图片
        const uploadedImages = []
        if (this.uploadImages.length > 0) {
          for (const filePath of this.uploadImages) {
            const imageUrl = await this.uploadImage(filePath)
            uploadedImages.push(imageUrl)
          }
        }

        // 构建提交数据
        const reportData = {
          heat_unit_name: this.heatUnitOptions[this.heatUnitIndex],
          building_no: this.buildingNo.trim(),
          unit_no: this.unitNo.trim(),
          room_no: this.roomNo.trim(),
          indoor_temp: this.reportTemp,
          outdoor_temp: this.outdoorTemp,
          latitude: this.latitude,
          longitude: this.longitude,
          images: uploadedImages,
          videos: [],
          remark: this.reportRemark.trim(),
          report_user_id: uni.getStorageSync('userId')
        }

        const response = await temperatureReportApi.submit(reportData)

        if (response.code === 200) {
          uni.showToast({
            title: '室温上报成功',
            icon: 'success'
          })
          uni.navigateBack()
        } else {
          throw new Error(response.message || '提交失败')
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '提交失败',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.submitting = false
        uni.hideLoading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.form-wrapper {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.picker-text {
  line-height: 80rpx;
  color: #333;
}

.temp-slider-container {
  margin-top: 20rpx;
  position: relative;

  .temp-value {
    position: absolute;
    right: 0;
    top: -50rpx;
    font-size: 32rpx;
    color: #1989fa;
    font-weight: bold;
  }
}

.outdoor-temp {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
  padding: 10rpx 0;
}

.upload-container {
  .upload-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;

    .upload-item,
    .upload-button {
      width: 180rpx;
      height: 180rpx;
      margin: 10rpx;
      border-radius: 8rpx;
      overflow: hidden;
      position: relative;
    }

    .upload-item {
      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .upload-delete {
        position: absolute;
        top: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        text-align: center;
        line-height: 40rpx;
        font-size: 24rpx;
        z-index: 1;
      }
    }

    .upload-button {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #f5f5f5;
      border: 1rpx dashed #ddd;

      .upload-icon {
        font-size: 60rpx;
        color: #999;
        line-height: 1;
        margin-bottom: 10rpx;
      }

      .upload-text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #1989fa;
  border-radius: 8rpx;
  color: #fff;
  font-size: 32rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }

  &[disabled] {
    opacity: 0.5;
  }
}
</style> 