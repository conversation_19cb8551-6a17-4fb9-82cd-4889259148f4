<view class="container">
  <form bindsubmit="submitForm">
    <view class="form-group">
      <view class="form-item">
        <text class="label">换热站</text>
        <picker 
          bindchange="bindStationChange" 
          value="{{stationIndex}}" 
          range="{{stations}}"
          range-key="name">
          <view class="picker">
            {{stations[stationIndex].name || '请选择换热站'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">发生时间</text>
        <picker mode="multiSelector" 
               value="{{dateTime}}" 
               range="{{dateTimeArray}}" 
               bindchange="onDateTimeChange">
          <view class="picker">
            {{form.occur_time || '请选择故障发生时间'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">故障类型</text>
        <picker bindchange="onTypeChange" 
                value="{{typeIndex}}" 
                range="{{types}}" 
                range-key="name">
          <view class="picker">
            {{types[typeIndex].name || '请选择故障类型'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">故障等级</text>
        <picker bindchange="onLevelChange" 
                value="{{levelIndex}}" 
                range="{{levels}}" 
                range-key="name">
          <view class="picker">
            {{levels[levelIndex].name || '请选择故障等级'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">故障描述</text>
        <textarea 
          placeholder="请输入故障描述" 
          class="textarea"
          value="{{form.fault_desc}}"
          bindinput="onDescInput"
        ></textarea>
      </view>

      <view class="form-item">
        <view class="label">故障照片</view>
        <view class="upload-box">
          <view class="image-list">
            <view 
              class="image-item" 
              wx:for="{{form.images}}" 
              wx:key="*this"
            >
              <image 
                src="{{item}}" 
                mode="aspectFill" 
                bindtap="previewImage" 
                data-url="{{item}}"
              ></image>
              <view class="delete-btn" catchtap="deleteImage" data-index="{{index}}">
                <text class="wx-icon">×</text>
              </view>
            </view> 
            <view class="upload-actions" wx:if="{{form.images.length < 4}}">
              <view class="upload-btn" bindtap="chooseFromAlbum">
                <text class="wx-icon">📁</text>
                <text class="upload-text">相册</text>
              </view>
              <view class="upload-btn" bindtap="takePhoto">
                <text class="wx-icon">📷</text>
                <text class="upload-text">拍照</text>
              </view>
            </view>
          </view>
          <text class="tips">请上传清晰的故障现场照片（最多4张）</text>
        </view>
      </view>
 
      <!-- <view class="form-item">
        <text class="label">视频附件</text>
        <view class="upload-box">
          <view class="video-list">
            <block wx:if="{{videoUrl}}">
              <view class="video-item">
                <video src="{{videoUrl}}"></video>
                <view class="delete-btn" bindtap="deleteVideo">×</view>
              </view>
            </block>
            <view class="upload-btn" bindtap="chooseVideo" wx:if="{{!videoUrl}}">
              <text class="plus">+</text>
            </view>
          </view>
        </view>
      </view> -->

    </view>

    <view class="submit-btn">
      <button type="primary" form-type="submit">提交故障</button>
    </view>
  </form>
</view> 