package com.heating.service;

import com.heating.dto.bill.*;

public interface BillService {

    /**
     * 获取账单列表
     */
    BillListResponse getBillList(BillListRequest request);

    /**
     * 获取账单详情
     */
    BillListResponse.BillDetail getBillDetail(BillDetailRequest request);

    /**
     * 查看账单详情（包含完整信息：基本信息、账单信息、缴费记录、逾期记录）
     * @param request 查看账单请求
     * @return 账单详情响应
     */
    BillDetailViewResponse viewBillDetail(BillDetailViewRequest request);

    /**
     * 根据账单ID获取缴费记录列表
     * @param billId 账单ID
     * @return 缴费记录列表
     */
    PaymentListResponse getPaymentListByBillId(Long billId);

    /**
     * 根据房屋ID获取所有缴费记录
     * @param houseId 房屋ID
     * @return 缴费记录列表
     */
    PaymentListResponse getPaymentRecordsByHouseId(Long houseId);

    /**
     * 获取票据详情
     * @param paymentId 缴费记录ID
     * @return 票据详情
     */
    InvoiceDetailResponse getInvoiceDetail(Long paymentId);

    /**
     * 生成票据PDF
     * @param paymentId 缴费记录ID
     * @return PDF文件URL
     */
    String generateInvoicePDF(Long paymentId);

    /**
     * 获取简化的账单信息
     * 根据用热状态返回不同的费用信息，严格按照账单表数据
     * @param request 简化账单信息请求
     * @return 简化账单信息响应
     */
    SimpleBillInfoResponse getSimpleBillInfo(SimpleBillInfoRequest request);
}





