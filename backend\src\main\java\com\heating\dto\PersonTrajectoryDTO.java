package com.heating.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 人员轨迹数据传输对象
 */
@Data
public class PersonTrajectoryDTO {

    private Integer id;

    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Integer employeeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 状态：0-正常 1-异常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 