package com.heating.entity.bill;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 欠费记录实体类
 * 对应数据库表 t_overdue_records
 */
@Data
@Entity
@Table(name = "t_overdue_records")
public class TOverdueRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联账单ID
     */
    @Column(name = "bill_id", nullable = false)
    private Long billId;

    /**
     * 关联房屋ID
     */
    @Column(name = "house_id", nullable = false)
    private Long houseId;

    /**
     * 所属供暖年度
     */
    @Column(name = "heating_year", nullable = false)
    private Integer heatingYear;

    /**
     * 账单总金额（元）
     */
    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    /**
     * 已缴金额（元）
     */
    @Column(name = "paid_amount", precision = 10, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;

    /**
     * 欠费金额（= total_amount - paid_amount）
     */
    @Column(name = "overdue_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal overdueAmount;

    /**
     * 逾期天数（从 due_date 到当前日期）
     */
    @Column(name = "overdue_days", nullable = false)
    private Integer overdueDays;

    /**
     * 滞纳金日利率（取自规则表）
     */
    @Column(name = "penalty_rate", precision = 5, scale = 4)
    private BigDecimal penaltyRate = new BigDecimal("0.0005");

    /**
     * 滞纳金金额（= overdue_amount * penalty_rate * overdue_days）
     */
    @Column(name = "penalty_amount", precision = 10, scale = 2)
    private BigDecimal penaltyAmount = BigDecimal.ZERO;

    /**
     * 状态：active=生效中，cleared=已结清，written_off=已核销
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private OverdueStatus status = OverdueStatus.active;

    /**
     * 首次逾期日期（即 due_date + 1 天）
     */
    @Column(name = "first_overdue_date", nullable = false)
    private LocalDate firstOverdueDate;

    /**
     * 最后更新日期（每日任务刷新）
     */
    @Column(name = "last_updated_date", nullable = false)
    private LocalDate lastUpdatedDate;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后修改时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 逾期状态枚举
     */
    public enum OverdueStatus {
        active,      // 生效中
        cleared,     // 已结清
        written_off  // 已核销
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
