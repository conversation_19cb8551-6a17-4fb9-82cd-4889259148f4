# 简化账单信息接口实现说明

## 概述

根据您的要求，重新实现了一个简化的账单信息接口，严格按照账单表数据，根据用热状态和缴费状态显示不同的信息。

## 实现要求

### 1. 数据来源
- **严格按照账单表数据**：不涉及停供申请等复杂场景
- **用热费用**：从 `t_bill.total_amount` 获取
- **欠费金额**：从 `t_bill.overdue_amount` 获取
- **用热状态**：从 `t_house.is_heating` 获取

### 2. 显示逻辑

#### 根据用热状态判别费用类型
- **用热状态 (is_heating=1)**：显示"用热费"，金额为账单表的 `total_amount`
- **不用热状态 (is_heating=0)**：显示"管网维护费"，金额为最低缴费金额（根据计费规则计算）

#### 根据缴费状态判别显示内容
- **未缴费状态**：显示用热费、欠费金额、应缴费
- **已缴费状态**：显示用热费、欠费金额、应缴费、实际缴费

## 后端实现

### 1. 新增DTO类

#### SimpleBillInfoRequest.java
```java
/**
 * 简化的账单信息请求DTO
 */
@Data
public class SimpleBillInfoRequest {
    private Long houseId;        // 房屋ID（必填）
    private Integer heatingYear; // 供暖年度（可选）
}
```

#### SimpleBillInfoResponse.java
```java
/**
 * 简化的账单信息响应DTO
 */
@Data
public class SimpleBillInfoResponse {
    private Integer code;
    private String message;
    private BillData data;
    
    // 包含：HouseInfo, BillFeeInfo, PaymentStatusInfo
}
```

### 2. 核心业务逻辑

#### BillServiceImpl.getSimpleBillInfo()
```java
/**
 * 获取简化的账单信息
 * 根据用热状态返回不同的费用信息，严格按照账单表数据
 */
@Override
public SimpleBillInfoResponse getSimpleBillInfo(SimpleBillInfoRequest request)
```

**处理流程：**
1. 参数验证（房屋ID必填）
2. 获取房屋信息（包含用热状态）
3. 查询账单信息（从t_bill表）
4. 根据用热状态构建费用信息
5. 根据缴费状态构建显示信息

#### 费用计算逻辑

##### 用热状态 (is_heating=1)
```java
// 用热费 = t_bill.total_amount
billFeeInfo.setHeatingFee(bill.getTotalAmount());
billFeeInfo.setFeeTypeName("用热费");
```

##### 不用热状态 (is_heating=0)
```java
// 管网维护费 = t_bill.total_amount * min_payment_rate
BigDecimal maintenanceFee = calculateMaintenanceFee(house, bill);
billFeeInfo.setHeatingFee(maintenanceFee);
billFeeInfo.setFeeTypeName("管网维护费");
```

#### 应缴费计算
```java
// 应缴费金额 = 用热费/管网维护费 + 欠费金额
BigDecimal totalPayableAmount = billFeeInfo.getHeatingFee().add(overdueAmount);
```

### 3. 控制器接口

#### WeixinController.getSimpleBillInfo()
```java
/**
 * 获取简化的账单信息接口
 * POST /api/weixin/bill/simple-info
 */
@PostMapping("/bill/simple-info")
public ResponseEntity<Map<String, Object>> getSimpleBillInfo(@RequestBody SimpleBillInfoRequest request)
```

## 前端实现

### 1. API调用

#### billApi.getSimpleBillInfo()
```javascript
// 获取简化的账单信息
getSimpleBillInfo(data = {}) {
  const userInfo = wx.getStorageSync('userInfo');
  const requestData = {
    houseId: data.houseId || userInfo.houseId,
    heatingYear: data.heatingYear
  };

  return request({
    url: '/api/weixin/bill/simple-info',
    method: 'POST',
    data: requestData
  });
}
```

### 2. 页面显示逻辑

#### bill-view.js
```javascript
// 调用新的简化账单信息接口
const response = await billApi.getSimpleBillInfo({
  houseId: userInfo.houseId,
  heatingYear: heatingYear
});

// 构建显示数据
const billInfo = {
  heatingFee: data.billFeeInfo.heatingFee,
  feeTypeName: data.billFeeInfo.feeTypeName,
  overdueAmount: data.billFeeInfo.overdueAmount,
  totalPayableAmount: data.billFeeInfo.totalPayableAmount,
  actualPaidAmount: data.billFeeInfo.actualPaidAmount,
  showActualPaidAmount: data.paymentStatusInfo.showActualPaidAmount
};
```

#### bill-view.wxml
```xml
<!-- 用热费用/管网维护费 -->
<view class="amount-row">
  <text class="amount-label">{{billInfo.feeTypeName || '用热费'}}：</text>
  <text class="amount-value">¥{{billInfo.heatingFee}}</text>
</view>

<!-- 欠费金额 -->
<view class="amount-row" wx:if="{{billInfo.overdueAmount && billInfo.overdueAmount > 0}}">
  <text class="amount-label">欠费金额：</text>
  <text class="amount-value debt">¥{{billInfo.overdueAmount}}</text>
</view>

<!-- 应缴费金额 -->
<view class="amount-row main-amount">
  <text class="amount-label">应缴费：</text>
  <text class="amount-value">¥{{billInfo.totalPayableAmount}}</text>
</view>

<!-- 实际缴费金额（仅已缴费状态显示） -->
<view class="amount-row" wx:if="{{billInfo.showActualPaidAmount}}">
  <text class="amount-label">实际缴费：</text>
  <text class="amount-value paid">¥{{billInfo.actualPaidAmount}}</text>
</view>
```

## 显示效果

### 用热状态 + 未缴费
```
用热费：¥2,500.00
欠费金额：¥500.00 (如果有)
应缴费：¥3,000.00
```

### 用热状态 + 已缴费
```
用热费：¥2,500.00
欠费金额：¥500.00 (如果有)
应缴费：¥3,000.00
实际缴费：¥3,000.00
```

### 不用热状态 + 未缴费
```
管网维护费：¥750.00
欠费金额：¥200.00 (如果有)
应缴费：¥950.00
```

### 不用热状态 + 已缴费
```
管网维护费：¥750.00
欠费金额：¥200.00 (如果有)
应缴费：¥950.00
实际缴费：¥950.00
```

## 测试建议

1. **用热状态测试**：验证用热费显示和计算
2. **不用热状态测试**：验证管网维护费显示和计算
3. **缴费状态测试**：验证实际缴费金额的显示控制
4. **欠费情况测试**：验证欠费金额的显示和计算
5. **边界情况测试**：验证无账单、无房屋等异常情况
