.container {
  background: #ffffff;
  min-height: 100vh;
  padding: 0;
}

.welcome-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 50rpx 40rpx;
  margin: 40rpx 30rpx 30rpx;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.welcome-text {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.user-info {
  background: #ffffff;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  position: relative;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.info-item:nth-child(1) .info-icon {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.info-item:nth-child(1) .info-icon::before {
  content: "🏠";
  filter: brightness(0) invert(1);
}

.info-item:nth-child(2) .info-icon {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.info-item:nth-child(2) .info-icon::before {
  content: "📍";
  filter: brightness(0) invert(1);
}

.info-item:nth-child(3) .info-icon {
  background: linear-gradient(135deg, #faad14, #d48806);
}

.info-item:nth-child(3) .info-icon::before {
  content: "📐";
  filter: brightness(0) invert(1);
}

.info-item:nth-child(4) .info-icon {
  background: linear-gradient(135deg, #f5222d, #cf1322);
}

.info-item:nth-child(4) .info-icon::before {
  content: "🔥";
  filter: brightness(0) invert(1);
}

.info-label {
  font-size: 30rpx;
  color: #8c8c8c;
  margin-right: 20rpx;
  min-width: 120rpx;
  font-weight: 400;
}

.info-value {
  font-size: 32rpx;
  color: #262626;
  font-weight: 500;
  flex: 1;
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 0 30rpx 40rpx;
}

.function-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 50rpx 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
}

.function-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.08);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 24rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.function-text {
  font-size: 30rpx;
  color: #262626;
  font-weight: 500;
  line-height: 1.4;
}

/* 功能图标样式 */
.function-item:nth-child(1) .function-icon {
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
}

.function-item:nth-child(1) .function-icon::before {
  content: "📊";
}

.function-item:nth-child(2) .function-icon {
  background: linear-gradient(135deg, #f6ffed, #d9f7be);
}

.function-item:nth-child(2) .function-icon::before {
  content: "💳";
}

.function-item:nth-child(3) .function-icon {
  background: linear-gradient(135deg, #fff7e6, #ffd591);
}

.function-item:nth-child(3) .function-icon::before {
  content: "📄";
}

.function-item:nth-child(4) .function-icon {
  background: linear-gradient(135deg, #fff2e8, #ffbb96);
}

.function-item:nth-child(4) .function-icon::before {
  content: "🔧";
}

.function-item:nth-child(5) .function-icon {
  background: linear-gradient(135deg, #f0f9ff, #bfdbfe);
}

.function-item:nth-child(5) .function-icon::before {
  content: "📋";
}

.function-item:nth-child(6) .function-icon {
  background: linear-gradient(135deg, #fff0f6, #ffadd2);
}

.function-item:nth-child(6) .function-icon::before {
  content: "💬";
}
