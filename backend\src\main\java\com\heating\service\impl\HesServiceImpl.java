package com.heating.service.impl;
 
import com.heating.service.HesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import com.heating.dto.hes.HesRequest;
import com.heating.dto.hes.HesDetailRequest;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.ArrayList; 
import com.heating.dto.hes.HesControlRequest;
import com.heating.dto.hes.HesHistoryRequest;
import com.heating.dto.hes.HesChartRequest;
import com.heating.dto.hes.HesAlarmsListRequest;
import com.heating.dto.hes.HesAlarmsDetailRequest;
import com.heating.dto.hes.HesAlarmsStatsRequest;
import com.heating.dto.hes.HesOnlineRateResponse;
import com.heating.repository.HesRepository;

@Service
public class HesServiceImpl implements HesService {

    private static final Logger logger = LoggerFactory.getLogger(HesServiceImpl.class);
 
    // 第三方接口
    // 换热站列表   
    @Value("${hes.api.list}")
    private String hesApiList;

    // 换热站详情
    @Value("${hes.api.detail}")
    private String hesApiDetail;

    // 换热站控制
    @Value("${hes.api.control}")
    private String hesApiControl;

    // 换热站远程协助
    @Value("${hes.api.remote}")
    private String hesApiRemote;    

    // 换热站历史数据
    @Value("${hes.api.history}")
    private String hesApiHistory;

    // 换热站数据曲线
    @Value("${hes.api.chart}")
    private String hesApiChart; 

    // 换热站告警列表
    @Value("${hes.api.alarms.list}")
    private String hesApiAlarmsList;

    // 换热站告警详情
    @Value("${hes.api.alarms.detail}")
    private String hesApiAlarmsDetail;  

    // 换热站告警统计
    @Value("${hes.api.alarms.stats}")
    private String hesApiAlarmsStats;   

    @Autowired
    private HesRepository hesRepository;

    public List<Map<String, Object>> getApi(String apiUrl,Object request){
        try {
                    HttpClient client = HttpClient.newHttpClient(); 
                    // 构建请求体
                    String requestBody = "";
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        requestBody = objectMapper.writeValueAsString(request);
                    } catch (Exception e) {
                        throw new RuntimeException("Error converting request to JSON", e);
                    } 
                    HttpRequest httpRequest = HttpRequest.newBuilder()
                            .uri(URI.create(apiUrl))
                            .header("Content-Type", "application/json")
                            .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                            .build();
                    
                    // 发送请求并获取响应
                    HttpResponse<String> response = client.send(httpRequest, 
                            HttpResponse.BodyHandlers.ofString());
                    
                    // 解析响应数据 
                    try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            // Parse the entire response as a Map
                            Map<String, Object> responseMap = objectMapper.readValue(response.body(), 
                                    new TypeReference<Map<String, Object>>() {});
                            
                            // Check if the response is successful
                            List<Map<String, Object>> resultList = new ArrayList<>();
                            resultList.add(responseMap);
                            return resultList;
                    } catch (Exception e) {
                        throw new RuntimeException("Error parsing response data: " + e.getMessage(), e);
                    } 
            } catch (Exception e) {
                throw new RuntimeException("Error fetching HES data: " + e.getMessage(), e);
            }
    }    

    @Override
    public List<Map<String, Object>> getHesList(HesRequest request) { 
        return getApi(hesApiList,request);
    } 

    @Override
    public Map<String, Object> getHesDetail(HesDetailRequest request) {
        return getApi(hesApiDetail,request).get(0);
    }

    @Override
    public Map<String, Object> controlHes(HesControlRequest request) {
        return getApi(hesApiControl,request).get(0);
    }

    @Override
    public List<Map<String, Object>> getHesHistory(HesHistoryRequest request) {
        return getApi(hesApiHistory,request);
    }   

    /**
     * 获取换热站数据曲线
     * @param request 包含查询条件的请求
     * @return 换热站数据曲线
     */
    @Override
    public Map<String, Object> getHesChart(HesChartRequest request) {
        return getApi(hesApiChart,request).get(0);
    }
    
    /**
     * 获取换热站告警列表
     * @param request 包含查询条件的请求
     * @return 换热站告警列表
     */
    @Override
    public Map<String, Object> getHesAlarmsList(HesAlarmsListRequest request) {
        return getApi(hesApiAlarmsList,request).get(0);    
    }
    
    /**
     * 获取换热站告警详情
     * @param request 包含告警ID的请求
     * @return 换热站告警详情
     */
    @Override
    public Map<String, Object> getHesAlarmsDetail(HesAlarmsDetailRequest request) {
        return getApi(hesApiAlarmsDetail,request).get(0);  
    }
    
    /**
     * 获取换热站告警统计
     * @param request 包含查询条件的请求
     * @return 换热站告警统计
     */
    @Override
    public Map<String, Object> getHesAlarmsStats(HesAlarmsStatsRequest request) {
        return getApi(hesApiAlarmsStats,request).get(0);   
    }
    
    @Override
    public HesOnlineRateResponse getOnlineRate() {
        logger.info("获取换热站在线率");
        
        try {
            // 查询所有换热站数量
            long total = hesRepository.count();
            
            // 查询在线换热站数量（根据状态字段判断，一般status=1表示在线）
            long online = hesRepository.countByStatus(1);
            
            // 计算在线率
            String onlineRate = total > 0 ? String.format("%.1f%%", (online * 100.0 / total)) : "0.0%";
            
            return new HesOnlineRateResponse((int) total, (int) online, onlineRate);
        } catch (Exception e) {
            logger.error("获取换热站在线率出错", e);
            return new HesOnlineRateResponse(0, 0, "0.0%");
        }
    }
}