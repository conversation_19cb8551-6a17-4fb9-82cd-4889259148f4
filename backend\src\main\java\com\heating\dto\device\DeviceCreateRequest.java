package com.heating.dto.device;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class DeviceCreateRequest {
    private String scanCode;
    private String name;
    private String type;
    private String model;
    private String manufacturer;
    
    private LocationInfo location;
    private Specs specs;
    private MaintenanceInfo maintenance;
    private List<String> photos;
    
    @Data
    public static class LocationInfo {
        private String building;
        private String floor;
        private String room;
        private Coordinates coordinates;
        
        @Data
        public static class Coordinates {
            private Double lat;
            private Double lng;
        }
    }
    
    @Data
    public static class Specs {
        private BigDecimal power;
        private TemperatureRange temperature;
        private String protocol;
        
        @Data
        public static class TemperatureRange {
            private BigDecimal min;
            private BigDecimal max;
        }
    }
    
    @Data
    public static class MaintenanceInfo {
        private Integer period;
        private List<String> staff;
    }
} 