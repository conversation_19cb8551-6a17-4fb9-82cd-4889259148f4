<view class="container">
  <!-- 页面标题 -->

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  
  <!-- 账单列表 -->
  <view class="bill-section" wx:if="{{!loading}}">
    <view class="section-title">账单列表</view>
    
    <view class="bill-list" wx:if="{{billList.length > 0}}">
      <view class="bill-item" wx:for="{{billList}}" wx:key="id" 
            bindtap="viewBillDetail" data-id="{{item.id}}">
        
        <!-- 账单头部 -->
        <view class="bill-item-header">
          <view class="bill-status-inline">
            <text class="status-icon {{item.status}}">{{item.statusIcon}}</text>
            <text class="status-text {{item.status}}">{{item.statusText}}</text>
          </view>
          <text class="bill-period-inline">{{item.period}}</text>
        </view>
        
        <!-- 账单内容 -->
        <view class="bill-item-content">
          <view class="bill-info">
            <text class="amount-text">¥{{item.amount}}</text>
            
            <text class="paid-amount-text" wx:if="{{item.paidAmount && item.paidAmount !== '0'}}">
              已缴金额：¥{{item.paidAmount}}
            </text>
            
            <text class="due-date">截止日期：{{item.dueDate}}</text>
            
            <text class="pay-time" wx:if="{{item.lastPaidDate}}">
              缴费时间：{{item.lastPaidDate}}
            </text>
          </view>
          
          <!-- 操作按钮 -->
          <view class="bill-actions">
            <button class="action-btn pay-btn" 
                    wx:if="{{item.status === 'unpaid' || item.status === 'partial_paid'}}"
                    catchtap="goToPay" 
                    data-id="{{item.id}}">
              去缴费
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{billList.length === 0}}">
      <text>暂无账单记录</text>
    </view>
  </view>
</view>


