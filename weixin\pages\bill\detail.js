const { billApi } = require('../../api/index.js');

Page({
  data: {
    billDetail: {},
    paymentRecords: [],
    loading: false
  },

  onLoad(options) {
    const billId = options.id;
    if (billId) {
      this.loadBillDetail(billId);
      this.loadPaymentRecords(billId);
    }
  },

  loadBillDetail(billId) {
    this.setData({ loading: true });
    wx.showLoading({
      title: '加载详情中...'
    });
    billApi.getBillDetail(billId).then(res => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.log('账单详情:', res);
      if (res) {
        this.setData({
          billDetail: {
            id: res.data.pid,
            heatingPeriod: res.data.period || '2024.11.15 - 2025.03.15',
            houseNumber: res.data.houseNumber,
            address: res.data.address,
            area: res.data.area,
            unitPrice: res.data.unitPrice,
            amount: res.data.amount,
            status: res.data.status === 'unpaid' ? 'pending' : 'paid',
            statusIcon: res.data.status === 'unpaid' ? '🔴' : '✅',
            statusText: res.data.statusText,
            createDate: res.data.createDate,
            dueDate: res.data.dueDate
          }
        });
      }
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取账单详情失败:', err);
      wx.showToast({
        title: err.message || '获取详情失败',
        icon: 'none'
      });
    });
  },

  loadPaymentRecords(billId) {
    console.log('开始加载缴费记录，账单ID:', billId);
    if (!billId) {
      console.error('账单ID为空');
      return;
    }
    wx.showLoading({
      title: '加载缴费记录...'
    });
    
    // 确保传递的是数字类型的billId
    const numericBillId = parseInt(billId);
    console.log('转换后的账单ID:', numericBillId);
    
    billApi.getPaymentList(numericBillId).then(res => {
      wx.hideLoading();
      console.log('缴费记录响应:', res);
      if (res && res.data && res.data.payments) {
        this.setData({
          paymentRecords: res.data.payments.map(payment => ({
            id: payment.id,
            amount: payment.amount,
            paymentMethod: payment.paymentMethodText,
            paymentTime: payment.paymentDate,
            status: 'success',
            statusText: '支付成功',
            statusIcon: '✅',
            orderNo: payment.transactionNo,
            period: payment.period,
            roomNo: payment.roomNo,
            heatYear: payment.heatYear
          }))
        });
      } else {
        console.log('没有缴费记录数据');
        this.setData({
          paymentRecords: []
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取缴费记录失败:', err);
      wx.showToast({
        title: err.message || '获取缴费记录失败',
        icon: 'none'
      });
    });
  },

  goBack() {
    wx.navigateBack();
  },

  goToPay() {
    wx.navigateTo({
      url: '/pages/payment/index?billId=' + this.data.billDetail.id
    });
  },

  viewPaymentDetail(e) {
    const paymentId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/payment/detail?id=' + paymentId
    });
  }
});



