<view class="container">
  <view class="header">
    <text class="title">完善个人信息</text>
    <text class="subtitle">请完善您的个人信息</text>
  </view>

  <view class="avatar-section">
    <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
      <image class="avatar" src="{{avatarUrl || '/images/default-avatar.png'}}"></image>
      <text class="avatar-tip">点击设置头像</text>
    </button>
  </view>

  <view class="form-section">
    <view class="form-item">
      <text class="label">昵称</text>
      <input 
        class="input" 
        type="nickname"
        placeholder="请输入昵称"
        value="{{nickName}}"
        bindinput="onNickNameInput"
      />
    </view>

    <button class="complete-btn" bindtap="handleComplete">完成</button>
  </view>
</view>
