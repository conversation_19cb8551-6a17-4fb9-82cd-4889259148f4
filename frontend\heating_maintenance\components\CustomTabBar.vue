<template>
  <view class="custom-tabbar">
    <view
      v-for="(item, index) in filteredTabList"
      :key="index"
      class="tab-item"
      :class="{ active: computedCurrentPath === item.pagePath }"
      @click="switchTab(item.pagePath)"
    >
      <view class="icon-container">
        <image
          :src="
            computedCurrentPath === item.pagePath ? item.selectedIconPath : item.iconPath
          "
          mode="aspectFit"
          class="tab-icon"
        ></image>
      </view>
      <text class="tab-text">{{ item.text }}</text>
      <view v-if="computedCurrentPath === item.pagePath" class="active-indicator"></view>
    </view>
  </view>
</template>

<script>
import { hasPermission } from "@/utils/auth.js";

export default {
  name: "CustomTabBar",
  data() {
    return {
      tabList: [
        {
          pagePath: "/pages/home/<USER>",
          text: "首页",
          iconPath: "/static/tab/home.png",
          selectedIconPath: "/static/tab/home-active.png",
        },
        {
          pagePath: "/pages/hes/list",
          text: "换热站",
          iconPath: "/static/tab/hes.png",
          selectedIconPath: "/static/tab/hes-active.png",
          permissionCode: "home:hes",
        },
        {
          pagePath: "/pages/message/center",
          text: "消息",
          iconPath: "/static/tab/notification.png",
          selectedIconPath: "/static/tab/notification-active.png",
        },
        {
          pagePath: "/pages/user/info",
          text: "我的",
          iconPath: "/static/tab/profile.png",
          selectedIconPath: "/static/tab/profile-active.png",
        },
      ],
    };
  },
  computed: {
    computedCurrentPath() {
      const pages = getCurrentPages();
      if (pages.length) {
        const currentPage = pages[pages.length - 1];
        return `/${currentPage.route}`;
      }
      return "";
    },
    filteredTabList() {
      return this.tabList.filter((item) => {
        return !item.permissionCode || hasPermission(item.permissionCode);
      });
    },
  },
  methods: {
    switchTab(path) {
      if (this.computedCurrentPath === path) return;
      uni.switchTab({
        url: path,
        fail: (err) => {
          console.error(`switchTab 失败: ${JSON.stringify(err)}`);
          uni.navigateTo({ url: path });
        },
      });
    },
  },
  mounted() {
    uni.hideTabBar();
  },
};
</script>

<style lang="scss">
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
  z-index: 999;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12rpx 0;
    position: relative;
    transition: all 0.3s;

    &.active {
      transform: translateY(-6rpx);

      .tab-text {
        color: #1890ff;
        font-weight: 500;
      }
    }

    .icon-container {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 6rpx;

      .tab-icon {
        width: 48rpx;
        height: 48rpx;
        transition: all 0.3s;
      }
    }

    .tab-text {
      font-size: 24rpx;
      color: #999;
      line-height: 1;
      transition: all 0.3s;
    }

    .active-indicator {
      position: absolute;
      bottom: -3rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 16rpx;
      height: 6rpx;
      background: #1890ff;
      border-radius: 6rpx;
    }
  }
}

page {
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}
</style>
