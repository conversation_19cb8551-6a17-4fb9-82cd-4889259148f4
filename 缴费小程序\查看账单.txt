### **小程序“查看账单”功能的实现逻辑**

当用户在小程序中点击【查看账单】时，系统会通过一系列后端查询和前端计算，为用户呈现一个清晰、准确的账单详情页面。整个流程无需代码，逻辑清晰。

---

### **一、 前端触发与用户验证**

1.  **用户操作**：
    *   用户登录 ，进入首页。
    *   点击【查看账单】按钮。

2.  **系统验证**：
    *   小程序前端检查用户是否已绑定户号。
    *   如果未绑定，则跳转至“绑定户号”页面，流程结束。
    *   如果已绑定，则获取当前用户的 `house_id`。

---


#### **第二步：查询核心账单数据（@backend）**

后端服务 `@backend` 根据 `house_id` 和当前供暖年度，查询以下数据：

1.  **查询本年度账单** (`t_bill`)：
    *   获取 `total_amount`, `paid_amount`, `due_date`, `status`, `remark` 等。
    *   如果 `t_bill` 记录不存在，说明本年度账单尚未生成。

2.  **查询停供申请** (`t_stop_supply_apply`)：
    *   查询该用户在当前供暖年度是否有状态为 `approved`（已批准）的停供申请。
    *   如果存在，获取 `stop_start_date`（停供生效日期）。

3.  **查询缴费记录** (`t_payment`)：
    *   获取所有关联此账单的缴费记录，用于展示支付历史。

4.  **查询欠费记录** (`t_overdue_records`)：
    *   查询是否存在 `status='active'` 的记录，以获取 `overdue_days` 和 `penalty_amount`。

---

#### **第三步：判断用户状态并生成最终账单视图**

系统根据“停供申请”和“账单”数据，判断用户当前所处的场景，并动态生成账单详情。

**场景1：正常供暖（无停供申请或申请未获批）**

*   **判断**：`t_stop_supply_apply.status != 'approved'`
*   **逻辑**：按标准账单流程处理。
*   **账单详情展示**：
    *   **应缴总金额**：`t_bill.total_amount`
    *   **已缴金额**：`t_bill.paid_amount`
    *   **剩余待缴金额**：`total_amount - paid_amount`
    *   **账单截止日**：`t_bill.due_date`
    *   **账单状态**：根据 `t_bill.status` 显示（如“未缴清”、“已缴清”）。
    *   **滞纳金**：如果 `t_overdue_records` 存在且 `status='active'`，则显示“⚠️ 已逾期X天，滞纳金：￥Y.YY”。
    *   **当前需支付**：`剩余待缴金额 + 滞纳金`（如果逾期）。
*   **操作按钮**：
    *   未缴清时：显示【在线缴费】。
    *   已缴清时：显示【开具电子发票】。

**场景2：在供暖开始前申请停供并获批**

*   **判断**：`t_stop_supply_apply.status = 'approved'` 且 `stop_start_date <= 供暖开始日期`。
*   **逻辑**：账单金额在生成时已按 `min_payment_rate` 折扣。
*   **账单详情展示**：
    *   **应缴总金额**：`t_bill.total_amount` （此金额已是打折后的金额，如750元）。
    *   **已缴金额**：`t_bill.paid_amount`
    *   **剩余待缴金额**：`total_amount - paid_amount`
    *   **账单截止日**：`t_bill.due_date`
    *   **账单状态**：根据 `t_bill.status` 显示。
    *   **备注**：在页面显眼位置添加提示：“您已申请本年度停供，按30%收取基础热损费”。
*   **操作按钮**：同场景1。

**场景3：在供暖开始后申请停供并获批（此前未缴费）**

*   **判断**：`t_stop_supply_apply.status = 'approved'` 且 `stop_start_date > 供暖开始日期`。
*   **逻辑**：原账单欠费记录已被核销，用户需缴纳“结算款”。
*   **账单详情展示**：
    *   **应缴总金额**：`t_bill.total_amount` （原始金额，如2500元，保持不变）。
    *   **已缴金额**：`t_bill.paid_amount` （仍为0.00元）。
    *   **剩余待缴金额**：`total_amount - paid_amount`
    *   **账单截止日**：`t_bill.due_date`
    *   **账单状态**：显示为“已调整”或“部分缴清”。
    *   **核心提示**：
        *   “✅ 您的停供申请已生效！”
        *   “说明：您只需缴纳 **971.02元**（47天供暖费）作为结算款，原滞纳金已免除。”
    *   **当前需支付**：`971.02元` （即最终结算金额）。
*   **操作按钮**：
    *   显示【立即结算】按钮，金额为971.02元。
计算逻辑详解
获取基础数据：
总金额 (total_amount)：从 t_bill 表获取，例如 2500.00元。
总供暖天数：从 t_heating_fee_rule 表获取 heating_start_date (如 2024-11-15) 和 heating_end_date (如 2025-03-15)，计算出总天数，例如 121天。
供暖开始日期 (heating_start)：2024-11-15。
停供生效日期 (stop_start_date)：用户申请的停供开始日，例如 2024-12-01。
计算“实际供暖天数”：
实际供暖天数 = 停供生效日期 - 供暖开始日期
例如：2024-12-01 - 2024-11-15 = 16天 （注意：日期计算通常不包含结束日，即11.15到12.01是16天）。
计算“按天折算的费用”：
每天费用 = 总金额 / 总供暖天数
例如：2500 / 121 ≈ 20.66元/天
按天折算费用 = 每天费用 * 实际供暖天数
例如：20.66 * 16 ≈ 330.56元
计算“最低基础热损费”：
从 t_heating_fee_rule 表获取 min_payment_rate，例如 0.3 (30%)。
最低基础费 = 总金额 * min_payment_rate
例如：2500 * 0.3 = 750.00元
确定“最终结算金额”：
原则：取“按天折算费用”和“最低基础费”中的较高者。
最终结算金额 = MAX(330.56, 750.00) = 750.00元
为什么取高者？这是为了保证供热公司的基本收入，防止用户“蹭暖”后只支付极低费用。
关于滞纳金：
在用户申请停供并获批的情况下，免除其在原账单逾期期间产生的滞纳金。这是对用户主动终止服务的一种鼓励。
总结：用户需支付 750.00 元

**场景4：在供暖开始后申请停供并获批（此前已全额缴费）**

*   **判断**：同场景3，且 `t_bill.paid_amount >= t_bill.total_amount`。
*   **逻辑**：用户多缴，已发起退费。
*   **账单详情展示**：
    *   **应缴总金额**：`t_bill.total_amount` （如2500元）。
    *   **已缴金额**：`t_bill.paid_amount` （如2500元）。
    *   **剩余待缴金额**：0.00元。
    *   **账单截止日**：`t_bill.due_date`
    *   **账单状态**：显示为“已缴清”。
    *   **核心提示**：
        *   “✅ 您的停供申请已生效！”
        *   “说明：因您已多缴费用，系统已为您发起退费申请，金额 **1528.98元**。”
        *   “状态：退费申请已提交，财务将在7个工作日内处理。”
*   **操作按钮**：
    *   显示【查看退费进度】按钮，可跳转至退费详情。
计算逻辑详解
获取基础数据：
已缴金额 (paid_amount)：从 t_bill 表获取，例如 2500.00元。
总金额 (total_amount)：2500.00元。
供暖开始日期 (heating_start)：2024-11-15。
停供生效日期 (stop_start_date)：2024-12-01。
计算“最终应收金额”：
此步骤与场景一的第3、4、5步完全相同。
计算出用户实际应该支付的金额。
例如：最终应收金额 = 750.00元 （与场景一的计算结果一致）。
计算“应退金额”：
应退金额 = 已缴金额 - 最终应收金额
例如：2500.00 - 750.00 = 1750.00元
---

### **四、 前端数据展示（@weixin）**

后端将处理好的数据通过API返回给小程序前端，前端进行渲染。

#### **场景1：在供暖开始前缴费（正常流程）**

*   **数据来源**：
    *   `total_amount = 2140.00`
    *   `paid_amount = 0.00`
    *   `due_date = 2024-10-31`
    *   `status = 'unpaid'`
    *   `overdue_records` 无有效记录。
*   **页面展示**：
    ```text
    应缴总金额：2140.00 元
    已缴金额：0.00 元
    剩余待缴金额：2140.00 元
    缴费截止日：2024-10-31
    账单状态：未缴清（unpaid）
    [在线缴费] 按钮
    ```

#### **场景2：在供暖开始后缴费（产生滞纳金）**

*   **数据来源**：
    *   `total_amount = 2140.00`
    *   `paid_amount = 0.00`
    *   `due_date = 2024-10-31`
    *   `status = 'overdue'`
    *   `overdue_records.penalty_amount = 16.05`
    *   `overdue_records.overdue_days = 15`
*   **页面展示**：
    ```text
    应缴总金额：2140.00 元
    已缴金额：0.00 元
    剩余待缴金额：2140.00 元
    缴费截止日：2024-10-31
    账单状态：已逾期15天（overdue）
    ⚠️ 滞纳金：16.05元
    ---------------------------------
    🔴 当前需支付：2156.05元
    [在线缴费] 按钮
    ```

> **关键点**：当用户点击【在线缴费】时，传递给支付接口的金额是 `2156.05元`，即 `total_amount + penalty_amount`。


特殊状态处理
已缴清状态：
账单状态 显示为“已缴清（paid）”。
剩余待缴金额 和 当前需支付 不再显示。
[在线缴费] 按钮隐藏。

[开具电子发票] 按钮变为可点击，用户可申请发票。

未缴费状态：
已缴金额 和 剩余待缴金额 与 应缴总金额 相同。
若已逾期，同样显示滞纳金和“当前需支付”金额。

3： 无当前账单
如果系统尚未为本年度生成账单，则显示：
┌────────────────────────────┐
|    2024-2025 供暖季账单     |
└────────────────────────────┘
⚠️ 尚未生成本年度账单
账单将在近期生成，请耐心等待。


---

### **五、 支付成功后的数据更新**

1.  **用户支付**：用户确认并完成微信支付。
2.  **系统回调**：
    *   微信支付平台向 `@backend` 发送支付成功通知。
    *   `@backend` 验证通知的合法性。
3.  **更新数据库**：
    *   在 `t_payment` 表中**新增**一条记录，`amount` 为 `2156.05`。
    *   更新 `t_bill` 表：`paid_amount = 2156.05`，`status = 'paid'`，`last_paid_date = TODAY`。
    *   更新 `t_overdue_records` 表：将对应记录的 `status` 从 `active` 更新为 `cleared`。
4.  **前端刷新**：
    *   支付成功页面跳转回“账单详情”。
    *   前端重新发起“查看账单”请求，获取最新数据。
    *   页面展示为“已缴清”状态，并提供【电子发票】入口。

---

### **总结**

“查看账单”的实现逻辑是一个**前后端协同**的过程：
1.  **前端**：触发请求，接收数据，渲染页面。
2.  **后端**：验证用户，查询 `t_bill`、`t_payment`、`t_overdue_records` 三张核心表，整合并计算数据。
3.  **数据库**：提供原始数据，`t_overdue_records` 表是滞纳金信息的唯一权威来源。

