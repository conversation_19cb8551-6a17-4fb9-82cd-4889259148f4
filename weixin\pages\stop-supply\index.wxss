.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx 40rpx;
  color: white;
  position: relative;
}

.header-actions {
  position: absolute;
  top: 60rpx;
  right: 30rpx;
}

.record-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  font-size: 24rpx;
  color: white;
  opacity: 0.9;
}

.record-btn .iconfont {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  display: block;
}

.card {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.house-info {
  padding: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-card {
  margin-bottom: 40rpx;
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-right: 8rpx;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
}

.picker-input.selected {
  color: #333;
  border-color: #007aff;
}

.picker-input.placeholder {
  color: #999;
}

.picker-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.textarea-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
  background: white;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 60rpx;
  background-color: #ff4757;
  color: white;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.submit-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
}




