package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.entity.fault.TFaultAttachment;

import java.util.List;
import java.util.Map;

@Repository
public interface FaultAttachmentRepository extends JpaRepository<TFaultAttachment, Long> {
    List<TFaultAttachment> findByFaultId(long faultId);

    /**
     * 根据故障ID获取附件列表（返回Map格式）
     */
    @Query(value = "SELECT " +
            "fa.id as attachment_id, " +
            "fa.file_type, " +
            "fa.file_path, " +
            "DATE_FORMAT(fa.created_at, '%Y-%m-%d %H:%i') as created_time " +
            "FROM t_fault_attachment fa " +
            "WHERE fa.fault_id = :faultId " +
            "ORDER BY fa.created_at DESC", nativeQuery = true)
    List<Map<String, Object>> findAttachmentsByFaultId(@Param("faultId") Long faultId);
}