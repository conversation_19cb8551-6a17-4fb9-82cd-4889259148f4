<view class="container">
  <!-- 账单信息卡片 -->
  <view class="bill-card">
    <view class="bill-header">
      <text class="bill-title">账单信息</text>
    </view>

    <view class="bill-info">
      <view class="info-row">
        <text class="label">供暖年度</text>
        <text class="value">{{billInfo.heatingYear}}-{{billInfo.heatingYear + 1}}</text>
      </view>
      <view class="info-row">
        <text class="label">户号</text>
        <text class="value">{{billInfo.houseNumber}}</text>
      </view>

      <view class="info-row">
        <text class="label">地址</text>
        <text class="value">{{billInfo.address}}</text>
      </view>

      <view class="info-row">
        <text class="label">面积</text>
        <text class="value">{{billInfo.area}}㎡</text>
      </view>

      <view class="info-row">
        <text class="label">单价</text>
        <text class="value">¥{{billInfo.unitPrice}}/㎡</text>
      </view>

      <view class="info-row">
        <text class="label">供暖状态</text>
        <text class="value">{{billInfo.heatingStatusText}}</text>
      </view>
    </view>
  </view>

  <!-- 缴费类型选择 -->
  <view class="fee-type-section">
    <view class="section-title">选择缴费类型</view>

    <view class="fee-type-option {{selectedFeeType === 'heating' ? 'selected' : ''}}"
          bindtap="selectFeeType" data-type="heating">
      <view class="option-left">
        <view class="radio {{selectedFeeType === 'heating' ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectedFeeType === 'heating'}}">✓</text>
        </view>
        <text class="fee-type-name">用热缴费（全额）</text>
      </view>
      <view class="fee-type-amount">¥{{billInfo.totalPayableAmount || billInfo.heatingFee}}</view>
    </view>

    <view class="fee-type-option {{selectedFeeType === 'maintenance' ? 'selected' : ''}}"
          bindtap="selectFeeType" data-type="maintenance">
      <view class="option-left">
        <view class="radio {{selectedFeeType === 'maintenance' ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectedFeeType === 'maintenance'}}">✓</text>
        </view>
        <text class="fee-type-name">不用热（管网维护费）</text>
      </view>
      <view class="fee-type-amount">¥{{billInfo.maintenanceFee || '0.00'}}</view>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-methods">
    <view class="section-title">选择支付方式</view>
    
    <view class="payment-option {{selectedPayment === 'wechat' ? 'selected' : ''}}" 
          bindtap="selectPayment" data-type="wechat">
      <view class="option-left">
        <view class="radio {{selectedPayment === 'wechat' ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectedPayment === 'wechat'}}">✓</text>
        </view>
        <image class="payment-icon" src="/images/wechat-pay.png"></image>
        <text class="payment-name">微信支付</text>
      </view>
      <text class="payment-desc">推荐使用，安全快捷</text>
    </view>
    
    <view class="payment-option {{selectedPayment === 'alipay' ? 'selected' : ''}}" 
          bindtap="selectPayment" data-type="alipay">
      <view class="option-left">
        <view class="radio {{selectedPayment === 'alipay' ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectedPayment === 'alipay'}}">✓</text>
        </view>
        <image class="payment-icon" src="/images/alipay.png"></image>
        <text class="payment-name">支付宝</text>
      </view>
      <text class="payment-desc">支持花呗、余额等</text>
    </view>
  </view>

  <!-- 协议同意 -->
  <view class="agreement-section">
    <view class="agreement-checkbox" bindtap="toggleAgreement">
      <view class="checkbox {{isAgreed ? 'checked' : ''}}">
        <text class="check-icon" wx:if="{{isAgreed}}">✓</text>
      </view>
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="viewAgreement">《供热缴费协议》</text>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="pay-button-container">
    <view class="payment-summary">
      <text class="summary-label">应付金额：</text>
      <text class="summary-amount">
        ¥{{selectedFeeType === 'heating' ? (billInfo.totalPayableAmount || billInfo.heatingFee) : (billInfo.maintenanceFee || '0.00')}}
      </text>
    </view>
    <button class="pay-button {{!isAgreed ? 'disabled' : ''}}"
            bindtap="handlePayment"
            disabled="{{!isAgreed}}">
      立即支付
    </button>
  </view>
</view>