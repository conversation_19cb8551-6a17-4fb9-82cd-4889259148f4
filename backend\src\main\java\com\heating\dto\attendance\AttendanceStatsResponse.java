package com.heating.dto.attendance;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class AttendanceStatsResponse {
    private Map<String, Object> summary;
    private ChartData chart;
    
    @Data
    public static class ChartData {
        private List<String> xAxis;
        private List<Series> series;
    }
    
    @Data
    public static class Series {
        private String name;
        private List<Double> data;
    }
} 