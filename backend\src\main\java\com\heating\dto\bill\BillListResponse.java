package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class BillListResponse {
    private BillSummary summary;
    private List<BillDetail> bills;
    private Integer total;
    private Integer page;
    private Integer pageSize;

    @Data
    public static class BillSummary {
        private BigDecimal totalUnpaid = BigDecimal.ZERO;
        private BigDecimal totalPaid = BigDecimal.ZERO;
        private Integer unpaidCount = 0;
        private Integer paidCount = 0;
    }

    @Data
    public static class BillDetail {
        private Long id;
        private String period; // 供暖期间，如"2024-2025 供暖季"
        private String houseNumber;
        private String houseName;
        private String address;
        private String area;
        private String unitPrice;
        private String amount;
        private String paidAmount;
        private String status;
        private String statusIcon;
        private String statusText;
        private String createDate;
        private String dueDate;
        private String lastPaidDate;
        private String remark;
        private Integer heatYear;
        private Integer isHeating;
    }
}