package com.heating.service.impl;

import com.heating.entity.system.DictData;
import com.heating.repository.DictDataRepository;
import com.heating.service.DictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典数据服务实现类
 */
@Service
public class DictDataServiceImpl implements DictDataService {

    @Autowired
    private DictDataRepository dictDataRepository;

    @Override
    public List<DictData> getDictDataByDictId(Integer dictId) {
        return dictDataRepository.findByDictId(dictId);
    }
} 