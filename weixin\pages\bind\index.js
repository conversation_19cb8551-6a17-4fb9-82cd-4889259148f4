const { bindApi } = require('../../api/index.js');

Page({
  data: {
    houseNumber: '',
    statusBarHeight: 0,
    navBarHeight: 0
  },

  onLoad() {
    // 获取系统信息，设置自定义导航栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 44
    });

    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.username) {
      wx.showModal({
        title: '提示',
        content: '用户信息异常，请重新登录',
        showCancel: false,
        success: () => {
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      });
    }
  },

  // 显示绑定提示
  showBindingTip() {
    wx.showModal({
      title: '温馨提示',
      content: '请先完成户号绑定才能使用系统功能。如需帮助，请联系客服。',
      showCancel: true,
      cancelText: '联系客服',
      confirmText: '继续绑定',
      success: (res) => {
        if (res.cancel) {
          this.contactService();
        }
      }
    });
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00\n\n或者您可以尝试重新登录',
      showCancel: true,
      cancelText: '重新登录',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        } else if (res.cancel) {
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  onHouseNumberInput(e) {
    this.setData({
      houseNumber: e.detail.value.toUpperCase()
    });
  },

  handleBind() {
    const { houseNumber } = this.data;

    if (!houseNumber.trim()) {
      wx.showToast({
        title: '请输入户号',
        icon: 'none'
      });
      return;
    }

    if (!/^HT\d{10}$/.test(houseNumber)) {
      wx.showToast({
        title: '户号格式不正确',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '绑定中...'
    });

    // 使用封装的API
    bindApi.bindHouse({ houseNumber })
      .then(res => {
        console.log('绑定响应:', res);
        
        if (res.code === 200) {
          // 更新本地用户信息，根据后端返回的数据结构
          const updatedUserInfo = wx.getStorageSync('userInfo') || {};
          const responseData = res.data;
          
          // 更新用户信息
          updatedUserInfo.houseNumber = houseNumber;
          
          // 根据后端WeixinController返回的数据结构更新
          if (responseData) {
            updatedUserInfo.id = responseData.id;
            updatedUserInfo.houseId = responseData.houseId;
            updatedUserInfo.address = responseData.address;
            updatedUserInfo.area = responseData.area;
            updatedUserInfo.heatingStatus = responseData.heatingStatus;
            updatedUserInfo.heatingStatusText = responseData.heatingStatusText;
            updatedUserInfo.name = responseData.name;
            updatedUserInfo.phone = responseData.phone;
          }
          
          wx.setStorageSync('userInfo', updatedUserInfo);

          wx.showToast({
            title: '绑定成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                // 绑定成功后跳转到主页
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }, 2000);
            }
          });
        } else {
          wx.showToast({
            title: res.message || '绑定失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('绑定失败:', err);
        wx.showToast({
          title: err.message || '绑定失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  }
});

