package com.heating.dto.user;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

public record LoginRequest(
    @NotEmpty(message = "账号不能为空")
    String username,
    @NotEmpty(message = "密码不能为空")
    String password,
    String code,
    String loginType,
    UserInfo userInfo
) {
    public LoginRequest {
        if (username != null) {
            username = username.trim();
        }
        if (password != null) {
            password = password.trim();
        }
    }

    @Data
    public static class UserInfo {
        private String nickName;
        private String avatar;
        private Integer gender;
    } 
} 