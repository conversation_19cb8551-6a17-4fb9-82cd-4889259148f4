package com.heating.controller;

import com.heating.dto.ApiResponse;
import com.heating.dto.MaterialsInfoDTO;
import com.heating.service.MaterialsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 材料信息控制器
 */
@RestController
@RequestMapping("/api/materials")
public class MaterialsController {

    @Autowired
    private MaterialsInfoService materialsInfoService;

    /**
     * 获取材料列表
     * @return 材料列表数据
     */
    @GetMapping("/list")
    public ApiResponse<List<MaterialsInfoDTO>> getMaterialsList() {
        List<MaterialsInfoDTO> materials = materialsInfoService.getAllMaterials();
        return ApiResponse.success("材料列表查询成功",materials);
    }
} 