package com.heating.entity.bill;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_bill")
public class TBill {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "house_id", nullable = false)
    private Long houseId;

    @Column(name = "heat_year", nullable = false)
    private Integer heatYear;

    @Column(name = "heat_fee_rule_id", nullable = false)
    private Integer heatFeeRuleId;


    @Column(name = "is_heating", nullable = false)
    private Integer isHeating = 1;

    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "paid_amount", precision = 10, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;


    @Column(name = "overdue_amount", precision = 10, scale = 2)
    private BigDecimal overdueAmount = BigDecimal.ZERO;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private BillStatus status = BillStatus.unpaid;

    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate;

    @Column(name = "last_paid_date")
    private LocalDate lastPaidDate;

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public enum BillStatus {
        unpaid, partial_paid, paid, overdue
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}