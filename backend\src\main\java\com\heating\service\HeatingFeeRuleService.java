package com.heating.service;

import com.heating.dto.bill.HeatingFeeRuleResponse;
import java.math.BigDecimal;

public interface HeatingFeeRuleService {
    
    /**
     * 根据ID获取供暖计费规则详情
     * @param id 规则ID
     * @return 供暖计费规则详情
     */
    HeatingFeeRuleResponse getHeatingFeeRuleById(Long id);
    
    /**
     * 根据规则ID获取单价
     * @param ruleId 规则ID
     * @return 单价
     */
    BigDecimal getUnitPriceByRuleId(Long ruleId);
    
    /**
     * 获取当前生效的供暖计费规则
     * @return 当前生效的规则
     */
    HeatingFeeRuleResponse getCurrentActiveRule();
}