package com.heating.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Web MVC 配置类，用于配置静态资源访问
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);
    
    @Value("${file.uploadFolder}")
    private String uploadPath;
    
    @Value("${file.accessPath}")
    private String urlPrefix;
    
    @Value("${file.staticAccessPath}")
    private String staticAccessPath;
    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件的访问路径
        String resourceLocation = "file:" + uploadPath;
        
        // 确保路径以 / 结尾
        if (!resourceLocation.endsWith("/")) {
            resourceLocation += "/";
        }
        
        // 使用更具体的路径模式，避免拦截所有请求
        String resourcePattern = urlPrefix + "/**";
        
        logger.info("配置静态资源映射: {} -> {}", resourcePattern, resourceLocation);
        
        registry.addResourceHandler(resourcePattern)
                .addResourceLocations(resourceLocation)
                .setCachePeriod(3600) // 缓存1小时
                .resourceChain(true); // 启用资源链优化
    }
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 允许静态资源跨域访问
        registry.addMapping(urlPrefix + "/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "HEAD", "OPTIONS")
                .allowCredentials(false)
                .maxAge(3600);
        
        logger.info("配置静态资源跨域访问: {}", urlPrefix + "/**");
    }
} 