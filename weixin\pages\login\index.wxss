.container {
  min-height: 100vh;
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.logo-box {
  margin-top: 120rpx;
  margin-bottom: 80rpx;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  border-radius: 50%;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
  display: block;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 4rpx;
  position: relative;
  text-align: center;
  width: 100%;
}

.app-name::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  border-radius: 2rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20rpx;
  letter-spacing: 2rpx;
  font-weight: 300;
  text-align: center;
  width: 100%;
}

/* 微信登录区域样式 */
.wx-login-section {
  width: 100%;
  margin-bottom: 40rpx;
}

.wx-login-btn {
  width: 100%;
  height: 100rpx;
  background: #07c160;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  border: none;
  margin: 0;
  padding: 0;
}

.wx-login-btn::after {
  border: none;
}

.wx-login-btn .wx-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.wx-login-btn text {
  color: #fff;
  font-size: 32rpx;
}

/* 分割线样式 */
.divider {
  width: 100%;
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: #e5e5e5;
}

.divider text {
  background: #fff;
  padding: 0 30rpx;
  color: #999;
  font-size: 28rpx;
  position: relative;
  z-index: 1;
}

.form-box {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.form-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  background: #f8f9fa;
  border-radius: 50rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-item:focus-within {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.form-item .wx-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.remember-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0 40rpx;
  padding: 0 20rpx;
}

.remember-pwd {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #4CAF50;
  border-color: #4CAF50;
}

.checkbox .wx-icon {
  font-size: 24rpx;
  color: #fff;
}

.remember-pwd text,
.forget-pwd {
  font-size: 24rpx;
  color: #999;
}

.forget-pwd {
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  line-height: 80rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 5rpx 15rpx rgba(102, 126, 234, 0.4);
}

.register-link {
  margin-top: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
}

.register-link .link {
  color: #4CAF50;
  margin-left: 10rpx;
}

.footer {
  position: fixed;
  bottom: 60rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.footer .link {
  color: #4CAF50;
  display: inline;
} 
