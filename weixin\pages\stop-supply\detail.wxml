<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">申请详情</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{!recordDetail.id && loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 详情内容 -->
  <block wx:if="{{recordDetail.id}}">
    <!-- 状态卡片 -->
    <view class="status-card">
      <view class="status-icon {{recordDetail.status}}">
        <text>{{recordDetail.statusIcon}}</text>
      </view>
      <view class="status-info">
        <text class="status-title">{{recordDetail.statusText}}</text>
        <text class="status-desc">{{recordDetail.statusDesc}}</text>
      </view>
    </view>

    <!-- 申请信息 -->
    <view class="info-card">
      <view class="card-title">申请信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">申请日期</text>
          <text class="info-value">{{recordDetail.applyDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">停供开始</text>
          <text class="info-value">{{recordDetail.stopStartDate}}</text>
        </view>
        <view class="info-item" wx:if="{{recordDetail.stopEndDate}}">
          <text class="info-label">停供结束</text>
          <text class="info-value">{{recordDetail.stopEndDate}}</text>
        </view>
        <view class="info-item" wx:else>
          <text class="info-label">停供类型</text>
          <text class="info-value">长期停供</text>
        </view>
        <view class="info-item">
          <text class="info-label">供暖年度</text>
          <text class="info-value">{{recordDetail.heatingYear}}-{{recordDetail.heatingYear + 1}}供暖季</text>
        </view>
      </view>
    </view>

    <!-- 申请原因 -->
    <view class="info-card">
      <view class="card-title">申请原因</view>
      <view class="reason-content">
        <text>{{recordDetail.reason || '无'}}</text>
      </view>
    </view>

    <!-- 审批信息 -->
    <view class="info-card" wx:if="{{recordDetail.approvedAt || recordDetail.status !== 'pending'}}">
      <view class="card-title">审批信息</view>
      <view class="info-list">
        <view class="info-item" wx:if="{{recordDetail.approvedAt}}">
          <text class="info-label">审批时间</text>
          <text class="info-value">{{recordDetail.approvedAt}}</text>
        </view>
        <view class="info-item" wx:if="{{recordDetail.approvedBy}}">
          <text class="info-label">审批人</text>
          <text class="info-value">管理员</text>
        </view>
        <view class="info-item" wx:if="{{recordDetail.status === 'rejected'}}">
          <text class="info-label">拒绝原因</text>
          <text class="info-value">{{recordDetail.rejectReason || '未提供'}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" wx:if="{{recordDetail.status === 'pending'}}">
      <button class="action-btn cancel-btn" bindtap="cancelApply">
        取消申请
      </button>
    </view>
  </block>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!recordDetail.id && !loading}}">
    <view class="empty-icon">📋</view>
    <text class="empty-title">申请不存在</text>
    <text class="empty-desc">该申请记录可能已被删除或不存在</text>
  </view>
</view>
