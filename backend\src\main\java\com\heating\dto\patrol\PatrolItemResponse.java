package com.heating.dto.patrol;

import lombok.Data;
import java.util.List;

/**
 * 巡检项目响应DTO
 */
@Data
public class PatrolItemResponse {
    /**
     * 巡检项目ID
     */
    private Long id;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 巡检项目
     */
    private Long itemDictionaryId;

    /** 
     * 巡检项目编码
     */
    private String itemCode;

    /**
     * 巡检项目名称
     */
    private String itemName; 

    /**
     * 所属类别名称
     */
    private String categoryName;

    /**
     * 参数类型
     */
    private String paramType;

    /**
     * 单位
    */
    private String unit; 

    /**
     * 检查方法
    */
    private String checkMethod; 

    /** 
     * 重要性
    */
    private String importance;

    /**
     * 描述说明
    */
    private String description;
     
} 