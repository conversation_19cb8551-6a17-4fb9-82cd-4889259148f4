/* 页面容器 */
.page-container {
  height: 100vh;
  background: #f5f5f5;
}

.container {
  padding: 20rpx;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
  min-height: calc(100vh - 140rpx);
  box-sizing: border-box;
}

/* 操作按钮 - 固定在底部 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  line-height: 60rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  line-height: 60rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}

/* 详情卡片 */
.detail-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.detail-header {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.bill-status {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

.detail-content {
  padding: 40rpx 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 30rpx;
  color: #666;
  font-weight: 400;
}

.item-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.amount-item .item-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.amount-item .amount {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b35;
}

.divider {
  height: 1rpx;
  background: #eee;
  margin: 20rpx 0;
}

.late-fee-notice {
  background: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 30rpx;
  display: flex;
  align-items: center;
}

.notice-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.4;
}

/* 缴费记录卡片 */
.payment-records-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.records-header {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.records-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.records-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.records-content {
  padding: 30rpx;
  max-height: none; /* 移除高度限制 */
}

.payment-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #52c41a;
  transition: all 0.3s ease;
}

.payment-item:last-child {
  margin-bottom: 0;
}

.payment-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.payment-status {
  display: flex;
  align-items: center;
}

.payment-status .status-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.payment-status .status-text {
  font-size: 28rpx;
  font-weight: 500;
}

.payment-status .status-text.success {
  color: #52c41a;
}

.payment-status .status-text.failed {
  color: #ff4d4f;
}

.payment-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #52c41a;
}

.payment-info {
  border-top: 1rpx solid #e8e8e8;
  padding-top: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
  min-height: 40rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  width: 180rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.empty-records {
  padding: 80rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}


