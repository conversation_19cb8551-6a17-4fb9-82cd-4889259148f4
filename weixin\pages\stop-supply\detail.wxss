/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx 40rpx;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
}

/* 状态卡片 */
.status-card {
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.status-icon.pending {
  background: linear-gradient(135deg, #ff9500, #ff7300);
  color: white;
}

.status-icon.approved {
  background: linear-gradient(135deg, #34c759, #28a745);
  color: white;
}

.status-icon.rejected {
  background: linear-gradient(135deg, #ff3b30, #dc3545);
  color: white;
}

.status-icon.canceled {
  background: linear-gradient(135deg, #8e8e93, #6c757d);
  color: white;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

/* 信息卡片 */
.info-card {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2rpx;
}

/* 信息列表 */
.info-list {
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  font-size: 30rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

/* 申请原因内容 */
.reason-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  border-left: 6rpx solid #667eea;
}

.reason-content text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 操作按钮区域 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  text-align: center;
}

.cancel-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.cancel-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .status-card {
    margin: 20rpx;
    padding: 30rpx 20rpx;
  }
  
  .status-icon {
    width: 100rpx;
    height: 100rpx;
    font-size: 50rpx;
    margin-right: 20rpx;
  }
  
  .info-card {
    margin: 15rpx 20rpx;
    padding: 30rpx 20rpx;
  }
  
  .info-label {
    width: 140rpx;
    font-size: 28rpx;
  }
  
  .info-value {
    font-size: 28rpx;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 60rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}
