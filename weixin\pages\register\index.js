Page({
  data: {
    phone: '',
    username: '',
    password: '',
    confirmPassword: '',
    name: ''
  },

  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  handleRegister() {
    const { phone, username, password, confirmPassword, name } = this.data;

    // 表单验证
    if (!phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    if (username.length < 3) {
      wx.showToast({
        title: '用户名至少3位字符',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码至少6位字符',
        icon: 'none'
      });
      return;
    }

    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none'
      });
      return;
    }

    if (!name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '注册中...'
    });

    // 发送注册请求
    wx.request({
      url: 'http://127.0.0.1:8889/api/weixin/register',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        phone: phone,
        username: username,
        password: password,
        name: name
      },
      success: (res) => {
        console.log('注册完整响应:', res);
        
        if (res.statusCode === 200 && res.data.code === 200) {
          wx.showToast({
            title: '注册成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        } else {
          console.error('注册失败:', res);
          wx.showToast({
            title: res.data?.message || '注册失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('注册请求失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  goToLogin() {
    wx.navigateBack();
  }
});





