Page({
  data: {
    userInfo: {
      avatar: '',
      name: '张三',
      role: '维修人员'
    }
  },

  onLoad() {
    this.loadUserInfo();
   // this.getImages();
  },

  getImages: function() {
    var that = this;
    wx.request({
      url: 'http://localhost:5000/api/images', 
      method: 'GET',
      success: function(res) {
        that.setData({
          imageList: res.data
        });
      },
      fail: function(error) {
        console.error(error);
        wx.showToast({
          title: '获取图片失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  chooseImage: function() {
    var that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        that.uploadImage(res.tempFilePaths[0]);
      }
    })
  },

  uploadImage: function(filePath) {
    var that = this;
    wx.uploadFile({
      url: 'http://your_backend_url/upload',  // 替换为你的后端上传接口
      filePath: filePath,
      name: 'image',
      success: function(res) {
        var data = JSON.parse(res.data);
        wx.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 2000
        });
        that.getImages();  // 重新获取图片列表
      },
      fail: function(error) {
        console.error(error);
        wx.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }, 

  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    console.log(userInfo);

    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },

  goToMyFaults() {
    wx.navigateTo({
      url: '/pages/fault/history/index?type=my'
    });
  },

  goToMyBills() {
    wx.navigateTo({
      url: '/pages/bill/index'
    });
  },

  goToMyTemps() {
    wx.navigateTo({
      url: '/pages/temperature/list/index?type=my'
    });
  },

  contactService() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '在线客服', '意见反馈'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.callService();
            break;
          case 1:
            this.openOnlineService();
            break;
          case 2:
            this.goToFeedback();
            break;
        }
      }
    });
  },

  callService() {
    wx.makePhoneCall({
      phoneNumber: '************',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  openOnlineService() {
    wx.showToast({
      title: '正在连接客服...',
      icon: 'loading',
      duration: 2000
    });
    // 这里可以跳转到在线客服页面
    // wx.navigateTo({
    //   url: '/pages/service/chat'
    // });
  },

  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    });
  },

  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  },

  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确认退出登录？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...'
          });
          
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '已退出登录',
              icon: 'success',
              duration: 2000,
              success: () => {
                // 清除本地存储的用户信息
                wx.clearStorageSync();
                // 重置用户信息
                this.setData({
                  userInfo: {
                    avatar: '',
                    name: '',
                    role: ''
                  }
                });
                
                // 跳转到登录页面
                setTimeout(() => {
                  wx.reLaunch({
                    url: '/pages/login/index'
                  });
                }, 1000);
              }
            });
          }, 1000);
        }
      }
    });
  }
}); 
