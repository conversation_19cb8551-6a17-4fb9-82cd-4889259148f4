package com.heating.dto.patrol;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 巡检结果详情完整响应，包含记录信息、结果列表和统计数据
 * 严格按照接口设计文档的响应结构
 */
@Data
public class PatrolResultDetailFullResponse {
    
    /**
     * 巡检记录基本信息
     */
    private RecordInfo recordInfo;
    
    /**
     * 巡检结果列表
     */
    private List<PatrolResultDetailResponse> resultList;
    
    /**
     * 巡检统计信息
     */
    private Summary summary;
    
    /**
     * 巡检计划信息
     */
    private Map<String, Object> planInfo;
    
    /**
     * 记录信息内部类
     */
    @Data
    public static class RecordInfo {
        private Long id;
        private Integer patrolPlanId;
        private String planName;
        private String executionDate;
        private String startTime;
        private String endTime;
        private String status;
        private Long executorId;
        private String executorName;
        private String executorPhone;
        private String executorAvatar;
        private Integer isLateEntry;
        private String remark;
        private String locations;
        private String createTime;
        private String updateTime;
    }
    
    /**
     * 统计信息内部类
     */
    @Data
    public static class Summary {
        private Integer totalItems;
        private Integer normalCount;
        private Integer abnormalCount;
        private String completionRate;
        private String duration;
    }
} 