const API_BASE = 'http://127.0.0.1:5000';

Page({
  data: {
    // 小区相关
    communities: [],          // 所有小区列表
    selectedCommunity: null,  // 选中的小区

    // 换热站相关
    stations: [],            // 换热站列表
    selectedStation: null,   // 选中的换热站

    // 房屋信息
    building: '',           // 楼栋号
    unit: '',              // 单元号
    room: '',              // 户号

    // 温度信息
    indoorTemp: '',        // 室内温度
    outdoorTemp: '',       // 室外温度
    updateTime: '',        // 室外温度更新时间

    // 图片相关
    images: '',        // 单张图像
    maxImages: 1,         // 最大图片数量
    video_Url: '',        // 视频地址

    // 位置信息
    location: null,       // 经纬度信息

    // 表单状态
    submitting: false     // 提交状态
  },

  onLoad() {
    console.log('页面加载');
    this.loadCommunities();
    this.loadOutdoorTemp();
    this.getLocation();
  },

  // 加载室外温度
  loadOutdoorTemp() {
    wx.request({
      url: `${API_BASE}/api/temperature/outdoor`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data.code === 0) {
          const { temperature, update_time } = res.data.data;
          this.setData({
            outdoorTemp: temperature.toFixed(1),
            updateTime: update_time
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取室外温度失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载小区列表
  loadCommunities() {
    wx.request({
      url: `${API_BASE}/api/useheatunits/list`,
      method: 'GET',
      success: (res) => {
        console.log('小区数据返回:', res);
        if (res.data.success) {
          const communities = res.data.data;
          console.log('处理后的小区数据:', communities);
          this.setData({
            communities: communities
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取小区列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '获取小区列表失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择小区
  onCommunitySelect(e) {
    console.log('选择小区:', e);
    const index = e.detail.value;
    const community = this.data.communities[index];
    console.log('选中的小区:', community);
    
    if (community) {
      this.setData({
        selectedCommunity: community,
        selectedStation: null,
        stations: []
      }, () => { 
      });
    }
  },
 
  // 房屋信息输入处理
  onBuildingInput(e) {
    this.setData({
      building: e.detail.value.replace(/[^\d]/g, '')
    });
  },

  onUnitInput(e) {
    this.setData({
      unit: e.detail.value.replace(/[^\d]/g, '')
    });
  },

  onRoomInput(e) {
    this.setData({
      room: e.detail.value.replace(/[^\d]/g, '')
    });
  },

  // 获取当前位置
  getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res;
        // 根据坐标获取地址信息
        wx.reverseGeocoder({
          location: {
            latitude,
            longitude
          },
          success: (res) => {
            this.setData({
              location: {
                name: res.result.address,
                address: res.result.formatted_addresses.recommend,
                latitude,
                longitude
              }
            });
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '请开启位置权限',
          icon: 'none'
        });
      }
    });
  },

  // 选择位置
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          location: {
            name: res.name,
            address: res.address,
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
      }
    });
  },

  // 温度输入
  onTempInput(e) {
    this.setData({
      temperature: e.detail.value
    });
  },

  // 拍照上传
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.setData({
          images: res.tempFilePaths[0]
        });
      }
    });
  },

  // 从相册选择
  chooseFromAlbum() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功：', res.tempFilePaths[0]);
        this.setData({
          images: res.tempFilePaths[0]
        });
      },
      fail: (err) => {
        console.error('选择图片失败：', err);
      }
    });
  },

  // 删除图片
  deleteImage() {
    this.setData({
      images: ''
    });
  },

  // 预览图片
  previewImage(e) {
    if (!this.data.images) return; 
    wx.previewImage({
      current: this.data.images,
      urls: [this.data.images]
    });
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 表单验证
  validateForm() {
    if (!this.data.selectedCommunity) {
      wx.showToast({ title: '请选择小区', icon: 'none' });
      return false;
    } 

    if (!this.data.building || !this.data.unit || !this.data.room) {
      wx.showToast({ title: '请填写完整的房屋信息', icon: 'none' });
      return false;
    }

    const temp = parseFloat(this.data.indoorTemp);
    if (isNaN(temp) || temp < -50 || temp > 50) {
      wx.showToast({ title: '请输入有效的室内温度', icon: 'none' });
      return false;
    }

    if (this.data.images.length === 0) {
      wx.showToast({ title: '请至少上传一张照片', icon: 'none' });
      return false;
    }
    
    if (!this.data.location) {
      this.setData({
        location: {
          latitude: 0,
          longitude: 0
        }
      });
      wx.showToast({ 
        title: '未能获取位置信息，将使用默认位置',
        icon: 'none'
      });
    }

    return true;
  },
 
  // 提交表单
  handleSubmit() {
    if (!this.validateForm()) return; 
    if (this.data.submitting) return; 
     
      this.setData({ submitting: true });
      wx.showLoading({ title: '提交中...' });  

      // 提交数据
      const formData = {
        community_name: String(this.data.selectedCommunity.name),
        building_no: String(this.data.building),
        unit_no: String(this.data.unit),
        room_no: String(this.data.room),
        indoor_temp: Number(this.data.indoorTemp), // 确保是数字
        outdoor_temp: Number(this.data.outdoorTemp), 
        latitude: Number(this.data.location.latitude),
        longitude: Number(this.data.location.longitude),
        image_url: this.data.images,
        video_url: this.data.video_Url
      };
      
      console.log('formData:', formData);

      wx.request({
        url: `${API_BASE}/api/temperature/report`,
        method: 'POST',
        data: formData,
        header: {
          'content-type': 'application/json' // 明确指定 content-type
        },
        success: (res) => {
          if (res.data.success) {
            wx.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 2000
            });
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          } else {
            wx.showToast({
              title: res.data.message || '提交失败',
              icon: 'none'
            });
          }
        },
        fail(err) {
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        },
        complete() {
          wx.hideLoading();
        }
      }); 
  },
 

  // 室内温度输入和验证
  onIndoorTempInput(e) {
    let value = e.detail.value;
    // 只允许输入数字和小数点
    value = value.replace(/[^\d.]/g, '');
    
    // 处理小数点
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 1) {
        value = `${parts[0]}.${parts[1].slice(0, 1)}`;
      }
    }

    // 验证温度范围
    const temp = parseFloat(value);
    if (!isNaN(temp)) {
      if (temp < -50 || temp > 50) {
        wx.showToast({
          title: '温度范围应在-50℃至50℃之间',
          icon: 'none'
        });
      }
    }

    this.setData({ indoorTemp: value });
  },

  // 选择图片
  chooseImage() {
    const remaining = this.data.maxImages - this.data.images.length;
    if (remaining <= 0) {
      wx.showToast({
        title: `最多上传${this.data.maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remaining,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          images: [...this.data.images, ...res.tempFilePaths]
        });
      }
    });
  },

  // 获取位置信息
  getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          location: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败，请开启位置权限',
          icon: 'none'
        });
      }
    });
  }
}); 