<view class="container">
  <!-- 基本信息卡片 -->
  <view class="info-card">
    <view class="header">
      <text class="title">基本信息</text>
      <text class="time">{{detail.report_time}}</text>
    </view>
    <view class="info-list">
      <view class="info-item">
        <text class="label">小区名称</text>
        <text class="value">{{detail.community_name}}</text>
      </view>
      <view class="info-item">
        <text class="label">房屋位置</text>
        <text class="value">{{detail.building_no}}栋{{detail.unit_no}}单元{{detail.room_no}}室</text>
      </view>
      <view class="info-item">
        <text class="label">室内温度</text>
        <text class="value temp {{detail.status}}">{{detail.indoor_temp}}°C</text>
      </view>
      <view class="info-item">
        <text class="label">室外温度</text>
        <text class="value">{{detail.outdoor_temp}}°C</text>
      </view>
      <view class="info-item" wx:if="{{detail.remark}}">
        <text class="label">备注说明</text>
        <text class="value">{{detail.remark}}</text>
      </view>
    </view>
  </view>

  <!-- 位置信息 -->
  <view class="location-card" wx:if="{{detail.latitude && detail.longitude}}">
    <view class="header">
      <text class="title">位置信息</text>
    </view>
    <map class="map" 
         latitude="{{detail.latitude}}" 
         longitude="{{detail.longitude}}"
         markers="{{markers}}"
         scale="16">
    </map>
  </view>

  <!-- 照片信息 -->
  <view class="photo-card" wx:if="{{detail.photos.length > 0}}">
    <view class="header">
      <text class="title">现场照片</text>
    </view>
    <view class="photo-list">
      <image wx:for="{{detail.photos}}" 
             wx:key="index"
             src="{{item}}"
             mode="aspectFill"
             class="photo"
             bindtap="previewImage"
             data-url="{{item}}"/>
    </view>
  </view>
</view> 