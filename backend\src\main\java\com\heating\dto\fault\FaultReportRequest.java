package com.heating.dto.fault;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class FaultReportRequest {
    @JsonProperty("heat_unit_id")
    private Long heatUnitId;

    @JsonProperty("house_id")
    private Long houseId;

    @JsonProperty("alarm_id")
    private Long alarmId;

    @JsonProperty("fault_type")
    private String faultType;

    @JsonProperty("fault_source")
    private String faultSource;

    @JsonProperty("fault_level")
    private String faultLevel;

    @JsonProperty("fault_desc")
    private String faultDesc;

    @JsonProperty("fault_status")
    private String faultStatus;

    @JsonProperty("occur_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime occurTime;

    @JsonProperty("report_user_id")
    private int reportUserId;
    
    @JsonProperty("repair_user_id")
    private int repairtUserId;
    
    
    @JsonProperty("address")
    private String address;
    
    @JsonProperty("manager_id")
    private Long managerId;

    @JsonProperty("attachment")
    private List<FaultAttachmentRequest> attachment; 
}