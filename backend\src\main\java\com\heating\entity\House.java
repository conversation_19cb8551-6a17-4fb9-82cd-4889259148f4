package com.heating.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "t_house")
@Data
public class House {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "heat_unit_id")
    private Long heatUnitId;
    
    @Column(name = "heat_unit_no")
    private String heatUnitNo;
    
    @Column(name = "floor_no")
    private Long floorNo;
    
    @Column(name = "room_no")
    private String roomNo;
    
    @Column(name = "house_number")
    private String houseNumber;
    
    @Column(name = "built_area")
    private Double builtArea;
    
    @Column(name = "billable_area")
    private Double billableArea;
    
    @Column(name = "toward")
    private String toward;
    
    @Column(name = "house_master_tel")
    private String houseMasterTel;
    
    @Column(name = "house_master")
    private String houseMaster;
    
    @Column(name = "house_type")
    private String houseType;
    
    @Column(name = "is_pay")
    private Integer isPay;
    
    @Column(name = "is_heating")
    private Integer isHeating;
    
    @Column(name = "valve_status")
    private Integer valveStatus;
    
    @Column(name = "heating_type")
    private Integer heatingType;
    
    @Column(name = "room_area")
    private String roomArea;
    
    @Column(name = "house_properties")
    private String houseProperties;
    
    @Column(name = "measurement_method")
    private Integer measurementMethod;
    
    @Column(name = "mm")
    private String remark;
    
    // 业务方法：获取房屋地址
    public String getAddress() {
        StringBuilder address = new StringBuilder();
        if (roomArea != null && !roomArea.isEmpty()) {
            address.append(roomArea);
        }
        if (heatUnitNo != null && !heatUnitNo.isEmpty()) {
            if (address.length() > 0) address.append(" ");
            address.append(heatUnitNo).append("号楼");
        }
        if (floorNo != null) {
            address.append(" ").append(floorNo).append("层");
        }
        if (roomNo != null && !roomNo.isEmpty()) {
            address.append(" ").append(roomNo).append("室");
        }
        return address.toString();
    }
    
    // 业务方法：获取房屋面积
    public Double getArea() {
        // 优先使用计量面积，如果没有则使用建筑面积
        return billableArea != null ? billableArea : builtArea;
    }
}