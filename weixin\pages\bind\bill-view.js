const { billApi } = require('../../api/index.js');

Page({
  data: {
    loading: false,
    billDetail: null,
    basicInfo: {},
    billInfo: {},
    paymentRecords: [],
    overdueInfo: {},
    heatingYear: null
  },

  onLoad(options) {
    // 获取传入的供暖年度参数
    const heatingYear = options.heatingYear ? parseInt(options.heatingYear) : null;
    this.setData({ heatingYear });
    
    // 加载账单详情
    this.loadBillDetail();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadBillDetail();
  },

  /**
   * 加载账单详情
   */
  async loadBillDetail() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showModal({
        title: '提示',
        content: '请先绑定户号',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载中...'
    });

    try {
      console.log('开始加载账单详情...');
      
      // 如果没有指定年度，使用当前供暖年度
      let heatingYear = this.data.heatingYear;
      if (!heatingYear) {
        heatingYear = this.getCurrentHeatingYear();
        this.setData({ heatingYear });
      }
      
      const response = await billApi.viewBillDetail({
        houseId: userInfo.houseId,
        heatingYear: heatingYear
      });

      console.log('账单详情响应:', response);

      if (response && response.code === 200) {
        const data = response.data || {};
        
        this.setData({
          billDetail: data,
          basicInfo: data.basicInfo || {},
          billInfo: data.billInfo || {},
          paymentRecords: data.paymentRecords || [],
          overdueInfo: data.overdueInfo || {}
        });

        console.log('账单详情加载成功');
      } else {
        // 不再抛出错误，而是显示空状态
        console.warn('获取账单详情失败:', response.message);
        this.setData({
          billDetail: null,
          basicInfo: {},
          billInfo: { status: 'no_bill', statusText: '暂无账单' },
          paymentRecords: [],
          overdueInfo: {}
        });
      }

    } catch (error) {
      console.error('加载账单详情失败:', error);
      
      // 不再显示错误弹窗，而是显示空状态
      this.setData({
        billDetail: null,
        basicInfo: {},
        billInfo: { status: 'no_bill', statusText: '暂无账单' },
        paymentRecords: [],
        overdueInfo: {}
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },

  /**
   * 获取当前供暖年度
   */
  getCurrentHeatingYear() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // getMonth()返回0-11
    
    // 如果是1-10月，则属于上一年的供暖年度
    if (currentMonth <= 10) {
      return currentYear - 1;
    } else {
      // 如果是11-12月，则属于当年的供暖年度
      return currentYear;
    }
  },

  /**
   * 去缴费
   */
  goToPay() {
    const { billInfo } = this.data;
    if (!billInfo || !billInfo.billId) {
      wx.showToast({
        title: '账单信息异常',
        icon: 'none'
      });
      return;
    }

    // 检查是否需要缴费
    if (billInfo.status === 'paid') {
      wx.showToast({
        title: '账单已缴清',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/payment/index?billId=${billInfo.billId}`
    });
  },

  /**
   * 查看缴费记录详情
   */
  viewPaymentDetail(e) {
    const paymentId = e.currentTarget.dataset.paymentId;
    if (paymentId) {
      wx.navigateTo({
        url: `/pages/payment/detail?paymentId=${paymentId}`
      });
    }
  },

  /**
   * 切换供暖年度
   */
  changeHeatingYear() {
    const currentHeatingYear = this.getCurrentHeatingYear();
    const years = [];
    const yearValues = [];
    
    // 生成最近5年的供暖年度选项
    for (let i = 0; i < 5; i++) {
      const year = currentHeatingYear - i;
      years.push(`${year}-${year + 1}供暖年度`);
      yearValues.push(year);
    }

    wx.showActionSheet({
      itemList: years,
      success: (res) => {
        const selectedYear = yearValues[res.tapIndex];
        this.setData({ heatingYear: selectedYear });
        this.loadBillDetail();
      }
    });
  },

  /**
   * 刷新数据
   */
  onRefresh() {
    this.loadBillDetail();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadBillDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  /**
   * 分享账单
   */
  onShareAppMessage() {
    return {
      title: '我的供暖账单',
      path: '/pages/bind/bill-view',
      imageUrl: '/images/share-bill.png'
    };
  }
});
