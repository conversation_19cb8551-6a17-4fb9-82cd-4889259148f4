<view class="container">
  <!-- 欢迎信息 -->
  <view class="welcome-header">
    <text class="welcome-text">欢迎回来，{{userInfo.name || '123456'}}！</text>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-info">
    <view class="info-item">
      <view class="info-icon"></view>
      <text class="info-label">户号</text>
      <text class="info-value">{{userInfo.houseNumber || 'HT2024000123'}}</text>
    </view>
    
    <view class="info-item">
      <view class="info-icon"></view>
      <text class="info-label">地址</text>
      <text class="info-value">{{userInfo.address || '低区 1000号楼 1层 1-1-0101室'}}</text>
    </view>
    
    <view class="info-item">
      <view class="info-icon"></view>
      <text class="info-label">面积</text>
      <text class="info-value">{{userInfo.area || '115.7'}}㎡</text>
    </view>
    
    <view class="info-item">
      <view class="info-icon"></view>
      <text class="info-label">供暖状态</text>
      <text class="info-value">{{userInfo.heatingStatusText || '异常'}}</text>
    </view>
  </view>

  <!-- 功能按钮网格 -->
  <view class="function-grid">
    <view class="function-item" bindtap="goToPayment">
      <view class="function-icon"></view>
      <text class="function-text">查看账单</text>
    </view>

    <view class="function-item" bindtap="goToOnlinePayment">
      <view class="function-icon"></view>
      <text class="function-text">在线缴费</text>
    </view>

    <view class="function-item" bindtap="goToPaymentRecords">
      <view class="function-icon"></view>
      <text class="function-text">缴费记录</text>
    </view>

    <view class="function-item" bindtap="goToOnlineRepair">
      <view class="function-icon"></view>
      <text class="function-text">在线报修</text>
    </view>

    <view class="function-item" bindtap="goToFaultHistory">
      <view class="function-icon"></view>
      <text class="function-text">报修记录</text>
    </view>
    <!-- <view class="function-item" bindtap="goToStopSupply">
        <view class="function-icon"></view>
        <text class="function-text">申请停供</text>
      </view> -->
    <!-- <view class="function-item" bindtap="contactService">
      <view class="function-icon"></view>
      <text class="function-text">联系客服</text>
    </view> -->
  </view>
</view>
