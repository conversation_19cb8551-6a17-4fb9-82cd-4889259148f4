<cover-view class="tab-bar">
  <cover-view class="tab-bar-item" wx:for="{{list}}" wx:key="index" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <cover-view class="iconfont {{selected === index ? item.selectedIcon : item.icon}}"></cover-view>
    <cover-view class="text" style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</cover-view>
  </cover-view>
</cover-view> 