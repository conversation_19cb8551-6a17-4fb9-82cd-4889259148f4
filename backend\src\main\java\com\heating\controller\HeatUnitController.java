package com.heating.controller; 
import com.heating.service.HeatUnitService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.heating.dto.ApiResponse;
import com.heating.dto.heatUnit.HeatUnitDTO;
import com.heating.dto.heatUnit.HeatUnitCountResponse;
import java.util.List; 

@RestController
@RequestMapping("/api/heatunits")
public class HeatUnitController {
    private static final Logger logger = LoggerFactory.getLogger(HeatUnitController.class);

    @Autowired
    private HeatUnitService heatUnitService;

    @GetMapping("/list")
    public ResponseEntity<ApiResponse<List<HeatUnitDTO>>> getHeatUnits() {
        logger.info("Accessing GET /api/heatunits/list");
        try {
            List<HeatUnitDTO> units = heatUnitService.getHeatUnits();
            logger.info("Retrieved {} heat units", units.size());
            return ResponseEntity.ok(ApiResponse.success("获取供热单元列表成功", units));
        } catch (Exception e) {
            logger.error("Error in GET /api/heatunits/list: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage())); 
        }
    } 

    @GetMapping("/count")
    public ResponseEntity<ApiResponse<HeatUnitCountResponse>> getHeatUnitCount() {
        HeatUnitCountResponse count = heatUnitService.getHeatUnitCount();
        return ResponseEntity.ok(ApiResponse.success("获取供热单元总数成功", count));
    }
}