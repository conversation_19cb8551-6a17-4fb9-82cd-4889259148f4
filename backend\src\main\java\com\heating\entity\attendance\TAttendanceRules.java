package com.heating.entity.attendance;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalTime;

@Data
@Entity
@Table(name = "t_attendance_rules")
public class TAttendanceRules {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "clock_in_time", nullable = false)
    private LocalTime clockInTime; // 上班打卡时间

    @Column(name = "clock_out_time", nullable = false)
    private LocalTime clockOutTime; // 下班打卡时间

    @Column(name = "allowed_distance", nullable = false)
    private Integer allowedDistance; // 允许的打卡距离(米)

    @Column(name = "late_threshold", nullable = false)
    private Integer lateThreshold; // 迟到阈值(分钟)

    @Column(name = "early_leave_threshold", nullable = false)
    private Integer earlyLeaveThreshold; // 早退阈值(分钟)

    @Column(name = "location_name", length = 100)
    private String locationName; // 考勤地点名称
 
    @Column(name = "latitude")
    private Double latitude; // 考勤地点纬度

    @Column(name = "longitude")
    private Double longitude; // 考勤地点经度

    @Column(name = "effective_date")
    private LocalTime effectiveDate; // 生效日期

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true; // 是否激活
} 