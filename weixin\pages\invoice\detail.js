const { paymentApi } = require('../../api/index.js');

Page({
  data: {
    invoiceDetail: {},
    paymentId: null
  },

  onLoad(options) {
    const paymentId = options.paymentId;
    if (paymentId) {
      this.setData({ paymentId: paymentId });
      this.loadInvoiceDetail(paymentId);
    }
  },

  loadInvoiceDetail(paymentId) {
    wx.showLoading({
      title: '加载票据中...'
    });

    paymentApi.getInvoiceDetail(paymentId).then(res => {
      wx.hideLoading();
      console.log('票据信息:', res);
      if (res) {
        this.setData({
          invoiceDetail: {
            invoiceNo: res.data.invoiceNo || 'PJ' + new Date().getTime(),
            createDate: this.formatDate(new Date()),
            heatingPeriod: res.data.period || '2024年11月15日 - 2025年03月15日',
            ownerName: res.data.ownerName || '未知',
            houseNumber: res.data.houseNumber || '未知房号',
            address: res.data.address || '未知地址',
            area: res.data.area || '0',
            unitPrice: res.data.unitPrice || '0.00',
            totalAmount: res.data.amount || '0.00',
            paidAmount: res.data.amount || '0.00',
            paymentMethod: res.data.paymentMethodText || '未知方式',
            orderNo: res.data.transactionNo || '无',
            paymentTime: res.data.paymentDate || '未知时间'
           }
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取票据详情失败:', err);
      wx.showToast({
        title: err.message || '获取票据失败',
        icon: 'none'
      });
    });
  },

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日`;
  },

  downloadPDF() {
    wx.showLoading({
      title: '生成PDF中...'
    });

    paymentApi.downloadInvoicePDF(this.data.paymentId).then(res => {
      wx.hideLoading();
      wx.showModal({
        title: '下载成功',
        content: 'PDF票据已保存到手机相册',
        showCancel: false,
        confirmText: '知道了'
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: err.message || '下载失败',
        icon: 'none'
      });
    });
  },

  shareInvoice() {
    wx.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '保存图片到相册'],
      success: (res) => {
        const actions = ['微信好友', '朋友圈', '相册'];
        
        if (res.tapIndex === 2) {
          this.saveToAlbum();
        } else {
          wx.showToast({
            title: `已分享到${actions[res.tapIndex]}`,
            icon: 'success'
          });
        }
      }
    });
  },

  saveToAlbum() {
    wx.showLoading({
      title: '生成图片中...'
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    }, 1500);
  }
});