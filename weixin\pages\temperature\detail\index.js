Page({
  data: {
    detail: null,
    markers: []
  },

  onLoad(options) {
    const id = options.id;
    this.loadDetail(id);
  },

  loadDetail(id) {
    wx.request({
      url: `http://localhost:5000/api/temperature/${id}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          const detail = res.data.data;
          
          // 设置地图标记
          const markers = [];
          if (detail.latitude && detail.longitude) {
            markers.push({
              id: 1,
              latitude: detail.latitude,
              longitude: detail.longitude,
              width: 32,
              height: 32
            });
          }

          this.setData({ 
            detail,
            markers
          });
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    if (this.data.detail.photos && this.data.detail.photos.length > 0) {
      wx.previewImage({
        urls: this.data.detail.photos,
        current: url
      });
    }
  }
}); 