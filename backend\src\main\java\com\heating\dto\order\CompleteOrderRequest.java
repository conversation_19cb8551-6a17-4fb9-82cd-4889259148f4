package com.heating.dto.order;
 
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class CompleteOrderRequest {
 
    @JsonProperty("order_id")   
    private Long orderId;

    @JsonProperty("repair_user_id")
    private Long repairUserId;

    @JsonProperty("order_status")
    private String orderStatus;

    @JsonProperty("repair_content")
    private String repairContent;

    @JsonProperty("repair_result")
    private String repairResult;

    @JsonProperty("repair_time")
    private String repairTime;
    
    @JsonProperty("repair_materials_quantity")
    private Map<String, Integer> repairMaterialsQuantity;

    @JsonProperty("attachment")
    private List<OrderAttachmentRequest> attachment; 
 
}
