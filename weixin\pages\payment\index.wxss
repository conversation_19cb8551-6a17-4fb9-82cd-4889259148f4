.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 30rpx;
}

/* 账单信息卡片 */
.bill-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.bill-header {
  padding: 30rpx 30rpx 0;
}

.bill-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.bill-info {
  padding: 20rpx 30rpx 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.divider {
  height: 1rpx;
  background: #eee;
  margin: 20rpx 0;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 10rpx;
}

.amount-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.amount-value {
  font-size: 40rpx;
  color: #ff6b35;
  font-weight: 700;
}

/* 缴费类型选择 */
.fee-type-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.fee-type-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.fee-type-option:last-child {
  border-bottom: none;
}

.fee-type-option.selected {
  background: #f8f9ff;
  border-radius: 12rpx;
  padding: 25rpx 20rpx;
  margin: 0 -20rpx;
}

.fee-type-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-left: 20rpx;
}

.fee-type-amount {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 700;
}

/* 支付方式选择 */
.payment-methods {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.payment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.payment-option.selected {
  border-color: #ff6b35;
  background: #fff8f6;
}

.payment-option:last-child {
  margin-bottom: 0;
}

.option-left {
  display: flex;
  align-items: center;
}

.radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio.checked {
  border-color: #ff6b35;
  background: #ff6b35;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.payment-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.payment-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.payment-desc {
  font-size: 24rpx;
  color: #999;
}

/* 协议同意 */
.agreement-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.agreement-checkbox {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  border-color: #ff6b35;
  background: #ff6b35;
}

.checkbox .check-icon {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.agreement-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.agreement-link {
  font-size: 28rpx;
  color: #ff6b35;
  text-decoration: underline;
}

/* 支付按钮 */
.pay-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.04);
}

.payment-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.summary-label {
  font-size: 30rpx;
  color: #666;
}

.summary-amount {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: 700;
}

.pay-button {
  width: 100%;
  height: 100rpx;
  line-height: 60rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
}

.pay-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.3);
}

.pay-button.disabled {
  background: #ccc;
  box-shadow: none;
}