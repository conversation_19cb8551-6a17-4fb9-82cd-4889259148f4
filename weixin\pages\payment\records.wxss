/* 页面容器 */
.page-container {
  height: 100vh;
  background: #f5f5f5;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  color: #fff;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.house-info {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 统计信息卡片 */
.summary-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-value.amount {
  color: #52c41a;
}

/* 缴费记录列表 */
.records-list {
  margin-bottom: 40rpx;
}

.record-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 6rpx solid #52c41a;
  transition: all 0.3s ease;
}

.record-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-status {
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #52c41a;
}

.record-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #52c41a;
}

.record-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  width: 180rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

/* 功能按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  font-size: 26rpx;
  border-radius: 32rpx;
  border: none;
  color: #fff;
  font-weight: 500;
}

.preview-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.download-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}
