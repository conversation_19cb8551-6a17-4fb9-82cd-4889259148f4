package com.heating.entity.fault;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_fault_attachment")
@Data
public class TFaultAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "fault_id")
    private long faultId;

    @Column(name = "file_type")
    private String fileType;

    @Column(name = "file_path")
    private String filePath;

    // 格式化时间  
    @Column(name = "created_at")
    private LocalDateTime createdAt;
 
}