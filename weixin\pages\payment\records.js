const { paymentApi } = require('../../api/index.js');

Page({
  data: {
    paymentRecords: [],
    summary: null,
    houseInfo: '',
    loading: false
  },

  onLoad() {
    this.loadPaymentRecords();
  },

  onShow() {
    this.loadPaymentRecords();
  },

  loadPaymentRecords() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ 
      loading: true,
      houseInfo: userInfo.houseNumber || '未知房号'
    });
    
    wx.showLoading({
      title: '加载记录中...'
    });

    paymentApi.getPaymentRecordsByHouse(userInfo.houseId).then(res => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.log('缴费记录:', res);
      if (res.code===200) {
        // 格式化缴费记录数据
        const formattedRecords = res.data.payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          period: payment.period || '未知期间',
          paymentTime: payment.paymentDate,
          paymentMethod: payment.paymentMethodText || '未知方式',
          transactionNo: payment.transactionNo,
          billId: payment.billId
        }));

        // 计算统计信息
        const summary = {
          totalCount: formattedRecords.length,
          totalAmount: formattedRecords.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0).toFixed(2)
        };

        this.setData({
          paymentRecords: formattedRecords,
          summary: summary
        });
      }
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取缴费记录失败:', err);
      wx.showToast({
        title: err.message || '获取记录失败',
        icon: 'none'
      });
    });
  },

  viewRecordDetail(e) {
    const recordId = e.currentTarget.dataset.id;
    const record = this.data.paymentRecords.find(r => r.id === recordId);
    if (record && record.billId) {
      wx.navigateTo({
        url: '/pages/bill/detail?id=' + record.billId
      });
    }
  },
  onPullDownRefresh() {
    this.loadPaymentRecords();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },
  
  // 预览票据
  previewInvoice(e) {
    const paymentId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/invoice/detail?paymentId=' + paymentId
    });
  },

  // 下载PDF
  downloadPDF(e) {
    const paymentId = e.currentTarget.dataset.id;
    
    wx.showLoading({
      title: '生成PDF中...'
    });

    // 调用下载PDF接口
    paymentApi.downloadInvoicePDF(paymentId).then(res => {
      wx.hideLoading();
      wx.showToast({
        title: 'PDF已保存到相册',
        icon: 'success'
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: err.message || '下载失败',
        icon: 'none'
      });
    });
  }
});
