<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="avatar-box">
      <image class="avatar" src="{{userInfo.headpic || '/images/default-avatar.png'}}" bindtap="chooseImage"></image>
    </view>
    <view class="user-info">
      <text class="name">{{userInfo.name || '未登录'}}</text>
      <!-- <text class="role">{{userInfo.role || '暂无角色'}}</text> -->
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-list">
    <!-- 服务功能 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="goToMyFaults">
        <view class="menu-content">
          <view class="menu-icon fault-icon">⚠️</view>
          <text class="menu-text">我的报修</text>
        </view>
        <text class="arrow-icon">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToMyBills">
        <view class="menu-content">
          <view class="menu-icon bill-icon">�</view>
          <text class="menu-text">我的账单</text>
        </view>
        <text class="arrow-icon">›</text>
      </view>
    </view>

    <!-- 系统功能 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="contactService">
        <view class="menu-content">
          <view class="menu-icon service-icon">💬</view>
          <text class="menu-text">联系客服</text>
        </view>
        <text class="arrow-icon">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-content">
          <view class="menu-icon settings-icon">⚙️</view>
          <text class="menu-text">系统设置</text>
        </view>
        <text class="arrow-icon">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-content">
          <view class="menu-icon about-icon">ℹ️</view>
          <text class="menu-text">关于我们</text>
        </view>
        <text class="arrow-icon">›</text>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-section" wx:if="{{userInfo.name}}">
    <button class="logout-btn" bindtap="handleLogout">退出登录</button>
  </view>
</view> 
