<view class="container">
  <!-- 工单基本信息 -->
  <view class="section">
    <view class="section-title">基本信息</view>
    <view class="info-item">
      <text class="label">工单编号：</text>
      <text class="value">{{detail.order_id}}</text>
    </view>
    <view class="info-item">
      <text class="label">换热站：</text>
      <text class="value">{{detail.station_name}}</text>
    </view>
    <view class="info-item">
      <text class="label">故障类型：</text>
      <text class="value">{{detail.fault_type}}</text>
    </view>
    <view class="info-item">
      <text class="label">故障等级：</text>
      <text class="level-{{detail.fault_level}}">{{detail.fault_level_text}}</text>
    </view>
    <view class="info-item">
      <text class="label">故障描述：</text>
      <text class="value">{{detail.fault_desc}}</text>
    </view>
    <view class="info-item">
      <text class="label">工单状态：</text>
      <text class="value status-{{detail.status}}">{{detail.status_text}}</text>
    </view>
    <view class="info-item">
      <text class="label">生成时间：</text>
      <text class="value">{{detail.created_time}}</text>
    </view>
  </view> 

  <!-- 维修信息 -->
  <view class="section" wx:if="{{detail.repair_content || detail.status === 'processing'}}">
    <view class="section-title">维修信息</view>
    <view class="info-item">
      <text class="label">维修人员：</text>
      <text class="value">{{detail.repair_user_name}}</text>
    </view>
    <view class="info-item">
      <text class="label">维修内容：</text>
      <block wx:if="{{detail.status === 'processing' && isEditing}}">
        <textarea class="input-area" 
                  value="{{detail.repair_content}}"
                  data-field="repair_content"
                  bindinput="handleInput"
                  placeholder="请输入维修内容"/>
      </block>
      <text wx:else class="value">{{detail.repair_content}}</text>
    </view>
    <view class="info-item">
      <text class="label">维修结果：</text>
      <block wx:if="{{detail.status === 'processing' && isEditing}}">
        <textarea class="input-area" 
                  value="{{detail.repair_result}}"
                  data-field="repair_result"
                  bindinput="handleInput"
                  placeholder="请输入维修结果"/>
      </block>
      <text wx:else class="value">{{detail.repair_result}}</text>
    </view>

    <!-- 修改维修时间的显示逻辑 -->
    <view class="info-item" wx:if="{{detail.status !== 'processing'}}">
      <text class="label">维修时间：</text>
      <text class="value">{{detail.repair_time}}</text>
    </view>
    
    <!-- 添加维修照片部分 -->
    <view class="form-item" wx:if="{{detail.status === 'processing' && isEditing }}">
      <text class="label">维修照片：</text>
      <view class="upload-box">
          <view class="image-list">
            <view 
              class="image-item" 
              wx:for="{{detail_images}}" 
              wx:key="*this"
            >
              <image 
                src="{{item}}" 
                mode="aspectFill" 
                bindtap="previewImage" 
                data-url="{{item}}"
              ></image>
              <view class="delete-btn" catchtap="deleteImage" data-index="{{index}}">
                <text class="wx-icon">×</text>
              </view>
            </view> 
            
            <view class="upload-actions" wx:if="{{detail_images.length < 4}}">
              <view class="upload-btn" bindtap="chooseFromAlbum">
                <text class="wx-icon">📁</text>
                <text class="upload-text">相册</text>
              </view>
              <view class="upload-btn" bindtap="takePhoto">
                <text class="wx-icon">📷</text>
                <text class="upload-text">拍照</text>
              </view>
            </view>

          </view> 
        </view>
    </view> 

    <!-- 添加编辑和完成按钮 -->
    <view class="action-buttons" wx:if="{{detail.status === 'processing'}}">
      <button wx:if="{{!isEditing}}" 
              class="btn-edit" 
              bindtap="handleEdit">编辑</button>
      <button wx:if="{{isEditing}}" 
              class="btn-complete" 
              bindtap="handleComplete">处理完成</button>
    </view>
  </view>

  <!-- 故障处理之前照片 --> 
  <view class="section" wx:if="{{detail.fault_attachments && detail.fault_attachments.length > 0}}">
    <view class="section-title">维修之前照片</view>
    <view class="image-list">
      <view class="image-item" 
            wx:for="{{detail.fault_attachments}}" 
            wx:key="index"
            wx:if="{{item.file_type === 'image'}}"
            bindtap="previewImage_fault"
            data-url="{{item.file_path}}">
        <image src="{{item.file_path}}"  mode="aspectFill"  class="fault-image"/>
      </view>
    </view>
  </view>

 
  <!-- 工单表附件列表 故障处理之后照片-->
  <view class="section" wx:if="{{detail.attachments.length > 0}}">
    <view class="section-title">维修之后照片</view>
    <view class="image-list">
      <view class="image-item" 
            wx:for="{{detail.attachments}}" 
            wx:key="index"
            wx:if="{{item.file_type === 'image'}}"
            bindtap="previewImage_attachments"
            data-url="{{item.file_path}}">
        <image src="{{item.file_path}}"  mode="aspectFill"  class="fault-image"/>
      </view>
    </view>
  </view>  

  <!-- 操作日志 -->
  <view class="section" wx:if="{{detail.logs.length > 0}}">
    <view class="section-title">操作日志</view>
    <view class="log-list">
      <view class="log-item" wx:for="{{detail.logs}}" wx:key="index">
        <view class="log-header">
          <text class="log-type">{{item.operation_type}}</text>
          <text class="log-time">{{item.created_time}}</text>
        </view>
        <view class="log-content">{{item.operation_desc}}</view>
      </view>
    </view>
  </view>
</view> 