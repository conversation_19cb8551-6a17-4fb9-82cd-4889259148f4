package com.heating.repository;

import com.heating.entity.bill.THeatingFeeRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface THeatingFeeRuleRepository extends JpaRepository<THeatingFeeRule, Long> {
    
    /**
     * 根据ID查询生效的供暖计费规则
     */
    @Query("SELECT r FROM THeatingFeeRule r WHERE r.id = :id AND r.isActive = true")
    Optional<THeatingFeeRule> findActiveById(@Param("id") Long id);
    
    /**
     * 查询当前生效的供暖计费规则
     */
    @Query("SELECT r FROM THeatingFeeRule r WHERE r.isActive = true ORDER BY r.id DESC")
    Optional<THeatingFeeRule> findCurrentActiveRule();
}