
管理员点击【批准】后，系统必须自动完成以下目标：
确认服务状态变更：将用户供暖状态从“供热”更新为“不供热”。
计算最终财务责任：根据申请时间和政策，确定用户是“减免”、“结算”还是“退费”。

系统根据“停供生效日期”与“供暖开始日期”的关系，自动判断属于哪种场景

1.在供暖开始前申请并获批停供，直接修改审批状态即可
2.在供暖开始后申请停供，且此前未缴费；

管理员审批通过后的业务逻辑：
1）计算“结算金额”：
计算A：按实际供暖天数计算费用。
实际天数 = 停供生效日 - 供暖开始日。
实际供暖费用 = (总金额 / 总天数) * 实际天数。
计算B：按最低缴费比例计算基础热损费。
基础热损费 = 总金额 * min_payment_rate。
最终结算金额 = MAX(计算A, 计算B)。
免除滞纳金：因为用户申请停供，视为主动终止服务，免除逾期期间产生的滞纳金。


2）处理“欠费记录表”（t_overdue_records）：
查询与该账单关联的 overdue_records 记录。
将其 status 从 active 更新为 written_off（已核销）。
在 remark 中添加：“因中途申请停供获批，此欠费记录已核销，待用户缴纳结算款”。

3）更新“账单表”（t_bill）：
绝不修改 total_amount（如2500元）。
将 status 保持 overdue。
在 remark 中追加：“已生成停供结算，实际应收金额：971.02元，滞纳金已免除”。


3.在供暖开始后申请停供，但此前已全额缴费

管理员审批通过后的业务逻辑
计算“应退金额”：
计算A：按实际供暖天数计算应收费用。
计算B：按最低缴费比例计算应收费用
最终应收金额 = MAX(计算A, 计算B)。
应退金额 = 已缴金额 - 最终应收金额。


创建“退费申请”：
系统自动在 t_refund 表中创建一条新记录。
payment_id：关联用户已支付的那笔 t_payment 记录。
refund_amount：1528.98。
refund_reason：“用户中途申请停供，退还多缴费用”。
status：pending。

更新“账单表”（t_bill）：
total_amount 和 paid_amount 保持不变（2500元）。
在 remark 中添加：“用户已申请停供，已发起退费申请，金额1528.98元”。
