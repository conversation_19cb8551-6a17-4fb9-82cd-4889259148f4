package com.heating.controller;

import com.heating.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/upload")
public class UploadController {
    private static final Logger logger = LoggerFactory.getLogger(UploadController.class);

    @Value("${file.uploadFolder}")
    private String uploadPath;


    /**
     * 上传图片 - App端
     * @param file 图片文件
     * @return 上传结果
     */
    @PostMapping("/uploadImage/app")
    public Result<String> uploadImageForApp(@RequestParam("file") MultipartFile file) {
        logger.info("收到图片上传请求，文件名: {}, 大小: {} bytes", 
            file.getOriginalFilename(), file.getSize());
        return uploadFile(file, "images");
    }

    /**
     * 上传视频 - App端
     * @param file 视频文件
     * @return 上传结果
     */
    @PostMapping("/uploadVideo/app")
    public Result<String> uploadVideoForApp(@RequestParam("file") MultipartFile file) {
        logger.info("收到视频上传请求，文件名: {}, 大小: {} bytes", 
            file.getOriginalFilename(), file.getSize());
        return uploadFile(file, "videos");
    }

    /**
     * 通用文件上传方法
     * @param file 上传的文件
     * @param subDir 子目录名称
     * @return 上传结果
     */
    private Result<String> uploadFile(MultipartFile file, String subDir) {
        if (file.isEmpty()) {
            logger.warn("上传的文件为空");
            return Result.error(400, "上传文件不能为空");
        }

        try {
            // 按日期创建子目录
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            String dateDir = dateFormat.format(new Date());
            
            // 创建目录结构：/uploadPath/subDir/dateDir/
            String dirPath = uploadPath + File.separator + subDir + File.separator + dateDir;
            File dir = new File(dirPath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    logger.error("无法创建目录: {}", dirPath);
                    return Result.error(500, "无法创建上传目录");
                }
                logger.info("创建目录成功: {}", dirPath);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = "unknown_file";
            }
            String extension = originalFilename.contains(".") ? 
                originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            String newFilename = UUID.randomUUID().toString().replace("-", "") + extension;
            
            // 完整文件路径
            String filePath = dirPath + File.separator + newFilename;
            File dest = new File(filePath);
            
            // 保存文件
            file.transferTo(dest);
            logger.info("文件保存成功: {}", filePath);
            
            // 返回可访问的文件路径
            String accessPath = "/" + subDir + "/" + dateDir + "/" + newFilename;
            logger.info("文件上传成功: {}", accessPath);
            
            return Result.success(accessPath);
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return Result.error(500, "文件上传失败: " + e.getMessage());
        }
    }
} 