<scroll-view scroll-y="true" class="page-container">
  <view class="container">
    <!-- 账单详情卡片 -->
    <view class="detail-card">
      <view class="detail-header">
        <text class="detail-title">账单详情</text>
        <view class="bill-status">
          <text class="status-icon {{billDetail.status}}">{{billDetail.statusIcon}}</text>
          <text class="status-text {{billDetail.status}}">{{billDetail.statusText}}</text>
        </view>
      </view>

      <view class="detail-content">
        <view class="detail-item">
          <text class="item-label">供暖周期</text>
          <text class="item-value">{{billDetail.heatingPeriod}}</text>
        </view>
        
        <view class="detail-item">
          <text class="item-label">户号</text>
          <text class="item-value">{{billDetail.houseNumber}}</text>
        </view>
        
        <view class="detail-item">
          <text class="item-label">房屋地址</text>
          <text class="item-value">{{billDetail.address}}</text>
        </view>
        
        <view class="detail-item">
          <text class="item-label">计费面积</text>
          <text class="item-value">{{billDetail.area}}㎡</text>
        </view>
        <view class="detail-item">
          <text class="item-label">单价</text>
          <text class="item-value">¥{{billDetail.unitPrice}}/㎡</text>
        </view>
        <view class="divider"></view>
        <view class="detail-item amount-item">
          <text class="item-label">应缴金额</text>
          <text class="item-value amount">¥{{billDetail.amount}}</text>
        </view>
        <view class="divider"></view>
        <view class="detail-item">
          <text class="item-label">缴费日期</text>
          <text class="item-value">{{billDetail.createDate}}</text>
        </view>
        <!-- <view class="late-fee-notice" wx:if="{{billDetail.status === 'pending'}}">
          <text class="notice-icon">⚠️</text>
          <text class="notice-text">滞纳金说明：逾期每日加收0.05%</text>
        </view> -->
      </view>
    </view>

    <!-- 缴费记录卡片 -->
    <view class="payment-records-card">
      <view class="records-header">
        <text class="records-title">缴费记录</text>
        <text class="records-count">共{{paymentRecords.length}}条记录</text>
      </view>

      <view class="records-content" wx:if="{{paymentRecords.length > 0}}">
        <view class="payment-item" wx:for="{{paymentRecords}}" wx:key="id" 
              bindtap="viewPaymentDetail" data-id="{{item.id}}">
          <view class="payment-header">
            <view class="payment-status">
              <text class="status-icon {{item.status}}">{{item.statusIcon}}</text>
              <text class="status-text {{item.status}}">{{item.statusText}}</text>
            </view>
            <text class="payment-amount">¥{{item.amount}}</text>
          </view>
          
          <view class="payment-info">
            <view class="info-row" wx:if="{{item.orderNo}}">
              <text class="info-label">交易流水号：</text>
              <text class="info-value">{{item.orderNo}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付方式：</text>
              <text class="info-value">{{item.paymentMethod}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付时间：</text>
              <text class="info-value">{{item.paymentTime}}</text>
            </view>
       
          </view>
        </view>
      </view>

      <view class="empty-records" wx:if="{{paymentRecords.length === 0}}">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无缴费记录</text>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>

  <!-- 操作按钮 - 固定在底部 -->
  <view class="action-buttons">
    <button class="btn-secondary" bindtap="goBack">返回</button>
    <button class="btn-primary" wx:if="{{billDetail.status === 'pending'}}" 
            bindtap="goToPay">
      立即缴费
    </button>
  </view>
</scroll-view>


