const { faultApi } = require('../../../api/index.js');

Page({
  data: {
    // 故障记录列表
    faultList: [],
    
    // 统计信息
    statistics: {
      totalCount: 0,
      pendingCount: 0,
      confirmedCount: 0,
      thisMonthCount: 0
    },
    
    // 筛选状态
    currentStatus: '',
    
    // 分页信息
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    
    // 加载状态
    loading: false,
    
    // 用户信息
    userInfo: null
  },

  onLoad() {
    this.loadUserInfo();
    this.loadStatistics();
    this.loadFaultList();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载统计信息
  async loadStatistics() {
    try {
      const userInfo = this.data.userInfo;
      if (!userInfo) return;
      
      const result = await faultApi.getFaultStatistics(userInfo.id);
      
      if (result.code === 200) {
        this.setData({
          statistics: result.data
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  },

  // 加载故障记录列表
  async loadFaultList(isRefresh = false) {
    try {
      const userInfo = this.data.userInfo;
      if (!userInfo) {
        console.error('用户信息不存在');
        return;
      }

      if (isRefresh) {
        this.setData({
          currentPage: 1,
          faultList: [],
          hasMore: true
        });
      }

      this.setData({
        loading: true
      });

      console.log('开始加载故障记录，参数:', {
        userId: userInfo.id,
        page: this.data.currentPage,
        size: this.data.pageSize,
        status: this.data.currentStatus || null
      });

      const result = await faultApi.getFaultHistory(
        userInfo.id,
        this.data.currentPage,
        this.data.pageSize,
        this.data.currentStatus || null
      );

      console.log('故障记录API响应:', result);

      if (result.code === 200) {
        const responseData = result.data || {};
        const list = responseData.list || [];
        const newList = isRefresh ? list : [...this.data.faultList, ...list];

        this.setData({
          faultList: newList,
          hasMore: responseData.page < responseData.totalPages,
          loading: false
        });

        console.log('故障记录加载成功，当前列表长度:', newList.length);
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载故障记录失败:', error);
      this.setData({
        loading: false
      });

      wx.showToast({
        title: error.message || '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadFaultList(true)
      ]);
      
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    }
  },

  // 按状态筛选
  filterByStatus(e) {
    const status = e.currentTarget.dataset.status;
    
    if (status === this.data.currentStatus) {
      return;
    }
    
    this.setData({
      currentStatus: status,
      currentPage: 1,
      faultList: [],
      hasMore: true
    });
    
    this.loadFaultList(true);
  },

  // 加载更多
  loadMore() {
    if (this.data.loading || !this.data.hasMore) {
      return;
    }
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    this.loadFaultList();
  },

  // 查看故障详情
  viewFaultDetail(e) {
    const faultId = e.currentTarget.dataset.faultId;
    
    wx.navigateTo({
      url: `/pages/fault/detail/index?faultId=${faultId}`,
      success: function(res) {
        console.log('跳转到故障详情成功:', res);
      },
      fail: function(err) {
        console.error('跳转到故障详情失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 去报修
  goToRepair() {
    wx.navigateTo({
      url: '/pages/fault/repair/index'
    });
  },

  // 加载测试数据（临时用于界面调试）
  loadTestData() {
    const testFaultList = [
      {
        fault_id: 1,
        fault_no: 'GZ202501120001',
        fault_type: '供暖不热',
        fault_level: '重要',
        fault_desc: '客厅暖气片不热，温度明显偏低，影响正常生活',
        fault_status: '待确认',
        fault_source: '用户上报',
        occur_time: '2025-01-12 08:30',
        report_time: '2025-01-12 09:15',
        created_time: '2025-01-12 09:15'
      },
      {
        fault_id: 2,
        fault_no: 'GZ202501110002',
        fault_type: '暖气漏水',
        fault_level: '严重',
        fault_desc: '卧室暖气片接口处漏水，地面已有积水',
        fault_status: '已确认',
        fault_source: '用户上报',
        occur_time: '2025-01-11 14:20',
        report_time: '2025-01-11 14:25',
        created_time: '2025-01-11 14:25'
      },
      {
        fault_id: 3,
        fault_no: 'GZ202501100003',
        fault_type: '阀门故障',
        fault_level: '一般',
        fault_desc: '温控阀无法正常调节，一直处于最大开度',
        fault_status: '已退回',
        fault_source: '用户上报',
        occur_time: '2025-01-10 16:45',
        report_time: '2025-01-10 17:00',
        created_time: '2025-01-10 17:00'
      },
      {
        fault_id: 4,
        fault_no: 'GZ202501090004',
        fault_type: '管道堵塞',
        fault_level: '重要',
        fault_desc: '供暖管道疑似堵塞，水流声异常',
        fault_status: '已确认',
        fault_source: '用户上报',
        occur_time: '2025-01-09 10:15',
        report_time: '2025-01-09 10:30',
        created_time: '2025-01-09 10:30'
      }
    ];

    const testStatistics = {
      totalCount: 4,
      pendingCount: 1,
      confirmedCount: 2,
      returnedCount: 1,
      thisMonthCount: 4,
      lastFaultTime: '2025-01-12 09:15'
    };

    this.setData({
      faultList: testFaultList,
      statistics: testStatistics,
      loading: false,
      hasMore: false
    });

    console.log('测试数据加载完成');
  }
});
