package com.heating.service.impl;

import com.heating.dto.permission.PermissionResponse;
import com.heating.dto.permission.RolePermissionResponse;
import com.heating.entity.permission.TSysPermission;
import com.heating.entity.permission.TSysRolePermission;
import com.heating.repository.SysPermissionRepository;
import com.heating.repository.SysRolePermissionRepository;
import com.heating.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统权限服务实现类
 */
@Service
@Slf4j
public class SysPermissionServiceImpl implements SysPermissionService {

    @Autowired
    private SysPermissionRepository sysPermissionRepository;
    
    @Autowired
    private SysRolePermissionRepository sysRolePermissionRepository;

    /**
     * 获取系统权限列表
     * @return 权限列表
     */
    @Override
    public List<PermissionResponse> getSystemPermissions() {
        // 从数据库获取所有权限
        List<TSysPermission> permissions = sysPermissionRepository.findAllByOrderById();
        
        // 转换为响应DTO
        return permissions.stream()
                .map(permission -> new PermissionResponse(
                        permission.getId(),
                        permission.getPermissionCode(),
                        permission.getPermissionName(),
                        permission.getMenuName(),
                        permission.getPath()
                ))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取角色权限编码列表
     * @param roleCode 角色编码
     * @return 角色权限列表
     */
    @Override
    public List<RolePermissionResponse> getRolePermissions(String roleCode) {
        log.info("Getting permissions for role: {}", roleCode);
        
        // 查询指定角色的所有有效权限关联
        List<TSysRolePermission> rolePermissions = sysRolePermissionRepository.findByRoleCodeAndStatusTrue(roleCode);
        
        // 转换为响应DTO
        return rolePermissions.stream()
                .map(rp -> new RolePermissionResponse(
                        rp.getId(), 
                        rp.getPermissionCode()
                ))
                .collect(Collectors.toList());
    }
} 