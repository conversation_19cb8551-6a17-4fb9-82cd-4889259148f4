package com.heating.dto.patrol;

import com.heating.converter.JsonConverter;
import jakarta.persistence.Convert;
import lombok.Data;
import java.time.LocalDate;
import java.util.List;

/**
 * 巡检计划列表响应DTO
 */
@Data
public class PatrolPlanListResponse {
    /**
     * 巡检计划ID
     */
    private Long id;
    
    /**
     * 巡检计划名称
     */
    private String name;
    
    /**
     * 计划编号
     */
    private String planNo;
    
    /**
     * 巡检类型：换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
     */
    private String patrolType;
    
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
    
    /**
     * 巡检地点
     */
    private String locations;
    
    /**
     * 巡检执行人，存储执行人姓名
     */
    private List<String> executors;
    
    /**
     * 状态: pending-待执行,processing-执行中,completed-已完成
     */
    private String status;

    /**
     * 是否开始执行
     */
    private Boolean isPlay;

    /**
     * 周期类型
     */
    private String scheduleType;

    /**
     * 具体时间
     */
    private String scheduleDays;

} 