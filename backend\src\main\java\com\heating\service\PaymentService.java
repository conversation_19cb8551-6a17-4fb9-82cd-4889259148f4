package com.heating.service;

import com.heating.dto.bill.OnlinePaymentRequest;
import com.heating.dto.bill.OnlinePaymentResponse;
import com.heating.dto.payment.PaymentSubmitRequest;
import com.heating.dto.payment.PaymentSubmitResponse;

/**
 * 缴费服务接口
 */
public interface PaymentService {
    
    /**
     * 处理在线缴费
     * @param request 缴费请求
     * @return 缴费响应
     */
    OnlinePaymentResponse processOnlinePayment(OnlinePaymentRequest request);
    
    /**
     * 验证缴费请求
     * @param request 缴费请求
     */
    void validatePaymentRequest(OnlinePaymentRequest request);

    /**
     * 提交缴费（新版本）
     * 支持用热缴费和管网维护费两种类型
     * 1. 更新账单数据中的实际缴费数据
     * 2. 根据用户是否用热更新t_house住户表的is_heating状态
     * 3. 查看是否有欠费记录，同时清除掉欠费记录状态为cleared已清除状态
     *
     * @param request 缴费提交请求
     * @return 缴费提交响应
     */
    PaymentSubmitResponse submitPayment(PaymentSubmitRequest request);
}
