.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-title {
  display: block;
  color: #ffffff;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 统计信息卡片 */
.statistics-card {
  background: #ffffff;
  margin: -30rpx 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 3;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 筛选器 */
.filter-container {
  padding: 0 30rpx 20rpx;
}

.filter-tabs {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  font-size: 28rpx;
  color: #8c8c8c;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background: #1890ff;
  color: #ffffff;
  font-weight: 500;
}

/* 故障记录列表 */
.fault-list {
  padding: 0 30rpx;
}

.fault-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.fault-item:active {
  transform: scale(0.98);
}

/* 故障记录头部 */
.fault-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.fault-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.fault-no {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.fault-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.fault-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.fault-status.confirmed {
  background: #f6ffed;
  color: #52c41a;
}

.fault-status.returned {
  background: #fff2f0;
  color: #ff4d4f;
}

.fault-time {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 故障记录内容 */
.fault-content {
  margin-bottom: 20rpx;
}

.fault-type-level {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.fault-type {
  font-size: 30rpx;
  color: #262626;
  font-weight: 500;
}

.fault-level {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.fault-level.severe {
  background: #fff2f0;
  color: #ff4d4f;
}

/* ——— 新的简洁卡片样式 ——— */
.fault-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.status-accent {
  width: 8rpx;
  align-self: stretch;
  border-radius: 8rpx;
}
.status-accent.pending { background: #faad14; }
.status-accent.confirmed { background: #52c41a; }
.status-accent.returned { background: #ff4d4f; }

.item-main { flex: 1; }

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.item-title {
  font-size: 32rpx;
  color: #1f1f1f;
  font-weight: 600;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 999rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.status-badge.pending { background: #fff7e6; color: #fa8c16; }
.status-badge.confirmed { background: #f6ffed; color: #389e0d; }
.status-badge.returned { background: #fff2f0; color: #cf1322; }

.item-meta .meta-time {
  font-size: 26rpx;
  color: #8c8c8c;
}

.chevron {
  font-size: 44rpx;
  color: #bfbfbf;
  padding-left: 10rpx;
}


.fault-level.important {
  background: #fff7e6;
  color: #fa8c16;
}

.fault-level.normal {
  background: #f6ffed;
  color: #52c41a;
}

.fault-desc {
  display: block;
  font-size: 28rpx;
  color: #595959;
  line-height: 1.6;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.fault-occur-time {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 故障元信息 */
.fault-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 12rpx;
}

.fault-occur-time,
.fault-source {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 故障记录底部 */
.fault-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

.footer-left {
  flex: 1;
}

.create-time {
  font-size: 24rpx;
  color: #8c8c8c;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.view-detail {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
}

.arrow {
  font-size: 24rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8c8c8c;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.icon-text {
  font-size: 60rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #8c8c8c;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.empty-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  background: #ffffff;
  color: #1890ff;
  border: 2rpx solid #1890ff;
  border-radius: 44rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #bfbfbf;
}
