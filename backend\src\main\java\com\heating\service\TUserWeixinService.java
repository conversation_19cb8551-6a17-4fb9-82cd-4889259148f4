package com.heating.service;

import com.heating.entity.user.TUserWeixin;
import com.heating.dto.user.WeixinLoginRequest;
import com.heating.dto.user.WeixinRegisterRequest;
import com.heating.dto.user.BindHouseRequest;
import com.heating.vo.user.WeixinLoginResponse;
import com.heating.vo.user.WeixinUserInfoResponse;
import com.heating.dto.user.WeixinAuthLoginRequest;
import com.heating.dto.user.WeixinPhoneRegisterRequest;

import java.util.Map;

public interface TUserWeixinService {

    /**
     * 用户登录 - 手机号登录
     */
    WeixinLoginResponse login(WeixinLoginRequest request);

    /**
     * 用户注册 - 手机号注册
     */
    boolean register(WeixinRegisterRequest request);

    /**
     * 通过用户名绑定户号
     */
    boolean bindHouseByUsername(String username, String houseNumber);

    /**
     * 获取用户信息
     */
    WeixinUserInfoResponse getUserInfo(Long userId);

    /**
     * 根据用户名查询用户
     */
    TUserWeixin getByUsername(String username);

    /**
     * 根据openid查询用户
     */
    TUserWeixin getByOpenid(String openid);

    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long userId);

    /**
     * 微信授权登录
     */
    WeixinLoginResponse weixinLogin(WeixinAuthLoginRequest request);

    /**
     * 微信手机号注册
     */
    boolean weixinRegister(WeixinPhoneRegisterRequest request);

    /**
     * 获取微信用户信息
     */
    Map<String, Object> getWeixinUserInfo(String code);

    /**
     * 根据ID查询用户
     */
    TUserWeixin getById(Long userId);
}







