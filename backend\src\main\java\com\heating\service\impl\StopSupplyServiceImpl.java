package com.heating.service.impl;

import com.heating.dto.bill.StopSupplyApplyRequest;
import com.heating.entity.bill.TStopSupplyApply;
import com.heating.repository.StopSupplyApplyRepository;
import com.heating.service.StopSupplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class StopSupplyServiceImpl implements StopSupplyService {

    @Autowired
    private StopSupplyApplyRepository stopSupplyApplyRepository;

    @Override
    @Transactional
    public Long submitApply(StopSupplyApplyRequest request) {
        try {
            log.info("开始处理停供申请: houseId={}", request.getHouseId());
            
            // 数据验证
            validateApplyRequest(request);

            LocalDate startDate = LocalDate.parse(request.getStopStartDate());
            LocalDate endDate = null;
            
            if (request.getStopEndDate() != null && !request.getStopEndDate().trim().isEmpty()) {
                endDate = LocalDate.parse(request.getStopEndDate());
                if (endDate.isBefore(startDate)) {
                    throw new RuntimeException("停供结束日期不能早于开始日期");
                }
            }

            // 创建停供申请对象
            TStopSupplyApply apply = new TStopSupplyApply();
            apply.setHouseId(request.getHouseId());
            apply.setApplyDate(LocalDate.now());
            apply.setStopStartDate(startDate);
            apply.setStopEndDate(endDate);
            apply.setHeatingYear(getHeatingYear(startDate));
            apply.setStatus(TStopSupplyApply.ApplyStatus.pending);
            apply.setReason(request.getReason().trim());
            apply.setCreatedAt(LocalDateTime.now());
            apply.setUpdatedAt(LocalDateTime.now());

            // 保存申请记录
            TStopSupplyApply savedApply = stopSupplyApplyRepository.save(apply);
            
            log.info("停供申请提交成功，申请ID: {}", savedApply.getId());
            return savedApply.getId();
            
        } catch (Exception e) {
            log.error("提交停供申请失败: houseId={}, error={}", request.getHouseId(), e.getMessage(), e);
            throw new RuntimeException("提交申请失败：" + e.getMessage());
        }
    }

    @Override
    public List<TStopSupplyApply> getApplyList(Long houseId) {
        try {
            log.info("获取停供申请列表: houseId={}", houseId);
            
            List<TStopSupplyApply> applyList;
            if (houseId != null) {
                applyList = stopSupplyApplyRepository.findByHouseIdOrderByCreatedAtDesc(houseId);
            } else {
                applyList = stopSupplyApplyRepository.findAllByOrderByCreatedAtDesc();
            }
            
            log.info("获取到{}条停供申请记录", applyList.size());
            return applyList;
            
        } catch (Exception e) {
            log.error("获取停供申请列表失败: houseId={}, error={}", houseId, e.getMessage(), e);
            throw new RuntimeException("获取申请列表失败：" + e.getMessage());
        }
    }

    /**
     * 验证申请请求参数
     * @param request 申请请求
     */
    private void validateApplyRequest(StopSupplyApplyRequest request) {
        if (request.getHouseId() == null) {
            throw new RuntimeException("房屋ID不能为空");
        }
        if (request.getStopStartDate() == null || request.getStopStartDate().trim().isEmpty()) {
            throw new RuntimeException("停供开始日期不能为空");
        }
        if (request.getReason() == null || request.getReason().trim().isEmpty()) {
            throw new RuntimeException("申请原因不能为空");
        }
        
        // 验证日期格式
        try {
            LocalDate.parse(request.getStopStartDate());
        } catch (Exception e) {
            throw new RuntimeException("停供开始日期格式错误");
        }
        
        if (request.getStopEndDate() != null && !request.getStopEndDate().trim().isEmpty()) {
            try {
                LocalDate.parse(request.getStopEndDate());
            } catch (Exception e) {
                throw new RuntimeException("停供结束日期格式错误");
            }
        }
    }

    /**
     * 根据日期计算供暖年度
     * @param date 日期
     * @return 供暖年度
     */
    private Integer getHeatingYear(LocalDate date) {
        return date.getMonthValue() >= 11 ? date.getYear() : date.getYear() - 1;
    }

    @Override
    public TStopSupplyApply getApplyDetail(Long id) {
        try {
            log.info("获取停供申请详情: id={}", id);
            
            TStopSupplyApply apply = stopSupplyApplyRepository.findById(id).orElse(null);
            if (apply == null) {
                throw new RuntimeException("申请记录不存在");
            }
            
            log.info("获取停供申请详情成功: id={}", id);
            return apply;
            
        } catch (Exception e) {
            log.error("获取停供申请详情失败: id={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("获取申请详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void cancelApply(Long id) {
        try {
            log.info("取消停供申请: id={}", id);
            
            TStopSupplyApply apply = stopSupplyApplyRepository.findById(id).orElse(null);
            if (apply == null) {
                throw new RuntimeException("申请记录不存在");
            }
            
            if (apply.getStatus() != TStopSupplyApply.ApplyStatus.pending) {
                throw new RuntimeException("只能取消待审批的申请");
            }
            
            apply.setStatus(TStopSupplyApply.ApplyStatus.canceled);
            apply.setUpdatedAt(LocalDateTime.now());
            stopSupplyApplyRepository.save(apply);
            
            log.info("取消停供申请成功: id={}", id);
            
        } catch (Exception e) {
            log.error("取消停供申请失败: id={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("取消申请失败：" + e.getMessage());
        }
    }
}



