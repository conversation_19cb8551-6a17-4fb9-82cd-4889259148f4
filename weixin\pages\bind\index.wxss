.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  margin-top: var(--status-bar-height, 44rpx);
}

.navbar-left, .navbar-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.home-btn.disabled {
  background: #f5f5f5;
  opacity: 0.6;
}

.home-btn .iconfont {
  font-size: 32rpx;
  color: #999;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 页面内容 */
.page-content {
  padding-top: calc(var(--status-bar-height, 44rpx) + 88rpx + 40rpx);
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 40rpx;
}

.house-input {
  width: 93%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.house-input:focus {
  border-color: #4CAF50;
  background: #fff;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ff5722, #d84315);
  color: #fff;
 border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn:active {
  background: #45a049;
}

.tips-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  display: block;
}

.contact-section {
  text-align: center;
}

.contact-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background: #1890ff;
  color: #fff;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
}

.contact-btn .iconfont {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.contact-btn:active {
  background: #1677cc;
}
