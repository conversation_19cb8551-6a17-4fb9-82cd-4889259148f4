package com.heating.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 人员轨迹记录实体类
 */
@Data
@Entity
@Table(name = "t_person_trajectory")
public class PersonTrajectory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 员工ID (数据库表要求字段)
     */
    @Column(name = "employee_id", nullable = false)
    private Integer employeeId;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Integer userId;

    /**
     * 经度
     */
    @Column(name = "longitude")
    private String longitude;

    /**
     * 纬度
     */
    @Column(name = "latitude")
    private String latitude;

    /**
     * 状态：0-正常 1-异常
     */
    @Column(name = "status")
    private Integer status = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
} 