package com.heating.entity.house;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "t_house")
@Data   
public class THouse { 

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "heat_unit_id")
    private Long heatUnitId;

    @Column(name = "heat_unit_floor_id")    
    private Long heatUnitFloorId;

    @Column(name = "heat_unit_floor_unit_id")
    private Long heatUnitFloorUnitId;

    @Column(name = "heat_unit_no")
    private String heatUnitNo;

    @Column(name = "floor_no")
    private Long floorNo;

    @Column(name = "room_no")
    private String roomNo;

    @Column(name = "indoor_t_no")
    private String indoorTNo;

    @Column(name = "heat_meter_no")
    private String heatMeterNo;

    @Column(name = "valves_no")
    private String valvesNo;

    @Column(name = "built_area")
    private Double builtArea;

    @Column(name = "billable_area")
    private Double billableArea;

    @Column(name = "toward")
    private String toward;

    @Column(name = "house_master_tel")
    private String houseMasterTel;

    @Column(name = "house_master")
    private String houseMaster;

    @Column(name = "house_type")
    private String houseType;

    @Column(name = "house_pic")
    private String housePic;

    @Column(name = "is_install_heat_meter")
    private Integer isInstallHeatMeter;

    @Column(name = "is_pay")
    private Integer isPay;

    @Column(name = "is_heating")
    private Integer isHeating;

    @Column(name = "valve_status")
    private Integer valveStatus;

    @Column(name = "control_mode")
    private Integer controlMode;

    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "heating_type")
    private Integer heatingType;

    @Column(name = "valve_factory")
    private Integer valveFactory;

    @Column(name = "channel_no")
    private Integer channelNo;

    @Column(name = "meter_type")
    private String meterType;

    @Column(name = "concentrator_id")
    private String concentratorId;

    @Column(name = "add_code")
    private String addCode;

    @Column(name = "dev_lot_no")
    private String devLotNo;

    @Column(name = "dev_desc")
    private String devDesc;

    @Column(name = "use_quality")
    private String useQuality;

    @Column(name = "house_properties")
    private String houseProperties;

    @Column(name = "measurement_method")
    private Integer measurementMethod;

    @Column(name = "room_area")
    private String roomArea; 

}