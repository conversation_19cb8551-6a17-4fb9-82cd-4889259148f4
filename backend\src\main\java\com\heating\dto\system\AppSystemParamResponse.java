package com.heating.dto.system;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 系统参数响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppSystemParamResponse {
    
    /**
     * 系统名称
     */
    private String systemName;
    
    /**
     * 系统logo
     */
    private String systemLogo;
    
    /**
     * 系统版本
     */
    private String systemVersions;
    
    /**
     * 版权
     */
    private String copyright;
    
    /**
     * 公司
     */
    private String company;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 发布日期
     */
    private LocalDate releaseDate;
    /**
     * 联系人
     */
    private String linkman;
    
    /**
     * 联系电话
     */
    private String mobile;
    
    /**
     * 网址
     */
    private String internetAddr;
    
    /**
     * 公司地址
     */
    private String companyAddr;
    
    /**
     * 系统简介
     */
    private String intro;
    
    /**
     * 构建号
     */
    private String buildNumber;
}