<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 故障详情内容 -->
  <view wx:else>
    <!-- 故障基本信息卡片 -->
    <view class="detail-card">
      <view class="card-header">
        <text class="fault-no">{{faultDetail.fault_no}}</text>
        <view class="status {{statusClass}}">{{faultDetail.fault_status}}</view>
      </view>

      <view class="info-list">
        <view class="info-item">
          <text class="label">故障类型</text>
          <text class="value">{{faultDetail.fault_type}}</text>
        </view>
        <view class="info-item">
          <text class="label">故障等级</text>
          <view class="fault-level {{faultDetail.fault_level === '严重' ? 'severe' : faultDetail.fault_level === '重要' ? 'important' : 'normal'}}">
            {{faultDetail.fault_level}}
          </view>
        </view>
        <view class="info-item desc">
          <text class="label">故障描述</text>
          <text class="value">{{faultDetail.fault_desc}}</text>
        </view>
        <view class="info-item">
          <text class="label">发生时间</text>
          <text class="value">{{faultDetail.occur_time}}</text>
        </view>
        <view class="info-item">
          <text class="label">上报时间</text>
          <text class="value">{{faultDetail.report_time}}</text>
        </view>
      </view>

      <!-- 图片附件 -->
      <view class="section" wx:if="{{attachments.length > 0}}">
        <view class="section-title">现场照片</view>
        <view class="attachment-list">
          <view class="attachment-item"
                wx:for="{{attachments}}"
                wx:key="attachment_id"
                wx:if="{{item.file_type === '图片'}}">
            <image src="{{item.file_path}}"
                   mode="aspectFill"
                   bindtap="previewImage"
                   data-url="{{item.file_path}}"
                   class="attachment-image">
            </image>
          </view>
        </view>
      </view>
    </view>

    <!-- 处理状态跟踪 -->
    <view class="tracking-card">
      <view class="section-title">
        <text class="title-text">处理状态</text>
      </view>

      <view class="status-info">
        <view class="current-status">
          <text class="status-label">当前状态：</text>
          <view class="status-value {{statusClass}}">{{trackingInfo.status || '未知'}}</view>
        </view>
        <text class="status-desc">{{trackingInfo.statusDesc || '状态描述暂无'}}</text>
      </view>

      <!-- 工单信息 -->
      <view class="work-orders" wx:if="{{trackingInfo.hasWorkOrder}}">
        <view class="subsection-title">工单信息</view>
        <view class="work-order-item"
              wx:for="{{trackingInfo.workOrders}}"
              wx:key="orderId">
          <view class="order-header">
            <text class="order-no">{{item.orderNo}}</text>
            <view class="order-status">{{item.orderStatus}}</view>
          </view>
          <view class="order-info">
            <text class="repair-user">维修人员：{{item.repairUserName || '未分配'}}</text>
            <text class="create-time">创建时间：{{item.createdTime}}</text>
            <text class="repair-time" wx:if="{{item.repairTime}}">完成时间：{{item.repairTime}}</text>
            <text class="repair-result" wx:if="{{item.repairResult}}">处理结果：{{item.repairResult}}</text>
          </view>
        </view>
      </view>

      <!-- 操作记录 -->
      <view class="operation-logs" wx:if="{{trackingInfo.operationLogs && trackingInfo.operationLogs.length > 0}}">
        <view class="subsection-title">操作记录</view>
        <view class="timeline">
          <view class="timeline-item"
                wx:for="{{trackingInfo.operationLogs}}"
                wx:key="index">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="log-header">
                <text class="operation-type">{{item.operationType}}</text>
                <text class="operation-time">{{item.createdTime}}</text>
              </view>
              <text class="operation-desc">{{item.operationDesc}}</text>
              <text class="operator-name" wx:if="{{item.operatorName}}">操作人：{{item.operatorName}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>