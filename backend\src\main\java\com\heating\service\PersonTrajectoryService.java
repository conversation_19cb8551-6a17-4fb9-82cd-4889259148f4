package com.heating.service;

import com.heating.dto.PersonTrajectoryDTO;
import com.heating.entity.PersonTrajectory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员轨迹服务接口
 */
public interface PersonTrajectoryService {

    /**
     * 添加轨迹记录
     * @param dto 轨迹记录DTO
     * @return 添加后的轨迹记录
     */
    PersonTrajectory addTrajectory(PersonTrajectoryDTO dto);

    /**
     * 批量添加轨迹记录
     * @param dtoList 轨迹记录DTO列表
     * @return 添加后的轨迹记录列表
     */
    List<PersonTrajectory> addTrajectoryBatch(List<PersonTrajectoryDTO> dtoList);

    /**
     * 根据用户ID查询轨迹记录
     * @param userId 用户ID
     * @return 轨迹记录列表
     */
    List<PersonTrajectory> getTrajectoryByUserId(Integer userId);

    /**
     * 根据用户ID和时间范围查询轨迹记录
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 轨迹记录列表
     */
    List<PersonTrajectory> getTrajectoryByUserIdAndTimeRange(Integer userId, LocalDateTime startTime, LocalDateTime endTime);
} 