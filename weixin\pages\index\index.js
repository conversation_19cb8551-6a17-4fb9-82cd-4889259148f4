const { billApi, userApi } = require('../../api/index.js');

Page({
  data: {
    userInfo: {},
    currentDate: '',
    stats: {},
    recentFaults: [],
    tempUpdateTime: '',
    stationTemps: []
  },

  onLoad() {
    this.loadUserInfo();
    this.loadPageData();
  },

  onShow() {
    this.loadUserInfo();
  },

  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    console.log('当前用户信息:', userInfo);
    this.setData({
      userInfo: userInfo
    });
  },

  checkPendingBills() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    if (!token) {
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询账单中...'
    });

    console.log('开始查询账单...');
    
    billApi.getBillList({
      status: 'unpaid',
      page: 1,
      pageSize: 1
    }).then(res => {
      console.log('账单查询成功:', res);
      wx.hideLoading();
      
      if (res.code==200 && res.data.bills && res.data.bills.length > 0) {
        // 有待缴账单，跳转到缴费页面
        wx.navigateTo({
          url: '/pages/payment/index?billId=' + res.data.bills[0].id
        });
      } else {
        // 无待缴账单
        wx.showModal({
          title: '提示',
          content: '暂无待缴账单',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    }).catch(err => {
      console.error('账单查询失败 - 详细错误:', err);
      wx.hideLoading();
      
      let errorMessage = '查询账单失败';
      if (err && err.message) {
        errorMessage = err.message;
      } else if (err && err.code) {
        errorMessage = `错误代码: ${err.code}`;
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
      
      // 如果是认证相关错误，跳转到登录页
      if (err && (err.code === 401 || err.message.includes('登录'))) {
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/index'
          });
        }, 2000);
      }
    });
  },

  goToBind() {
    wx.navigateTo({
      url: '/pages/bind/index'
    });
  },

  goToBill() {
    wx.navigateTo({
      url: '/pages/bill/index'
    });
  },

  goToPayment() {
    // 跳转到新的查看账单详情页面
    wx.navigateTo({
      url: '/pages/bill/bill-view'
    });
  },

  goToOnlinePayment() {
    this.checkPendingBills();
  },

  goToElectronicInvoice() {
    wx.navigateTo({
      url: '/pages/invoice/index'
    });
  },

  goToPaymentRecords() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

   wx.navigateTo({
    url: '/pages/payment/records',
    success: function(res) {
      console.log('跳转成功:', res);
    },
    fail: function(err) {
      console.error('跳转失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
  },

  goToOnlineRepair() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/fault/repair/index',
      success: function(res) {
        console.log('跳转到在线报修成功:', res);
      },
      fail: function(err) {
        console.error('跳转到在线报修失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  goToFaultHistory() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/fault/history/index',
      success: function(res) {
        console.log('跳转到报修记录成功:', res);
      },
      fail: function(err) {
        console.error('跳转到报修记录失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false
    });
  },

  goToStopSupply() {
    wx.navigateTo({
      url: '/pages/stop-supply/index'
    });
  },

  loadPageData() {
    // 其他数据加载逻辑
  }
}); 
