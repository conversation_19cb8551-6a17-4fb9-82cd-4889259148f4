package com.heating.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heating.utils.JwtUtils;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import jakarta.servlet.http.Cookie;
import java.util.HashMap;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter { 
   
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private final JwtUtils jwtUtils;

    @Autowired
    public JwtAuthenticationFilter(JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }

    /**
     * 执行过滤器的主要逻辑
     * 本方法旨在通过JWT进行身份验证，并设置相应的用户信息和权限
     *
     * @param request 用于获取请求信息的HttpServletRequest对象
     * @param response 用于获取响应信息的HttpServletResponse对象
     * @param chain 过滤器链，用于将请求传递给下一个过滤器或目标资源
     * @throws ServletException 如果过滤过程中发生Servlet异常
     * @throws IOException 如果过滤过程中发生I/O异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {

        // 不拦截登录、注册和静态资源请求
        if (request.getRequestURI().equals("/api/auth/login") || 
            request.getRequestURI().equals("/api/auth/register") ||
            request.getRequestURI().equals("/api/weixin/login") ||
            request.getRequestURI().equals("/api/weixin/register") ||
            request.getRequestURI().startsWith("/api/weixin/") ||
            request.getRequestURI().startsWith("/uploads/")) {
            chain.doFilter(request, response);
            return;
        }
        
        try {
            // 从请求中提取JWT令牌
            String token = extractToken(request);
            if (token != null) {
                // 解析令牌以获取声明信息
                Claims claims = jwtUtils.getClaimsFromToken(token);
                // 从声明信息中提取用户ID和角色
                Long userId = claims.get("userId", Long.class);
                // 从令牌中提取权限列表
                List<String> permissions = jwtUtils.getPermissionsFromToken(token);
                // 将权限列表转换为Spring Security所需的GrantedAuthority对象列表
                List<SimpleGrantedAuthority> authorities = permissions.stream()
                    .map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());
                // 创建Authentication对象，用于Spring Security进行身份验证
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(userId, null, authorities);
                // 将身份验证信息设置到SecurityContext中，以便在后续流程中使用
                SecurityContextHolder.getContext().setAuthentication(authentication);
                // 将用户ID设置为请求属性，以便在后续流程中使用
                request.setAttribute("userId", userId);
                // 将请求传递给下一个过滤器或目标资源
                chain.doFilter(request, response);
            } else {
                // 如果没有token，返回401错误
                Map<String, Object> errorDetails = new HashMap<>();
                errorDetails.put("message", "缺少访问令牌");
                errorDetails.put("code", 401);
                
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                
                new ObjectMapper().writeValue(response.getOutputStream(), errorDetails);
            }
        } catch (Exception e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8"); 
            new ObjectMapper().writeValue(
                response.getWriter(),   
                Map.of(
                    "code", 401,
                    "message", "认证失败: " + e.getMessage()
                )
            );
        }  
    }

    private String extractToken(HttpServletRequest request) {
        logger.debug("Extracting token from request...");

        // Check header first
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            logger.debug("Token found in Authorization header.");
            return bearerToken.substring(7);
        }
        logger.debug("No token found in Authorization header, checking URL parameter...");

        // Then check URL parameter
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            logger.debug("Token found in URL parameter.");
            return tokenParam;
        }
        logger.debug("No token found in URL parameter, checking cookies...");

        // Finally check cookies
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("token".equals(cookie.getName())) {
                    logger.debug("Token found in cookies.");
                    return cookie.getValue();
                }
            }
        }
        logger.debug("No token found in cookies.");
        return null;
    }
}
