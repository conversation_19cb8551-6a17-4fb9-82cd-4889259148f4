# 按钮美化优化说明

## 优化内容

根据您的要求，对页面头部的年度选择和刷新按钮进行了美化，确保文字垂直居中。

### 🎨 主要改进

#### 1. 文字垂直居中
- 使用 `display: flex` + `align-items: center` + `justify-content: center`
- 确保文字在按钮中完美居中显示

#### 2. 按钮尺寸优化
- 固定按钮高度：`height: 70rpx`
- 年度按钮：弹性宽度，最大320rpx，最小200rpx
- 刷新按钮：最小宽度100rpx

#### 3. 视觉效果提升
- 增加毛玻璃效果：`backdrop-filter: blur(15rpx)`
- 添加阴影：`box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)`
- 增加按压效果：点击时缩放和背景变化
- 优化圆角：`border-radius: 35rpx`

#### 4. 细节优化
- 字体加粗：`font-weight: 500`
- 字间距：`letter-spacing: 1rpx`
- 过渡动画：`transition: all 0.3s ease`

## 具体代码

### 按钮样式
```css
.change-year-btn, .refresh-btn {
  display: flex;
  align-items: center;          /* 垂直居中 */
  justify-content: center;      /* 水平居中 */
  height: 70rpx;               /* 固定高度 */
  padding: 0 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 35rpx;        /* 圆角优化 */
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  color: white;
  backdrop-filter: blur(15rpx); /* 毛玻璃效果 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 阴影 */
  transition: all 0.3s ease;    /* 过渡动画 */
  font-weight: 500;            /* 字体加粗 */
  letter-spacing: 1rpx;        /* 字间距 */
}

/* 按压效果 */
.change-year-btn:active, .refresh-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);      /* 缩放效果 */
}

/* 按钮尺寸 */
.change-year-btn {
  flex: 1;
  max-width: 320rpx;
  min-width: 200rpx;
}

.refresh-btn {
  min-width: 100rpx;
  padding: 0 20rpx;
}
```

### 布局优化
```css
.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;                  /* 按钮间距 */
  position: relative;
  z-index: 2;
  padding: 10rpx 0;           /* 上下内边距 */
}
```

### 装饰效果
```css
/* 主装饰圆 */
.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

/* 次装饰圆 */
.page-header::after {
  content: '';
  position: absolute;
  top: -30%;
  left: -15%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}
```

## 视觉效果

### 1. 文字居中
- ✅ 文字完美垂直居中
- ✅ 文字水平居中
- ✅ 不同长度文字都能正确居中

### 2. 现代化设计
- 🎨 毛玻璃质感
- 🎨 柔和阴影效果
- 🎨 圆润的圆角设计
- 🎨 优雅的过渡动画

### 3. 交互体验
- 👆 按压反馈效果
- 👆 缩放动画
- 👆 背景变化
- 👆 流畅的过渡

### 4. 布局协调
- 📐 按钮间距合理
- 📐 尺寸比例协调
- 📐 与整体设计统一
- 📐 响应式适配

## 技术要点

### Flexbox 居中
使用现代CSS布局技术确保文字完美居中：
- `display: flex` - 启用弹性布局
- `align-items: center` - 垂直居中
- `justify-content: center` - 水平居中

### 毛玻璃效果
- `backdrop-filter: blur(15rpx)` - 背景模糊
- `background: rgba(255, 255, 255, 0.2)` - 半透明背景

### 交互动画
- `transition: all 0.3s ease` - 平滑过渡
- `:active` 伪类 - 按压状态
- `transform: scale(0.98)` - 缩放效果

这些优化使按钮更加美观现代，文字完美居中，用户体验更佳。
