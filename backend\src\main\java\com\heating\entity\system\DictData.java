package com.heating.entity.system;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_dict_data")
@Data
public class DictData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "name")
    private String name;

    @Column(name = "code")
    private String code;

    @Column(name = "dict_id")
    private Integer dictId;
    
    @Column(name = "status")
    private Integer status; 

    @Column(name = "note")
    private String note;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "create_user_id")
    private Integer createUserId;

    @Column(name = "update_user_id")    
    private Integer updateUserId;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
}