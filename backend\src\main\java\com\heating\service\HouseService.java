package com.heating.service;

import com.heating.entity.House;

import java.util.List;
import java.util.Optional;

public interface HouseService {

    /**
     * 根据户号查询房屋信息
     */
    Optional<House> findByHouseNumber(String houseNumber);

    /**
     * 根据ID查询房屋信息
     */
    Optional<House> findById(Long id);

    /**
     * 根据热用户ID查询房屋列表
     */
    List<House> findByHeatUnitId(Long heatUnitId);

    /**
     * 保存房屋信息
     */
    House save(House house);

    /**
     * 更新房屋供暖状态
     */
    boolean updateHeatingStatus(Long houseId, Integer heatingStatus);

    /**
     * 更新房屋缴费状态
     */
    boolean updatePayStatus(Long houseId, Integer payStatus);

    /**
     * 更新阀门状态
     */
    boolean updateValveStatus(Long houseId, Integer valveStatus);

    /**
     * 获取供暖状态文本描述
     */
    String getHeatingStatusText(Integer status);
}