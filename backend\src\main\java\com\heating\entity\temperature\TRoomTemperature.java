package com.heating.entity.temperature;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;  
import com.heating.converter.JsonConverter;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_room_temperature")
@Data
public class TRoomTemperature {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    
    @Column(name = "heat_unit_name")
    private String heatUnitName;
    
    @Column(name = "building_no")
    private String buildingNo;
    
    @Column(name = "unit_no")
    private String unitNo;
    
    @Column(name = "room_no")
    private String roomNo;
    
    @Column(name = "indoor_temp")
    private Double indoorTemp;
    
    @Column(name = "outdoor_temp")
    private Double outdoorTemp;
    
    @Column(name = "latitude")
    private Double latitude;
    
    @Column(name = "longitude")
    private Double longitude;
    
    @Column(name = "report_time")
    private LocalDateTime reportTime;
    
    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<String> images;
    
    @Convert(converter = JsonConverter.class)
    @Column(columnDefinition = "json")
    private List<String> videos;

    @Column(name = "remark")
    private String remark;

    @Column(name = "report_user_id")
    private long reportUserId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
 
}