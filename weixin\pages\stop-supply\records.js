const { stopSupplyApi } = require('../../api/index.js');

Page({
  data: {
    recordList: [],
    loading: false,
    pendingCount: 0,
    approvedCount: 0
  },

  onLoad() {
    this.loadRecordList();
  },

  onShow() {
    this.loadRecordList();
  },

  async loadRecordList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载记录中...'
    });

    try {
      const res = await stopSupplyApi.getApplyList(userInfo.houseId);
      wx.hideLoading();
      this.setData({ loading: false });
        
        if (res.code === 200) {
          const records = (res.data || []).map(record => ({
            id: record.id,
            houseId: record.houseId,
            applyDate: this.formatDate(record.applyDate),
            stopStartDate: record.stopStartDate,
            stopEndDate: record.stopEndDate,
            stopPeriod: this.formatStopPeriod(record.stopStartDate, record.stopEndDate),
            heatingYear: record.heatingYear,
            status: record.status,
            statusText: this.getStatusText(record.status),
            reason: record.reason,
            approvedBy: record.approvedBy,
            approvedAt: record.approvedAt ? this.formatDateTime(record.approvedAt) : null,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt
          }));

          // 计算统计数据
          const pendingCount = records.filter(r => r.status === 'pending').length;
          const approvedCount = records.filter(r => r.status === 'approved').length;

          this.setData({
            recordList: records,
            pendingCount: pendingCount,
            approvedCount: approvedCount
          });
        } else {
          wx.showToast({
            title: res.message || '获取记录失败',
            icon: 'none'
          });
        }
    } catch (error) {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取申请记录失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  viewRecordDetail(e) {
    const recordId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/stop-supply/detail?id=${recordId}`
    });
  },

  goToApply() {
    wx.navigateTo({
      url: '/pages/stop-supply/index'
    });
  },

  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期格式:', dateStr);
      return '暂无';
    }

    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    // 打印原始数据用于调试
    console.log('原始审批时间数据:', dateTimeStr, '类型:', typeof dateTimeStr);

    let date;

    // 处理不同的时间格式
    if (Array.isArray(dateTimeStr)) {
      // 如果是数组格式 [2025, 8, 12, 16, 34, 11]
      const [year, month, day, hour, minute, second] = dateTimeStr;
      date = new Date(year, month - 1, day, hour || 0, minute || 0, second || 0);
    } else if (typeof dateTimeStr === 'string') {
      // 如果是字符串格式
      date = new Date(dateTimeStr);
    } else {
      // 其他格式直接尝试转换
      date = new Date(dateTimeStr);
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期时间格式:', dateTimeStr);
      return '暂无';
    }

    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  formatStopPeriod(startDate, endDate) {
    if (!startDate) return '';
    const start = this.formatDate(startDate);
    if (endDate) {
      const end = this.formatDate(endDate);
      return `${start} 至 ${end}`;
    }
    return `${start} 起长期停供`;
  },

  getStatusText(status) {
    const statusMap = {
      'pending': '待审批',
      'approved': '已通过',
      'rejected': '已拒绝',
      'canceled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  onPullDownRefresh() {
    this.loadRecordList();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});