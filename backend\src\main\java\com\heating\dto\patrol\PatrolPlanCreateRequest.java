package com.heating.dto.patrol;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDate;
import java.util.List;

@Data
public class PatrolPlanCreateRequest {
    private String name;
    @JsonProperty("patrol_type")
    private String patrolType; // 巡检类型：换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
    @JsonProperty("start_date")
    private LocalDate startDate;
    @JsonProperty("end_date") 
    private LocalDate endDate;
    /**
     * 巡检执行人IDs
     */
    @JsonProperty("executor_ids")   
    private List<Object> executorIds;
    @JsonProperty("schedule_type")
    private String scheduleType; // daily/weekly/monthly/custom
    @JsonProperty("schedule_interval")
    private Integer scheduleInterval;
    @JsonProperty("schedule_week_days")
    private List<Integer> scheduleWeekDays;
    @JsonProperty("schedule_month_days")
    private List<Integer> scheduleMonthDays;
    @JsonProperty("device_ids")
    private List<Object> deviceIds;
    @JsonProperty("locations")
    private String locations; // 巡检地点

    @JsonProperty("patrol_item")
    private List<PatrolDeviceItem> patrolItem; // 新增字段

    @Data
    public static class PatrolDeviceItem {
        @JsonProperty("device_patrol_item_id")
        private Long devicePatrolItemId; 
    }
} 