.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面标题 */
.page-header {
  background: #fff;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 票据列表 */
.invoice-list {
  padding: 0 30rpx;
}

.invoice-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 6rpx solid #1890ff;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.invoice-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.invoice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.invoice-info {
  margin-bottom: 25rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  width: 30rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.divider {
  height: 1rpx;
  background: #eee;
  margin: 25rpx 0;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  line-height: 64rpx;
  padding: 0;
}

.preview-btn {
  background: #f0f9ff;
  color: #1890ff;
  border: 1rpx solid #1890ff;
}

.download-btn {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #52c41a;
}

.share-btn {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #fa8c16;
}

.action-btn:active {
  opacity: 0.8;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 50rpx;
  line-height: 1.5;
}

.generate-btn {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
}