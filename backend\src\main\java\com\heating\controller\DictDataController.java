package com.heating.controller;

import com.heating.entity.system.DictData;
import com.heating.service.DictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典数据控制器
 */
@RestController
@RequestMapping("/api/dict")
public class DictDataController {

    @Autowired
    private DictDataService dictDataService;

    /**
     * 根据字典ID查询字典数据列表
     * @param dictId 字典ID
     * @return 字典数据列表
     */
    @GetMapping("/data/{dictId}")
    public ResponseEntity<?> getDictDataByDictId(@PathVariable Integer dictId) {
        List<DictData> dictDataList = dictDataService.getDictDataByDictId(dictId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "获取字典数据成功");
        response.put("data", dictDataList);
        
        return ResponseEntity.ok(response);
    }
} 