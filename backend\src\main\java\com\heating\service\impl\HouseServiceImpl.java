package com.heating.service.impl;

import com.heating.entity.House;
import com.heating.repository.HouseRepository;
import com.heating.service.HouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class HouseServiceImpl implements HouseService {

    @Autowired
    private HouseRepository houseRepository;

    @Override
    public Optional<House> findByHouseNumber(String houseNumber) {
        return houseRepository.findByHouseNumber(houseNumber);
    }

    @Override
    public Optional<House> findById(Long id) {
        return houseRepository.findById(id);
    }

    @Override
    public List<House> findByHeatUnitId(Long heatUnitId) {
        return houseRepository.findByHeatUnitId(heatUnitId);
    }

    @Override
    @Transactional
    public House save(House house) {
        return houseRepository.save(house);
    }

    @Override
    @Transactional
    public boolean updateHeatingStatus(Long houseId, Integer heatingStatus) {
        try {
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (houseOpt.isPresent()) {
                House house = houseOpt.get();
                house.setIsHeating(heatingStatus);
                houseRepository.save(house);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新房屋供暖状态失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updatePayStatus(Long houseId, Integer payStatus) {
        try {
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (houseOpt.isPresent()) {
                House house = houseOpt.get();
                house.setIsPay(payStatus);
                houseRepository.save(house);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新房屋缴费状态失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateValveStatus(Long houseId, Integer valveStatus) {
        try {
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (houseOpt.isPresent()) {
                House house = houseOpt.get();
                house.setValveStatus(valveStatus);
                houseRepository.save(house);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新阀门状态失败", e);
            return false;
        }
    }

    @Override
    public String getHeatingStatusText(Integer status) {
        if (status == null) {
            return "正常";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "停暖";
            default:
                return "正常";
        }
    }
}