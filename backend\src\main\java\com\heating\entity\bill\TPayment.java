package com.heating.entity.bill;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_payment")
public class TPayment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "house_id", nullable = false)
    private Long houseId;

    @Column(name = "bill_id", nullable = false)
    private Long billId;

    @Column(name = "room_no", nullable = false)
    private String roomNo = "";

    @Column(name = "heat_year", nullable = false)
    private Integer heatYear = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false)
    private PaymentMethod paymentMethod;

    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;

    @Column(name = "transaction_no", length = 100)
    private String transactionNo;

    @Column(name = "payment_date", nullable = false)
    private LocalDateTime paymentDate;

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    public enum PaymentMethod {
        wechat, alipay, bank_transfer, cash
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}