<view class="container">
  
  <view class="form-card">
    <!-- 小区选择 -->
    <view class="form-item">
      <view class="label required">所在小区</view>
      <picker 
        bindchange="onCommunitySelect"
        range="{{communities}}"
        range-key="name"
      >
        <view class="picker-box">
          <text class="picker-text">{{selectedCommunity.name || '请选择小区'}}</text> 
        </view>
      </picker>
    </view>

    <!-- 换热站选择 -->
    <!-- <view class="form-item">
      <view class="label required">换热站</view>
      <picker 
        bindchange="onStationSelect"
        range="{{stations}}" 
        range-key="name"
        disabled="{{!selectedCommunity}}"
      >
        <view class="picker {{!selectedCommunity ? 'disabled' : ''}}">
          {{selectedStation.name || '请选择换热站'}}
        </view>
      </picker>
    </view> -->

    <!-- 楼栋信息 -->
    <view class="form-group">
      <view class="form-item">
        <view class="label required">楼栋号</view>
        <input 
          class="input"
          type="number"
          value="{{building}}"
          placeholder="请输入楼栋号"
          bindinput="onBuildingInput"
        />
      </view>
      <view class="form-item">
        <view class="label required">单元号</view>
        <input 
          class="input"
          type="number"
          value="{{unit}}"
          placeholder="请输入单元号"
          bindinput="onUnitInput"
        />
      </view>
      <view class="form-item">
        <view class="label required">户号</view>
        <input 
          class="input"
          type="number"
          value="{{room}}"
          placeholder="请输入户号"
          bindinput="onRoomInput"
        />
      </view>

       <!-- 温度信息 -->
      <view class="form-item">
        <view class="label required">室内温度</view>
        <view class="temp-input">
          <input 
            type="digit"
            value="{{indoorTemp}}"
            bindinput="onIndoorTempInput"
            placeholder="请输入室内温度"
          />
          <text class="unit">°C</text>
        </view>
      </view>

      <!-- 室外温度显示 -->
      <view class="form-item">
        <view class="label">室外温度</view>
        <view class="outdoor-temp">
          {{outdoorTemp}}°C
          <text class="update-time red">{{updateTime}}</text>
        </view>
      </view>

    </view>


<!-- <view class="upload-actions" wx:if="{{form.images.length < 4}}">
              <view class="upload-btn" bindtap="chooseFromAlbum">
                <text class="wx-icon">📁</text>
                <text class="upload-text">相册</text>
              </view>
              <view class="upload-btn" bindtap="takePhoto">
                <text class="wx-icon">📷</text>
                <text class="upload-text">拍照</text>
              </view>
            </view> -->
   

    <!-- 照片上传 -->
    <view class="photo-section">
      <view class="section-title">现场照片<text class="required"></text></view>
      <view class="photo-buttons">
        <view class="photo-btn" bindtap="takePhoto">
          <text class="wx-icon">📷</text>
          <text class="upload-text">拍照</text>
        </view>
        <view class="photo-btn" bindtap="chooseFromAlbum">
          <text class="wx-icon">📁</text>
          <text class="upload-text">相册</text>
        </view>
      </view>
      
      <view class="photo-preview" wx:if="{{tempFilePath}}">
        <image src="{{tempFilePath}}" mode="aspectFit"></image>
        <view class="delete-btn" bindtap="deletePhoto">
          <image src="/images/delete.png"></image>
        </view>
      </view>
      <!-- 显示已选择的图片 -->
      <view class="photo-preview" wx:if="{{images}}">
        <image src="{{images}}" mode="aspectFill"></image>
        <view class="delete-btn" bindtap="deleteImage">×</view>
      </view>
    </view>

    <!-- 位置信息 -->
    <view class="form-item">
      <view class="label">当前位置</view>
      <view class="location-box">
        <view class="location-info" wx:if="{{location}}">
          <view class="coordinates">
            经度：{{location.longitude}}
            纬度：{{location.latitude}}
          </view>
        </view>
        <view class="placeholder" wx:else>正在获取位置...</view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-item">
      <view class="label">备注说明</view>
      <textarea 
        class="remark"
        value="{{remark}}"
        bindinput="onRemarkInput"
        placeholder="请输入备注说明"
        maxlength="200"
      ></textarea>
      <view class="word-count">{{remark.length}}/200</view>
    </view>
  </view>

  <view class="submit-btn">
    <button 
      type="primary" 
      bindtap="handleSubmit"
      loading="{{submitting}}"
      disabled="{{submitting}}"
    >
      提交
    </button>
  </view>
</view> 