App({
  onLaunch() {
    // 检查登录状态和绑定状态
    this.checkLoginAndBinding();
  },

  // 检查登录状态和绑定状态
  checkLoginAndBinding() {
    const token = wx.getStorageSync('token');
    if (!token) {
      // 未登录，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/index'
      });
      return false;
    }

    // 已登录，检查是否绑定户号
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId || userInfo.houseId <= 0) {
      // 未绑定户号，跳转到绑定页
      wx.reLaunch({
        url: '/pages/bind/index'
      });
      return false;
    }

    // 已登录且已绑定，正常使用
    return true;
  },

  // 页面跳转拦截
  interceptNavigation() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId || userInfo.houseId <= 0) {
      wx.showModal({
        title: '温馨提示',
        content: '请先完成户号绑定才能使用系统功能',
        showCancel: false,
        confirmText: '去绑定',
        success: () => {
          wx.reLaunch({
            url: '/pages/bind/index'
          });
        }
      });
      return false;
    }
    return true;
  }
}); 
