# 查看账单详情接口实现说明

## 概述

根据 `缴费小程序/查看账单.txt` 的业务逻辑要求，重新实现了 `BillServiceImpl.viewBillDetail()` 方法，支持以下四种场景：

## 实现的业务场景

### 场景1：正常供暖（无停供申请或申请未获批）
- **判断条件**：`t_stop_supply_apply.status != 'approved'`
- **处理逻辑**：按标准账单流程处理
- **实现方法**：`buildNormalBillInfo()`
- **特点**：
  - 显示完整的账单金额信息
  - 包含滞纳金计算和显示
  - 支持正常的缴费流程

### 场景2：在供暖开始前申请停供并获批
- **判断条件**：`t_stop_supply_apply.status = 'approved'` 且 `stop_start_date <= 供暖开始日期`
- **处理逻辑**：账单金额已按 `min_payment_rate` 折扣
- **实现方法**：`buildPreHeatingStopBillInfo()`
- **特点**：
  - 显示已打折的账单金额（如30%）
  - 添加停供说明备注
  - 正常缴费流程

### 场景3：在供暖开始后申请停供并获批（此前未缴费）
- **判断条件**：`t_stop_supply_apply.status = 'approved'` 且 `stop_start_date > 供暖开始日期` 且未全额缴费
- **处理逻辑**：计算结算款，免除滞纳金
- **实现方法**：`buildPostHeatingStopUnpaidBillInfo()`
- **特点**：
  - 计算实际应缴的结算金额
  - 显示详细的结算说明
  - 状态标记为"已调整"

### 场景4：在供暖开始后申请停供并获批（此前已全额缴费）
- **判断条件**：同场景3，且 `t_bill.paid_amount >= t_bill.total_amount`
- **处理逻辑**：计算退费金额
- **实现方法**：`buildPostHeatingStopFullPaidBillInfo()`
- **特点**：
  - 计算应退金额
  - 显示退费申请说明
  - 状态标记为"已缴清"

## 核心计算逻辑

### 结算金额计算 (`calculateSettlementAmount`)

按照业务文档的计算逻辑：

1. **获取基础数据**：
   - 总金额 (total_amount)：从 t_bill 表获取
   - 总供暖天数：从供暖开始日期到结束日期
   - 实际供暖天数：从供暖开始到停供生效日期
   - 最低缴费比例 (min_payment_rate)：从规则表获取

2. **计算按天折算费用**：
   ```
   每天费用 = 总金额 / 总供暖天数
   按天折算费用 = 每天费用 * 实际供暖天数
   ```

3. **计算最低基础热损费**：
   ```
   最低基础费 = 总金额 * min_payment_rate
   ```

4. **确定最终结算金额**：
   ```
   最终结算金额 = MAX(按天折算费用, 最低基础费)
   ```

## 数据库查询优化

### 新增的查询方法

1. **StopSupplyApplyRepository**：
   - `findByHouseIdAndHeatingYearAndStatus()` - 查询指定房屋和年度的已批准停供申请

2. **使用现有的查询方法**：
   - `THeatingFeeRuleRepository.findCurrentActiveRule()` - 获取当前有效的供暖规则
   - `TBillRepository.findByHouseIdAndHeatYear()` - 查询账单
   - `TOverdueRecordRepository.findByBillId()` - 查询逾期记录

## 接口响应结构

### 基本信息 (BasicInfo)
- 户号、地址、面积、供暖年度、供暖状态

### 账单信息 (BillInfo)
- 账单ID、单价、应缴金额、已缴金额、剩余金额
- 缴费截止日期、最后缴费日期
- 账单状态、状态文本、备注信息

### 缴费记录 (PaymentRecords)
- 历史缴费记录列表

### 逾期信息 (OverdueInfo)
- 逾期天数、欠费金额、滞纳金等

## 日志记录

实现中添加了详细的日志记录：
- 场景判断日志
- 计算过程日志
- 关键数据日志
- 错误处理日志

## 错误处理

- 参数验证：房屋ID、用户权限验证
- 数据存在性检查：房屋信息、账单信息
- 业务逻辑异常处理
- 统一的异常返回格式

## 测试建议

### 测试数据准备
1. 创建测试房屋和用户数据
2. 创建不同状态的账单数据
3. 创建不同时间的停供申请数据
4. 创建供暖规则数据

### 测试用例
1. **场景1测试**：正常账单，无停供申请
2. **场景2测试**：供暖前停供申请
3. **场景3测试**：供暖后停供申请，未缴费
4. **场景4测试**：供暖后停供申请，已缴费
5. **边界测试**：无账单、无规则等异常情况

### 验证要点
- 金额计算准确性
- 状态显示正确性
- 备注信息完整性
- 日期格式正确性
- 异常处理有效性

## 部署注意事项

1. 确保数据库表结构完整
2. 检查外键关联关系
3. 验证索引性能
4. 测试并发访问
5. 监控接口性能
