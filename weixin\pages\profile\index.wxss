.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #4CAF50, #2196F3);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.25);
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.avatar-box {
  margin-right: 40rpx;
  position: relative;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
  color: #fff;
}

.name {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.role {
  font-size: 28rpx;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 菜单列表 */
.menu-list {
  margin-bottom: 50rpx;
}

.menu-group {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s ease;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.fault-icon {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.bill-icon {
  background: linear-gradient(135deg, #2196f3, #1976d2);
}

.temp-icon {
  background: linear-gradient(135deg, #e91e63, #c2185b);
}

.service-icon {
  background: linear-gradient(135deg, #00bcd4, #0097a7);
}

.settings-icon {
  background: linear-gradient(135deg, #9c27b0, #7b1fa2);
}

.about-icon {
  background: linear-gradient(135deg, #607d8b, #455a64);
}

.menu-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  font-size: 28rpx;
  color: #bbb;
  font-weight: 300;
}

/* 退出登录 */
.logout-section {
  padding: 0 20rpx;
}

.logout-btn {
  width: 100%;
  height: 96rpx;
  line-height: 60rpx;
  background: linear-gradient(135deg, #ff5722, #d84315);
  color: #fff;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 6rpx 24rpx rgba(255, 87, 34, 0.3);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.logout-btn:active::before {
  left: 100%;
}

.logout-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 87, 34, 0.4);
}
