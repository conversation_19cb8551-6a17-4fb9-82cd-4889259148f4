{"compileType": "miniprogram", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "libVersion": "3.9.0", "packOptions": {"ignore": [], "include": []}, "appid": "wx501f260109cf76e8"}