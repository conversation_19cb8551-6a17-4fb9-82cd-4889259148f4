Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#4CAF50",
    list: [{
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "icon-home",
      selectedIcon: "icon-home-fill"
    }, {
      pagePath: "/pages/profile/index",
      text: "我的",
      icon: "icon-user",
      selectedIcon: "icon-user-fill"
    }]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    }
  }
}); 