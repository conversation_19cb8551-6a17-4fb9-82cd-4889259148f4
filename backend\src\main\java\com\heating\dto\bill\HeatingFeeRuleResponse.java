package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class HeatingFeeRuleResponse {
    private Long id;
    private BigDecimal unitPrice;
    private String heatingStartDate;
    private String heatingEndDate;
    private BigDecimal minPaymentRate;
    private BigDecimal penaltyRate;
    private Boolean isActive;
    private String createdAt;
    private String updatedAt;
}