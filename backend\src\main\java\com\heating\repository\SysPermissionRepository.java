package com.heating.repository;

import com.heating.entity.permission.TSysPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统权限数据访问接口
 */
@Repository
public interface SysPermissionRepository extends JpaRepository<TSysPermission, Long> {
    
    /**
     * 查询所有系统权限并按ID排序
     * @return 权限列表
     */
    List<TSysPermission> findAllByOrderById();
} 